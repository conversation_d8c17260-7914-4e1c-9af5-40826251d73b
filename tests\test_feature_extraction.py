"""
Test Suite for Feature Extraction Module
=======================================

Comprehensive tests for the feature extraction and selection components.

Author: Dr. <PERSON>
"""

import unittest
import numpy as np
import sys
import os

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from feature_extraction import (
    TextureFeatures, ShapeFeatures, StatisticalFeatures,
    FeatureExtractor, FeatureSelector, FeatureAnalyzer
)


class TestTextureFeatures(unittest.TestCase):
    """Test texture feature extraction."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.texture_extractor = TextureFeatures()
        self.image = np.random.rand(50, 50) * 255
        self.mask = np.ones((50, 50), dtype=np.uint8)
        
    def test_glcm_features(self):
        """Test GLCM feature extraction."""
        features = self.texture_extractor.extract_glcm_features(self.image, self.mask)
        
        # Check that features are extracted
        self.assertIsInstance(features, dict)
        self.assertGreater(len(features), 0)
        
        # Check specific features
        expected_features = ['glcm_contrast_mean', 'glcm_dissimilarity_mean', 
                           'glcm_homogeneity_mean', 'glcm_energy_mean']
        for feature in expected_features:
            self.assertIn(feature, features)
            self.assertIsInstance(features[feature], (int, float))
            self.assertFalse(np.isnan(features[feature]))
    
    def test_lbp_features(self):
        """Test LBP feature extraction."""
        features = self.texture_extractor.extract_lbp_features(self.image, self.mask)
        
        # Check that features are extracted
        self.assertIsInstance(features, dict)
        self.assertGreater(len(features), 0)
        
        # Check specific features
        expected_features = ['lbp_uniformity', 'lbp_entropy', 'lbp_mean', 'lbp_std']
        for feature in expected_features:
            self.assertIn(feature, features)
            self.assertIsInstance(features[feature], (int, float))
            self.assertFalse(np.isnan(features[feature]))
    
    def test_edge_features(self):
        """Test edge feature extraction."""
        features = self.texture_extractor.extract_edge_features(self.image, self.mask)
        
        # Check that features are extracted
        self.assertIsInstance(features, dict)
        self.assertGreater(len(features), 0)
        
        # Check specific features
        expected_features = ['edge_roberts_mean', 'edge_sobel_mean', 
                           'edge_scharr_mean', 'edge_prewitt_mean']
        for feature in expected_features:
            self.assertIn(feature, features)
            self.assertIsInstance(features[feature], (int, float))
            self.assertFalse(np.isnan(features[feature]))


class TestShapeFeatures(unittest.TestCase):
    """Test shape feature extraction."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.shape_extractor = ShapeFeatures()
        # Create a simple circular mask
        self.mask = np.zeros((50, 50), dtype=np.uint8)
        y, x = np.ogrid[:50, :50]
        center = (25, 25)
        radius = 15
        mask_circle = (x - center[0])**2 + (y - center[1])**2 <= radius**2
        self.mask[mask_circle] = 1
        
    def test_basic_shape_features(self):
        """Test basic shape feature extraction."""
        features = self.shape_extractor.extract_basic_shape_features(self.mask)
        
        # Check that features are extracted
        self.assertIsInstance(features, dict)
        self.assertGreater(len(features), 0)
        
        # Check specific features
        expected_features = ['area', 'perimeter', 'circularity', 
                           'compactness', 'extent', 'solidity']
        for feature in expected_features:
            self.assertIn(feature, features)
            self.assertIsInstance(features[feature], (int, float))
            self.assertFalse(np.isnan(features[feature]))
        
        # Check that area is positive
        self.assertGreater(features['area'], 0)
        
        # Check that circularity is between 0 and 1
        self.assertGreaterEqual(features['circularity'], 0)
        self.assertLessEqual(features['circularity'], 1)
    
    def test_advanced_shape_features(self):
        """Test advanced shape feature extraction."""
        features = self.shape_extractor.extract_advanced_shape_features(self.mask)
        
        # Check that features are extracted
        self.assertIsInstance(features, dict)
        self.assertGreater(len(features), 0)
        
        # Check specific features
        expected_features = ['eccentricity', 'major_axis_length', 
                           'minor_axis_length', 'orientation']
        for feature in expected_features:
            self.assertIn(feature, features)
            self.assertIsInstance(features[feature], (int, float))
            self.assertFalse(np.isnan(features[feature]))
        
        # Check Hu moments
        hu_features = [f'hu_moment_{i+1}' for i in range(7)]
        for feature in hu_features:
            self.assertIn(feature, features)
            self.assertIsInstance(features[feature], (int, float))


class TestStatisticalFeatures(unittest.TestCase):
    """Test statistical feature extraction."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.stats_extractor = StatisticalFeatures()
        self.image = np.random.rand(50, 50) * 255
        self.mask = np.ones((50, 50), dtype=np.uint8)
        
    def test_first_order_statistics(self):
        """Test first-order statistical features."""
        features = self.stats_extractor.extract_first_order_statistics(self.image, self.mask)
        
        # Check that features are extracted
        self.assertIsInstance(features, dict)
        self.assertGreater(len(features), 0)
        
        # Check specific features
        expected_features = ['mean', 'std', 'variance', 'median', 
                           'skewness', 'kurtosis', 'entropy']
        for feature in expected_features:
            self.assertIn(feature, features)
            self.assertIsInstance(features[feature], (int, float))
            self.assertFalse(np.isnan(features[feature]))
        
        # Check that variance is non-negative
        self.assertGreaterEqual(features['variance'], 0)
        
        # Check that standard deviation is non-negative
        self.assertGreaterEqual(features['std'], 0)
    
    def test_second_order_statistics(self):
        """Test second-order statistical features."""
        features = self.stats_extractor.extract_second_order_statistics(self.image, self.mask)
        
        # Check that features are extracted
        self.assertIsInstance(features, dict)
        self.assertGreater(len(features), 0)
        
        # Check specific features
        expected_features = ['gradient_mean', 'gradient_std', 
                           'laplacian_mean', 'laplacian_std']
        for feature in expected_features:
            self.assertIn(feature, features)
            self.assertIsInstance(features[feature], (int, float))
            self.assertFalse(np.isnan(features[feature]))


class TestFeatureExtractor(unittest.TestCase):
    """Test the main feature extractor class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.extractor = FeatureExtractor()
        self.image = np.random.rand(50, 50) * 255
        self.mask = np.ones((50, 50), dtype=np.uint8)
        
    def test_extract_all_features(self):
        """Test extraction of all feature types."""
        features = self.extractor.extract_all_features(self.image, self.mask)
        
        # Check that features are extracted
        self.assertIsInstance(features, dict)
        self.assertGreater(len(features), 0)
        
        # Check that all feature types are present
        feature_types = ['glcm', 'lbp', 'edge', 'area', 'mean', 'std']
        for feature_type in feature_types:
            found = any(feature_type in name for name in features.keys())
            self.assertTrue(found, f"No {feature_type} features found")
    
    def test_batch_processing(self):
        """Test batch feature extraction."""
        images = [self.image, self.image, self.image]
        masks = [self.mask, self.mask, self.mask]
        
        features_list = self.extractor.extract_features_batch(images, masks)
        
        # Check that features are extracted for all images
        self.assertEqual(len(features_list), 3)
        
        # Check that each feature dict has the same keys
        keys1 = set(features_list[0].keys())
        keys2 = set(features_list[1].keys())
        keys3 = set(features_list[2].keys())
        
        self.assertEqual(keys1, keys2)
        self.assertEqual(keys2, keys3)


class TestFeatureSelector(unittest.TestCase):
    """Test feature selection methods."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.selector = FeatureSelector()
        
        # Create synthetic data
        np.random.seed(42)
        self.n_samples = 100
        self.n_features = 50
        
        # Create some informative features
        X_informative = np.random.randn(self.n_samples, 10)
        
        # Create some noise features
        X_noise = np.random.randn(self.n_samples, self.n_features - 10)
        
        self.X = np.column_stack([X_informative, X_noise])
        
        # Create labels correlated with informative features
        self.y = (np.sum(X_informative[:, :5], axis=1) > 0).astype(int)
        
    def test_univariate_selection(self):
        """Test univariate feature selection."""
        X_selected, selected_indices = self.selector.select_features_univariate(
            self.X, self.y, k=10)
        
        # Check output format
        self.assertEqual(X_selected.shape[1], 10)
        self.assertEqual(len(selected_indices), 10)
        self.assertEqual(X_selected.shape[0], self.n_samples)
        
        # Check that indices are valid
        for idx in selected_indices:
            self.assertGreaterEqual(idx, 0)
            self.assertLess(idx, self.n_features)
    
    def test_rfe_selection(self):
        """Test RFE feature selection."""
        X_selected, selected_indices = self.selector.select_features_rfe(
            self.X, self.y, n_features=10)
        
        # Check output format
        self.assertEqual(X_selected.shape[1], 10)
        self.assertEqual(len(selected_indices), 10)
        self.assertEqual(X_selected.shape[0], self.n_samples)
        
        # Check that indices are valid
        for idx in selected_indices:
            self.assertGreaterEqual(idx, 0)
            self.assertLess(idx, self.n_features)
    
    def test_importance_selection(self):
        """Test importance-based feature selection."""
        X_selected, selected_indices = self.selector.select_features_importance(
            self.X, self.y, threshold=0.01)
        
        # Check output format
        self.assertGreater(X_selected.shape[1], 0)
        self.assertEqual(len(selected_indices), X_selected.shape[1])
        self.assertEqual(X_selected.shape[0], self.n_samples)
        
        # Check that indices are valid
        for idx in selected_indices:
            self.assertGreaterEqual(idx, 0)
            self.assertLess(idx, self.n_features)
    
    def test_feature_ranking(self):
        """Test feature ranking functionality."""
        # First run a selection method
        self.selector.select_features_univariate(self.X, self.y, k=10)
        
        # Get feature ranking
        feature_names = [f'feature_{i}' for i in range(self.n_features)]
        ranking = self.selector.get_feature_ranking(feature_names)
        
        # Check output format
        self.assertEqual(len(ranking), self.n_features)
        
        # Check that ranking is sorted
        scores = [score for _, score in ranking]
        self.assertEqual(scores, sorted(scores, reverse=True))


class TestFeatureAnalyzer(unittest.TestCase):
    """Test feature analysis utilities."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.analyzer = FeatureAnalyzer()
        
        # Create synthetic feature data
        np.random.seed(42)
        self.n_samples = 100
        self.features_dict = {
            'feature1': np.random.normal(0, 1, self.n_samples),
            'feature2': np.random.exponential(1, self.n_samples),
            'feature3': np.random.uniform(0, 1, self.n_samples)
        }
        self.labels = np.random.randint(0, 2, self.n_samples)
        
    def test_feature_distribution_analysis(self):
        """Test feature distribution analysis."""
        analysis = self.analyzer.analyze_feature_distribution(
            self.features_dict, 'feature1')
        
        # Check that analysis contains expected statistics
        expected_stats = ['mean', 'std', 'min', 'max', 'median', 
                         'skewness', 'kurtosis']
        for stat in expected_stats:
            self.assertIn(stat, analysis)
            self.assertIsInstance(analysis[stat], (int, float))
            self.assertFalse(np.isnan(analysis[stat]))
    
    def test_feature_group_comparison(self):
        """Test feature comparison between groups."""
        comparison = self.analyzer.compare_feature_groups(
            self.features_dict, self.labels)
        
        # Check that comparison contains all features
        for feature_name in self.features_dict.keys():
            self.assertIn(feature_name, comparison)
            
            # Check that each group is represented
            for group_stats in comparison[feature_name].values():
                self.assertIn('mean', group_stats)
                self.assertIn('std', group_stats)
                self.assertIn('count', group_stats)


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete pipeline."""
    
    def setUp(self):
        """Set up test fixtures."""
        np.random.seed(42)
        
        # Create synthetic MRI-like data
        self.n_samples = 50
        self.image_size = (32, 32)
        
        self.images = []
        self.masks = []
        self.labels = []
        
        for i in range(self.n_samples):
            # Create image
            image = np.random.rand(*self.image_size) * 255
            
            # Create mask
            mask = np.zeros(self.image_size, dtype=np.uint8)
            center = (16, 16)
            radius = np.random.randint(5, 10)
            y, x = np.ogrid[:self.image_size[0], :self.image_size[1]]
            mask_circle = (x - center[0])**2 + (y - center[1])**2 <= radius**2
            mask[mask_circle] = 1
            
            # Create label
            label = np.random.randint(0, 2)
            
            self.images.append(image)
            self.masks.append(mask)
            self.labels.append(label)
    
    def test_complete_pipeline(self):
        """Test the complete feature extraction and selection pipeline."""
        # 1. Feature extraction
        extractor = FeatureExtractor()
        features_list = extractor.extract_features_batch(self.images, self.masks)
        
        self.assertEqual(len(features_list), self.n_samples)
        
        # 2. Convert to matrix format
        feature_names = list(features_list[0].keys())
        X = np.array([[features.get(name, 0) for name in feature_names] 
                      for features in features_list])
        X = np.nan_to_num(X)  # Handle NaN values
        
        self.assertEqual(X.shape[0], self.n_samples)
        self.assertGreater(X.shape[1], 0)
        
        # 3. Feature selection
        selector = FeatureSelector()
        X_selected, selected_indices = selector.select_features_univariate(
            X, self.labels, k=min(10, X.shape[1]))
        
        self.assertLessEqual(X_selected.shape[1], X.shape[1])
        self.assertEqual(X_selected.shape[0], self.n_samples)
        
        # 4. Feature analysis
        analyzer = FeatureAnalyzer()
        features_dict = {name: [features.get(name, 0) for features in features_list] 
                        for name in feature_names}
        
        # Test analysis on a sample feature
        if len(feature_names) > 0:
            analysis = analyzer.analyze_feature_distribution(
                features_dict, feature_names[0])
            self.assertIsInstance(analysis, dict)
            self.assertGreater(len(analysis), 0)
    
    def test_robustness_to_empty_masks(self):
        """Test robustness to empty masks."""
        extractor = FeatureExtractor()
        
        # Create image with empty mask
        image = np.random.rand(32, 32) * 255
        empty_mask = np.zeros((32, 32), dtype=np.uint8)
        
        # This should not crash
        try:
            features = extractor.extract_all_features(image, empty_mask)
            # Features should be extracted even with empty mask
            self.assertIsInstance(features, dict)
        except Exception as e:
            self.fail(f"Feature extraction failed with empty mask: {e}")
    
    def test_robustness_to_single_value_images(self):
        """Test robustness to single-value images."""
        extractor = FeatureExtractor()
        
        # Create image with single value
        image = np.ones((32, 32)) * 128
        mask = np.ones((32, 32), dtype=np.uint8)
        
        # This should not crash
        try:
            features = extractor.extract_all_features(image, mask)
            self.assertIsInstance(features, dict)
        except Exception as e:
            self.fail(f"Feature extraction failed with single-value image: {e}")


def run_all_tests():
    """Run all tests."""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestTextureFeatures,
        TestShapeFeatures,
        TestStatisticalFeatures,
        TestFeatureExtractor,
        TestFeatureSelector,
        TestFeatureAnalyzer,
        TestIntegration
    ]
    
    for test_class in test_classes:
        test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(test_class))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result


if __name__ == '__main__':
    print("Running Feature Extraction Module Tests")
    print("=" * 50)
    
    result = run_all_tests()
    
    print("\n" + "=" * 50)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, failure in result.failures:
            print(f"- {test}: {failure}")
    
    if result.errors:
        print("\nErrors:")
        for test, error in result.errors:
            print(f"- {test}: {error}")
    
    if result.wasSuccessful():
        print("\nAll tests passed successfully! ✅")
    else:
        print("\nSome tests failed. ❌")"""
Test Suite for Feature Extraction Module
=======================================

Comprehensive tests for the feature extraction and selection components.

Author: Dr. Mohammed Yagoub Esmail
"""

import unittest
import numpy as np
import sys
import os

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from feature_extraction import (
    TextureFeatures, ShapeFeatures, StatisticalFeatures,
    FeatureExtractor, FeatureSelector, FeatureAnalyzer
)


class TestTextureFeatures(unittest.TestCase):
    """Test texture feature extraction."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.texture_extractor = TextureFeatures()
        self.image = np.random.rand(50, 50) * 255
        self.mask = np.ones((50, 50), dtype=np.uint8)
        
    def test_glcm_features(self):
        """Test GLCM feature extraction."""
        features = self.texture_extractor.extract_glcm_features(self.image, self.mask)
        
        # Check that features are extracted
        self.assertIsInstance(features, dict)
        self.assertGreater(len(features), 0)
        
        # Check specific features
        expected_features = ['glcm_contrast_mean', 'glcm_dissimilarity_mean', 
                           'glcm_homogeneity_mean', 'glcm_energy_mean']
        for feature in expected_features:
            self.assertIn(feature, features)
            self.assertIsInstance(features[feature], (int, float))
            self.assertFalse(np.isnan(features[feature]))
    
    def test_lbp_features(self):
        """Test LBP feature extraction."""
        features = self.texture_extractor.extract_lbp_features(self.image, self.mask)
        
        # Check that features are extracted
        self.assertIsInstance(features, dict)
        self.assertGreater(len(features), 0)
        
        # Check specific features
        expected_features = ['lbp_uniformity', 'lbp_entropy', 'lbp_mean', 'lbp_std']
        for feature in expected_features:
            self.assertIn(feature, features)
            self.assertIsInstance(features[feature], (int, float))
            self.assertFalse(np.isnan(features[feature]))
    
    def test_edge_features(self):
        """Test edge feature extraction."""
        features = self.texture_extractor.extract_edge_features(self.image, self.mask)
        
        # Check that features are extracted
        self.assertIsInstance(features, dict)
        self.assertGreater(len(features), 0)
        
        # Check specific features
        expected_features = ['edge_roberts_mean', 'edge_sobel_mean', 
                           'edge_scharr_mean', 'edge_prewitt_mean']
        for feature in expected_features:
            self.assertIn(feature, features)
            self.assertIsInstance(features[feature], (int, float))
            self.assertFalse(np.isnan(features[feature]))


class TestShapeFeatures(unittest.TestCase):
    """Test shape feature extraction."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.shape_extractor = ShapeFeatures()
        # Create a simple circular mask
        self.mask = np.zeros((50, 50), dtype=np.uint8)
        y, x = np.ogrid[:50, :50]
        center = (25, 25)
        radius = 15
        mask_circle = (x - center[0])**2 + (y - center[1])**2 <= radius**2
        self.mask[mask_circle] = 1
        
    def test_basic_shape_features(self):
        """Test basic shape feature extraction."""
        features = self.shape_extractor.extract_basic_shape_features(self.mask)
        
        # Check that features are extracted
        self.assertIsInstance(features, dict)
        self.assertGreater(len(features), 0)
        
        # Check specific features
        expected_features = ['area', 'perimeter', 'circularity', 
                           'compactness', 'extent', 'solidity']
        for feature in expected_features:
            self.assertIn(feature, features)
            self.assertIsInstance(features[feature], (int, float))
            self.assertFalse(np.isnan(features[feature]))
        
        # Check that area is positive
        self.assertGreater(features['area'], 0)
        
        # Check that circularity is between 0 and 1
        self.assertGreaterEqual(features['circularity'], 0)
        self.assertLessEqual(features['circularity'], 1)
    
    def test_advanced_shape_features(self):
        """Test advanced shape feature extraction."""
        features = self.shape_extractor.extract_advanced_shape_features(self.mask)
        
        # Check that features are extracted
        self.assertIsInstance(features, dict)
        self.assertGreater(len(features), 0)
        
        # Check specific features
        expected_features = ['eccentricity', 'major_axis_length', 
                           'minor_axis_length', 'orientation']
        for feature in expected_features:
            self.assertIn(feature, features)
            self.assertIsInstance(features[feature], (int, float))
            self.assertFalse(np.isnan(features[feature]))
        
        # Check Hu moments
        hu_features = [f'hu_moment_{i+1}' for i in range(7)]
        for feature in hu_features:
            self.assertIn(feature, features)
            self.assertIsInstance(features[feature], (int, float))


class TestStatisticalFeatures(unittest.TestCase):
    """Test statistical feature extraction."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.stats_extractor = StatisticalFeatures()
        self.image = np.random.rand(50, 50) * 255
        self.mask = np.ones((50, 50), dtype=np.uint8)
        
    def test_first_order_statistics(self):
        """Test first-order statistical features."""
        features = self.stats_extractor.extract_first_order_statistics(self.image, self.mask)
        
        # Check that features are extracted
        self.assertIsInstance(features, dict)
        self.assertGreater(len(features), 0)
        
        # Check specific features
        expected_features = ['mean', 'std', 'variance', 'median', 
                           'skewness', 'kurtosis', 'entropy']
        for feature in expected_features:
            self.assertIn(feature, features)
            self.assertIsInstance(features[feature], (int, float))
            self.assertFalse(np.isnan(features[feature]))
        
        # Check that variance is non-negative
        self.assertGreaterEqual(features['variance'], 0)
        
        # Check that standard deviation is non-negative
        self.assertGreaterEqual(features['std'], 0)
    
    def test_second_order_statistics(self):
        """Test second-order statistical features."""
        features = self.stats_extractor.extract_second_order_statistics(self.image, self.mask)
        
        # Check that features are extracted
        self.assertIsInstance(features, dict)
        self.assertGreater(len(features), 0)
        
        # Check specific features
        expected_features = ['gradient_mean', 'gradient_std', 
                           'laplacian_mean', 'laplacian_std']
        for feature in expected_features:
            self.assertIn(feature, features)
            self.assertIsInstance(features[feature], (int, float))
            self.assertFalse(np.isnan(features[feature]))


class TestFeatureExtractor(unittest.TestCase):
    """Test the main feature extractor class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.extractor = FeatureExtractor()
        self.image = np.random.rand(50, 50) * 255
        self.mask = np.ones((50, 50), dtype=np.uint8)
        
    def test_extract_all_features(self):
        """Test extraction of all feature types."""
        features = self.extractor.extract_all_features(self.image, self.mask)
        
        # Check that features are extracted
        self.assertIsInstance(features, dict)
        self.assertGreater(len(features), 0)
        
        # Check that all feature types are present
        feature_types = ['glcm', 'lbp', 'edge', 'area', 'mean', 'std']
        for feature_type in feature_types:
            found = any(feature_type in name for name in features.keys())
            self.assertTrue(found, f"No {feature_type} features found")
    
    def test_batch_processing(self):
        """Test batch feature extraction."""
        images = [self.image, self.image, self.image]
        masks = [self.mask, self.mask, self.mask]
        
        features_list = self.extractor.extract_features_batch(images, masks)
        
        # Check that features are extracted for all images
        self.assertEqual(len(features_list), 3)
        
        # Check that each feature dict has the same keys
        keys1 = set(features_list[0].keys())
        keys2 = set(features_list[1].keys())
        keys3 = set(features_list[2].keys())
        
        self.assertEqual(keys1, keys2)
        self.assertEqual(keys2, keys3)


class TestFeatureSelector(unittest.TestCase):
    """Test feature selection methods."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.selector = FeatureSelector()
        
        # Create synthetic data
        np.random.seed(42)
        self.n_samples = 100
        self.n_features = 50
        
        # Create some informative features
        X_informative = np.random.randn(self.n_samples, 10)
        
        # Create some noise features
        X_noise = np.random.randn(self.n_samples, self.n_features - 10)
        
        self.X = np.column_stack([X_informative, X_noise])
        
        # Create labels correlated with informative features
        self.y = (np.sum(X_informative[:, :5], axis=1) > 0).astype(int)
        
    def test_univariate_selection(self):
        """Test univariate feature selection."""
        X_selected, selected_indices = self.selector.select_features_univariate(
            self.X, self.y, k=10)
        
        # Check output format
        self.assertEqual(X_selected.shape[1], 10)
        self.assertEqual(len(selected_indices), 10)
        self.assertEqual(X_selected.shape[0], self.n_samples)
        
        # Check that indices are valid
        for idx in selected_indices:
            self.assertGreaterEqual(idx, 0)
            self.assertLess(idx, self.n_features)
    
    def test_rfe_selection(self):
        """Test RFE feature selection."""
        X_selected, selected_indices = self.selector.select_features_rfe(
            self.X, self.y, n_features=10)
        
        # Check output format
        self.assertEqual(X_selected.shape[1], 10)
        self.assertEqual(len(selected_indices), 10)
        self.assertEqual(X_selected.shape[0], self.n_samples)
        
        # Check that indices are valid
        for idx in selected_indices:
            self.assertGreaterEqual(idx, 0)
            self.assertLess(idx, self.n_features)
    
    def test_importance_selection(self):
        """Test importance-based feature selection."""
        X_selected, selected_indices = self.selector.select_features_importance(
            self.X, self.y, threshold=0.01)
        
        # Check output format
        self.assertGreater(X_selected.shape[1], 0)
        self.assertEqual(len(selected_indices), X_selected.shape[1])
        self.assertEqual(X_selected.shape[0], self.n_samples)
        
        # Check that indices are valid
        for idx in selected_indices:
            self.assertGreaterEqual(idx, 0)
            self.assertLess(idx, self.n_features)
    
    def test_feature_ranking(self):
        """Test feature ranking functionality."""
        # First run a selection method
        self.selector.select_features_univariate(self.X, self.y, k=10)
        
        # Get feature ranking
        feature_names = [f'feature_{i}' for i in range(self.n_features)]
        ranking = self.selector.get_feature_ranking(feature_names)
        
        # Check output format
        self.assertEqual(len(ranking), self.n_features)
        
        # Check that ranking is sorted
        scores = [score for _, score in ranking]
        self.assertEqual(scores, sorted(scores, reverse=True))


class TestFeatureAnalyzer(unittest.TestCase):
    """Test feature analysis utilities."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.analyzer = FeatureAnalyzer()
        
        # Create synthetic feature data
        np.random.seed(42)
        self.n_samples = 100
        self.features_dict = {
            'feature1': np.random.normal(0, 1, self.n_samples),
            'feature2': np.random.exponential(1, self.n_samples),
            'feature3': np.random.uniform(0, 1, self.n_samples)
        }
        self.labels = np.random.randint(0, 2, self.n_samples)
        
    def test_feature_distribution_analysis(self):
        """Test feature distribution analysis."""
        analysis = self.analyzer.analyze_feature_distribution(
            self.features_dict, 'feature1')
        
        # Check that analysis contains expected statistics
        expected_stats = ['mean', 'std', 'min', 'max', 'median', 
                         'skewness', 'kurtosis']
        for stat in expected_stats:
            self.assertIn(stat, analysis)
            self.assertIsInstance(analysis[stat], (int, float))
            self.assertFalse(np.isnan(analysis[stat]))
    
    def test_feature_group_comparison(self):
        """Test feature comparison between groups."""
        comparison = self.analyzer.compare_feature_groups(
            self.features_dict, self.labels)
        
        # Check that comparison contains all features
        for feature_name in self.features_dict.keys():
            self.assertIn(feature_name, comparison)
            
            # Check that each group is represented
            for group_stats in comparison[feature_name].values():
                self.assertIn('mean', group_stats)
                self.assertIn('std', group_stats)
                self.assertIn('count', group_stats)


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete pipeline."""
    
    def setUp(self):
        """Set up test fixtures."""
        np.random.seed(42)
        
        # Create synthetic MRI-like data
        self.n_samples = 50
        self.image_size = (32, 32)
        
        self.images = []
        self.masks = []
        self.labels = []
        
        for i in range(self.n_samples):
            # Create image
            image = np.random.rand(*self.image_size) * 255
            
            # Create mask
            mask = np.zeros(self.image_size, dtype=np.uint8)
            center = (16, 16)
            radius = np.random.randint(5, 10)
            y, x = np.ogrid[:self.image_size[0], :self.image_size[1]]
            mask_circle = (x - center[0])**2 + (y - center[1])**2 <= radius**2
            mask[mask_circle] = 1
            
            # Create label
            label = np.random.randint(0, 2)
            
            self.images.append(image)
            self.masks.append(mask)
            self.labels.append(label)
    
    def test_complete_pipeline(self):
        """Test the complete feature extraction and selection pipeline."""
        # 1. Feature extraction
        extractor = FeatureExtractor()
        features_list = extractor.extract_features_batch(self.images, self.masks)
        
        self.assertEqual(len(features_list), self.n_samples)
        
        # 2. Convert to matrix format
        feature_names = list(features_list[0].keys())
        X = np.array([[features.get(name, 0) for name in feature_names] 
                      for features in features_list])
        X = np.nan_to_num(X)  # Handle NaN values
        
        self.assertEqual(X.shape[0], self.n_samples)
        self.assertGreater(X.shape[1], 0)
        
        # 3. Feature selection
        selector = FeatureSelector()
        X_selected, selected_indices = selector.select_features_univariate(
            X, self.labels, k=min(10, X.shape[1]))
        
        self.assertLessEqual(X_selected.shape[1], X.shape[1])
        self.assertEqual(X_selected.shape[0], self.n_samples)
        
        # 4. Feature analysis
        analyzer = FeatureAnalyzer()
        features_dict = {name: [features.get(name, 0) for features in features_list] 
                        for name in feature_names}
        
        # Test analysis on a sample feature
        if len(feature_names) > 0:
            analysis = analyzer.analyze_feature_distribution(
                features_dict, feature_names[0])
            self.assertIsInstance(analysis, dict)
            self.assertGreater(len(analysis), 0)
    
    def test_robustness_to_empty_masks(self):
        """Test robustness to empty masks."""
        extractor = FeatureExtractor()
        
        # Create image with empty mask
        image = np.random.rand(32, 32) * 255
        empty_mask = np.zeros((32, 32), dtype=np.uint8)
        
        # This should not crash
        try:
            features = extractor.extract_all_features(image, empty_mask)
            # Features should be extracted even with empty mask
            self.assertIsInstance(features, dict)
        except Exception as e:
            self.fail(f"Feature extraction failed with empty mask: {e}")
    
    def test_robustness_to_single_value_images(self):
        """Test robustness to single-value images."""
        extractor = FeatureExtractor()
        
        # Create image with single value
        image = np.ones((32, 32)) * 128
        mask = np.ones((32, 32), dtype=np.uint8)
        
        # This should not crash
        try:
            features = extractor.extract_all_features(image, mask)
            self.assertIsInstance(features, dict)
        except Exception as e:
            self.fail(f"Feature extraction failed with single-value image: {e}")


def run_all_tests():
    """Run all tests."""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestTextureFeatures,
        TestShapeFeatures,
        TestStatisticalFeatures,
        TestFeatureExtractor,
        TestFeatureSelector,
        TestFeatureAnalyzer,
        TestIntegration
    ]
    
    for test_class in test_classes:
        test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(test_class))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result


if __name__ == '__main__':
    print("Running Feature Extraction Module Tests")
    print("=" * 50)
    
    result = run_all_tests()
    
    print("\n" + "=" * 50)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, failure in result.failures:
            print(f"- {test}: {failure}")
    
    if result.errors:
        print("\nErrors:")
        for test, error in result.errors:
            print(f"- {test}: {error}")
    
    if result.wasSuccessful():
        print("\nAll tests passed successfully! ✅")
    else:
        print("\nSome tests failed. ❌")