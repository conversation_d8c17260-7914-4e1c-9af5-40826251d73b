import React from 'react';

const DicomTab = ({ analysisResults }) => {
  const getDicomInfo = () => {
    if (analysisResults?.processing_stages?.dicom) {
      return analysisResults.processing_stages.dicom;
    }
    return {
      series_info: {
        slice_count: 0,
        image_size: [256, 256],
        data_type: 'DICOM'
      },
      metadata: {
        patient_id: 'Not loaded',
        modality: 'MRI',
        series_description: 'Brain MRI'
      }
    };
  };

  const dicomInfo = getDicomInfo();

  return (
    <div className="dicom-tab">
      <div className="detail-header">
        <i className="fas fa-database detail-icon"></i>
        <div>
          <h3 className="detail-title">1. DICOM Loading & Processing</h3>
          <p className="detail-subtitle">
            Load, organize, and process DICOM series from medical imaging devices
          </p>
        </div>
      </div>

      <div className="dicom-info-grid">
        <div className="info-card">
          <div className="info-header">
            <i className="fas fa-info-circle"></i>
            <h4>Series Information</h4>
          </div>
          <div className="info-content">
            <div className="info-item">
              <span className="info-label">Slice Count:</span>
              <span className="info-value">{dicomInfo.series_info?.slice_count || 0}</span>
            </div>
            <div className="info-item">
              <span className="info-label">Image Size:</span>
              <span className="info-value">
                {dicomInfo.series_info?.image_size ? 
                  `${dicomInfo.series_info.image_size[0]} × ${dicomInfo.series_info.image_size[1]}` : 
                  'Not loaded'}
              </span>
            </div>
            <div className="info-item">
              <span className="info-label">Data Type:</span>
              <span className="info-value">{dicomInfo.series_info?.data_type || 'Unknown'}</span>
            </div>
            <div className="info-item">
              <span className="info-label">Modality:</span>
              <span className="info-value">{dicomInfo.metadata?.modality || 'Unknown'}</span>
            </div>
          </div>
        </div>

        <div className="info-card">
          <div className="info-header">
            <i className="fas fa-user"></i>
            <h4>Patient Information</h4>
          </div>
          <div className="info-content">
            <div className="info-item">
              <span className="info-label">Patient ID:</span>
              <span className="info-value">{dicomInfo.metadata?.patient_id || 'Not loaded'}</span>
            </div>
            <div className="info-item">
              <span className="info-label">Study Date:</span>
              <span className="info-value">{dicomInfo.metadata?.study_date || 'Not loaded'}</span>
            </div>
            <div className="info-item">
              <span className="info-label">Series Description:</span>
              <span className="info-value">{dicomInfo.metadata?.series_description || 'Not loaded'}</span>
            </div>
          </div>
        </div>
      </div>

      <div className="dicom-pipeline">
        <h4>DICOM Processing Pipeline</h4>
        <div className="pipeline-steps">
          <div className="pipeline-step">
            <div className="step-icon">
              <i className="fas fa-folder-open"></i>
            </div>
            <div className="step-content">
              <h5>1. File Discovery</h5>
              <p>Recursively scan directory for DICOM files (.dcm, .dicom)</p>
            </div>
          </div>
          <div className="pipeline-arrow">
            <i className="fas fa-arrow-right"></i>
          </div>
          <div className="pipeline-step">
            <div className="step-icon">
              <i className="fas fa-file-medical"></i>
            </div>
            <div className="step-content">
              <h5>2. DICOM Parsing</h5>
              <p>Parse DICOM headers and extract metadata information</p>
            </div>
          </div>
          <div className="pipeline-arrow">
            <i className="fas fa-arrow-right"></i>
          </div>
          <div className="pipeline-step">
            <div className="step-icon">
              <i className="fas fa-sort-amount-down"></i>
            </div>
            <div className="step-content">
              <h5>3. Series Sorting</h5>
              <p>Sort slices by location and instance number</p>
            </div>
          </div>
          <div className="pipeline-arrow">
            <i className="fas fa-arrow-right"></i>
          </div>
          <div className="pipeline-step">
            <div className="step-icon">
              <i className="fas fa-cube"></i>
            </div>
            <div className="step-content">
              <h5>4. Volume Assembly</h5>
              <p>Combine slices into 3D volume array</p>
            </div>
          </div>
        </div>
      </div>

      <div className="dicom-visualization">
        <h4>DICOM Visualization</h4>
        <div className="visualization-grid">
          <div className="viz-item">
            <div className="viz-placeholder">
              <i className="fas fa-file-medical"></i>
              <span>Sample DICOM Slice</span>
            </div>
            <div className="viz-label">Axial View</div>
          </div>
          <div className="viz-item">
            <div className="viz-placeholder">
              <i className="fas fa-chart-line"></i>
              <span>Intensity Histogram</span>
            </div>
            <div className="viz-label">Pixel Distribution</div>
          </div>
          <div className="viz-item">
            <div className="viz-placeholder">
              <i className="fas fa-info"></i>
              <span>DICOM Header Info</span>
            </div>
            <div className="viz-label">Metadata Display</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DicomTab;