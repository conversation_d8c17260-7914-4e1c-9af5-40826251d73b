"""
Feature Visualization Module
===========================

This module provides comprehensive visualization tools for feature analysis
in brain tumor detection systems.

Author: Dr. <PERSON>
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.patches import Rectangle
from matplotlib.colors import LinearSegmentedColormap
import pandas as pd
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from scipy.cluster.hierarchy import dendrogram, linkage
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore')


class FeatureVisualizer:
    """
    Comprehensive feature visualization class.
    """
    
    def __init__(self, style='seaborn-v0_8', figsize=(12, 8)):
        """
        Initialize the visualizer.
        
        Args:
            style: Matplotlib style to use
            figsize: Default figure size
        """
        plt.style.use(style)
        self.figsize = figsize
        self.colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
                      '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
    
    def plot_feature_distribution(self, features_dict, feature_name, labels=None, 
                                save_path=None, bins=50):
        """
        Plot distribution of a single feature.
        
        Args:
            features_dict: Dictionary containing feature values
            feature_name: Name of the feature to plot
            labels: Optional labels for different groups
            save_path: Path to save the plot
            bins: Number of histogram bins
        """
        if feature_name not in features_dict:
            print(f"Feature '{feature_name}' not found in features_dict")
            return
        
        plt.figure(figsize=self.figsize)
        
        values = np.array(features_dict[feature_name])
        
        if labels is None:
            # Single distribution
            plt.hist(values, bins=bins, alpha=0.7, color=self.colors[0], 
                    edgecolor='black', linewidth=0.5)
            plt.title(f'Distribution of {feature_name}')
        else:
            # Multiple distributions
            unique_labels = np.unique(labels)
            for i, label in enumerate(unique_labels):
                mask = np.array(labels) == label
                group_values = values[mask]
                label_name = f'Tumor' if label == 1 else f'Normal' if label == 0 else f'Group {label}'
                plt.hist(group_values, bins=bins, alpha=0.6, 
                        color=self.colors[i % len(self.colors)], 
                        label=label_name, edgecolor='black', linewidth=0.5)
            plt.legend()
            plt.title(f'Distribution of {feature_name} by Group')
        
        plt.xlabel(feature_name)
        plt.ylabel('Frequency')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def plot_feature_comparison(self, features_dict, feature_names, labels, 
                              save_path=None):
        """
        Compare multiple features across different groups.
        
        Args:
            features_dict: Dictionary containing feature values
            feature_names: List of feature names to compare
            labels: Labels for different groups
            save_path: Path to save the plot
        """
        n_features = len(feature_names)
        n_cols = min(3, n_features)
        n_rows = (n_features + n_cols - 1) // n_cols
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(5*n_cols, 4*n_rows))
        if n_features == 1:
            axes = [axes]
        elif n_rows == 1:
            axes = axes.reshape(1, -1)
        
        for i, feature_name in enumerate(feature_names):
            row = i // n_cols
            col = i % n_cols
            ax = axes[row, col] if n_rows > 1 else axes[col]
            
            if feature_name in features_dict:
                values = np.array(features_dict[feature_name])
                
                # Create dataframe for seaborn
                df = pd.DataFrame({
                    'value': values,
                    'group': ['Tumor' if l == 1 else 'Normal' for l in labels]
                })
                
                # Box plot
                sns.boxplot(data=df, x='group', y='value', ax=ax)
                ax.set_title(f'{feature_name}')
                ax.set_xlabel('Group')
                ax.set_ylabel('Value')
            else:
                ax.text(0.5, 0.5, f'Feature\n{feature_name}\nnot found', 
                       ha='center', va='center', transform=ax.transAxes)
        
        # Remove empty subplots
        for i in range(n_features, n_rows * n_cols):
            row = i // n_cols
            col = i % n_cols
            ax = axes[row, col] if n_rows > 1 else axes[col]
            ax.remove()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def plot_correlation_matrix(self, features_dict, feature_names=None, 
                               save_path=None, method='pearson'):
        """
        Plot correlation matrix of features.
        
        Args:
            features_dict: Dictionary containing feature values
            feature_names: List of feature names to include
            save_path: Path to save the plot
            method: Correlation method ('pearson', 'spearman', 'kendall')
        """
        if feature_names is None:
            feature_names = list(features_dict.keys())
        
        # Create feature matrix
        feature_matrix = []
        valid_features = []
        
        for name in feature_names:
            if name in features_dict:
                values = np.array(features_dict[name])
                if not np.all(np.isnan(values)) and np.var(values) > 0:
                    feature_matrix.append(values)
                    valid_features.append(name)
        
        if len(feature_matrix) < 2:
            print("Need at least 2 valid features for correlation matrix")
            return
        
        feature_matrix = np.array(feature_matrix).T
        df = pd.DataFrame(feature_matrix, columns=valid_features)
        
        # Calculate correlation
        corr_matrix = df.corr(method=method)
        
        # Plot
        plt.figure(figsize=(max(10, len(valid_features)//2), max(8, len(valid_features)//2)))
        
        # Create custom colormap
        colors = ['#d73027', '#f46d43', '#fdae61', '#fee08b', '#e6f598', 
                 '#abdda4', '#66c2a5', '#3288bd', '#5e4fa2']
        cmap = LinearSegmentedColormap.from_list('custom', colors, N=256)
        
        mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
        sns.heatmap(corr_matrix, mask=mask, annot=True, fmt='.2f', 
                   cmap=cmap, center=0, square=True, linewidths=0.5,
                   cbar_kws={"shrink": .8})
        
        plt.title(f'Feature Correlation Matrix ({method.capitalize()})')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def plot_feature_importance(self, feature_names, importance_scores, 
                              top_n=20, save_path=None):
        """
        Plot feature importance scores.
        
        Args:
            feature_names: List of feature names
            importance_scores: Corresponding importance scores
            top_n: Number of top features to show
            save_path: Path to save the plot
        """
        # Sort by importance
        sorted_indices = np.argsort(importance_scores)[::-1]
        
        # Get top N features
        top_indices = sorted_indices[:top_n]
        top_names = [feature_names[i] for i in top_indices]
        top_scores = [importance_scores[i] for i in top_indices]
        
        # Create plot
        plt.figure(figsize=(12, max(8, top_n//3)))
        
        # Horizontal bar plot
        bars = plt.barh(range(len(top_names)), top_scores, 
                       color=plt.cm.viridis(np.linspace(0, 1, len(top_names))))
        
        # Customize
        plt.yticks(range(len(top_names)), top_names)
        plt.xlabel('Importance Score')
        plt.title(f'Top {top_n} Feature Importance')
        plt.gca().invert_yaxis()  # Highest importance at top
        
        # Add value labels on bars
        for i, (bar, score) in enumerate(zip(bars, top_scores)):
            plt.text(bar.get_width() + max(top_scores)*0.01, bar.get_y() + bar.get_height()/2,
                    f'{score:.3f}', va='center', fontsize=9)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def plot_feature_selection_comparison(self, all_features, selected_features_dict, 
                                        feature_names, save_path=None):
        """
        Compare different feature selection methods.
        
        Args:
            all_features: Matrix of all features
            selected_features_dict: Dictionary of method name -> selected indices
            feature_names: List of feature names
            save_path: Path to save the plot
        """
        methods = list(selected_features_dict.keys())
        n_methods = len(methods)
        
        fig, axes = plt.subplots(2, n_methods, figsize=(5*n_methods, 10))
        if n_methods == 1:
            axes = axes.reshape(2, 1)
        
        # Plot 1: Number of features selected
        feature_counts = [len(selected_features_dict[method]) for method in methods]
        
        axes[0, 0].bar(methods, feature_counts, color=self.colors[:n_methods])
        axes[0, 0].set_title('Number of Features Selected')
        axes[0, 0].set_ylabel('Number of Features')
        
        # Plot 2: Feature overlap
        for i, method in enumerate(methods):
            selected_indices = selected_features_dict[method]
            overlap_scores = []
            
            for j, other_method in enumerate(methods):
                other_indices = selected_features_dict[other_method]
                overlap = len(set(selected_indices) & set(other_indices))
                overlap_scores.append(overlap)
            
            axes[0, 1].bar([f'{method}\nvs\n{other}' for other in methods], 
                          overlap_scores, alpha=0.7, label=method)
        
        axes[0, 1].set_title('Feature Selection Overlap')
        axes[0, 1].set_ylabel('Number of Common Features')
        axes[0, 1].legend()
        
        # Plot 3: Selected features for each method
        for i, method in enumerate(methods):
            selected_indices = selected_features_dict[method]
            selected_names = [feature_names[idx] for idx in selected_indices[:15]]  # Top 15
            
            y_pos = np.arange(len(selected_names))
            axes[1, i].barh(y_pos, [1]*len(selected_names), 
                           color=self.colors[i % len(self.colors)])
            axes[1, i].set_yticks(y_pos)
            axes[1, i].set_yticklabels(selected_names, fontsize=8)
            axes[1, i].set_title(f'{method} - Top 15 Features')
            axes[1, i].set_xlabel('Selected')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def plot_dimensionality_reduction(self, features_matrix, labels, 
                                    method='tsne', save_path=None):
        """
        Plot dimensionality reduction visualization.
        
        Args:
            features_matrix: Matrix of features
            labels: Labels for different groups
            method: Reduction method ('tsne', 'pca')
            save_path: Path to save the plot
        """
        # Standardize features
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features_matrix)
        
        # Apply dimensionality reduction
        if method.lower() == 'tsne':
            reducer = TSNE(n_components=2, random_state=42, perplexity=30)
            reduced_features = reducer.fit_transform(features_scaled)
            title = 't-SNE Visualization'
        elif method.lower() == 'pca':
            reducer = PCA(n_components=2, random_state=42)
            reduced_features = reducer.fit_transform(features_scaled)
            explained_var = reducer.explained_variance_ratio_
            title = f'PCA Visualization (Explained Variance: {explained_var[0]:.2f}, {explained_var[1]:.2f})'
        else:
            print(f"Unsupported method: {method}")
            return
        
        # Create plot
        plt.figure(figsize=self.figsize)
        
        # Plot different groups
        unique_labels = np.unique(labels)
        for i, label in enumerate(unique_labels):
            mask = np.array(labels) == label
            group_data = reduced_features[mask]
            
            label_name = f'Tumor' if label == 1 else f'Normal' if label == 0 else f'Group {label}'
            plt.scatter(group_data[:, 0], group_data[:, 1], 
                       c=self.colors[i % len(self.colors)], 
                       label=label_name, alpha=0.7, s=50)
        
        plt.xlabel(f'Component 1')
        plt.ylabel(f'Component 2')
        plt.title(title)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def plot_feature_clustering(self, features_dict, feature_names=None, 
                               save_path=None, method='ward'):
        """
        Plot hierarchical clustering of features.
        
        Args:
            features_dict: Dictionary containing feature values
            feature_names: List of feature names to include
            save_path: Path to save the plot
            method: Clustering method
        """
        if feature_names is None:
            feature_names = list(features_dict.keys())
        
        # Create feature matrix
        feature_matrix = []
        valid_features = []
        
        for name in feature_names:
            if name in features_dict:
                values = np.array(features_dict[name])
                if not np.all(np.isnan(values)) and np.var(values) > 0:
                    feature_matrix.append(values)
                    valid_features.append(name)
        
        if len(feature_matrix) < 2:
            print("Need at least 2 valid features for clustering")
            return
        
        feature_matrix = np.array(feature_matrix)
        
        # Perform hierarchical clustering
        linkage_matrix = linkage(feature_matrix, method=method)
        
        # Create dendrogram
        plt.figure(figsize=(max(12, len(valid_features)//2), 8))
        
        dendrogram(linkage_matrix, labels=valid_features, 
                  orientation='top', distance_sort='descending',
                  show_leaf_counts=True)
        
        plt.title(f'Hierarchical Clustering of Features ({method.capitalize()})')
        plt.xlabel('Features')
        plt.ylabel('Distance')
        plt.xticks(rotation=45, ha='right')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def create_feature_summary_dashboard(self, features_dict, labels, 
                                       feature_names=None, save_path=None):
        """
        Create a comprehensive dashboard summarizing all features.
        
        Args:
            features_dict: Dictionary containing feature values
            labels: Labels for different groups
            feature_names: List of feature names to include
            save_path: Path to save the plot
        """
        if feature_names is None:
            feature_names = list(features_dict.keys())[:20]  # Limit to first 20
        
        # Create subplot grid
        fig = plt.figure(figsize=(20, 16))
        gs = fig.add_gridspec(4, 4, hspace=0.3, wspace=0.3)
        
        # 1. Feature distribution (top-left)
        ax1 = fig.add_subplot(gs[0, :2])
        sample_feature = feature_names[0] if feature_names else list(features_dict.keys())[0]
        if sample_feature in features_dict:
            values = np.array(features_dict[sample_feature])
            unique_labels = np.unique(labels)
            for i, label in enumerate(unique_labels):
                mask = np.array(labels) == label
                group_values = values[mask]
                label_name = f'Tumor' if label == 1 else f'Normal' if label == 0 else f'Group {label}'
                ax1.hist(group_values, bins=30, alpha=0.6, 
                        color=self.colors[i % len(self.colors)], label=label_name)
            ax1.set_title(f'Distribution: {sample_feature}')
            ax1.legend()
        
        # 2. Feature importance (top-right)
        ax2 = fig.add_subplot(gs[0, 2:])
        if len(feature_names) > 0:
            # Mock importance scores for demonstration
            importance_scores = np.random.rand(len(feature_names))
            importance_scores = np.sort(importance_scores)[::-1]
            
            top_n = min(10, len(feature_names))
            y_pos = np.arange(top_n)
            ax2.barh(y_pos, importance_scores[:top_n], 
                    color=plt.cm.viridis(np.linspace(0, 1, top_n)))
            ax2.set_yticks(y_pos)
            ax2.set_yticklabels(feature_names[:top_n], fontsize=9)
            ax2.set_title('Top 10 Feature Importance')
            ax2.invert_yaxis()
        
        # 3. Feature correlation heatmap (middle-left)
        ax3 = fig.add_subplot(gs[1, :2])
        valid_features = []
        feature_matrix = []
        
        for name in feature_names[:10]:  # Limit to 10 for readability
            if name in features_dict:
                values = np.array(features_dict[name])
                if not np.all(np.isnan(values)) and np.var(values) > 0:
                    feature_matrix.append(values)
                    valid_features.append(name)
        
        if len(feature_matrix) >= 2:
            feature_matrix = np.array(feature_matrix).T
            df = pd.DataFrame(feature_matrix, columns=valid_features)
            corr_matrix = df.corr()
            
            sns.heatmap(corr_matrix, annot=True, fmt='.2f', cmap='coolwarm',
                       center=0, square=True, ax=ax3, cbar_kws={"shrink": .8})
            ax3.set_title('Feature Correlation Matrix')
        
        # 4. Box plot comparison (middle-right)
        ax4 = fig.add_subplot(gs[1, 2:])
        if len(feature_names) >= 3:
            sample_features = feature_names[:3]
            box_data = []
            box_labels = []
            
            for feature in sample_features:
                if feature in features_dict:
                    values = np.array(features_dict[feature])
                    box_data.append(values)
                    box_labels.append(feature)
            
            if box_data:
                ax4.boxplot(box_data, labels=box_labels)
                ax4.set_title('Feature Value Ranges')
                ax4.tick_params(axis='x', rotation=45)
        
        # 5. Feature statistics table (bottom)
        ax5 = fig.add_subplot(gs[2:, :])
        ax5.axis('off')
        
        # Create statistics table
        stats_data = []
        for feature in feature_names[:10]:  # Limit to 10 features
            if feature in features_dict:
                values = np.array(features_dict[feature])
                stats_data.append([
                    feature,
                    f"{np.mean(values):.3f}",
                    f"{np.std(values):.3f}",
                    f"{np.min(values):.3f}",
                    f"{np.max(values):.3f}",
                    f"{np.median(values):.3f}"
                ])
        
        if stats_data:
            table = ax5.table(cellText=stats_data,
                             colLabels=['Feature', 'Mean', 'Std', 'Min', 'Max', 'Median'],
                             cellLoc='center',
                             loc='center',
                             colWidths=[0.3, 0.14, 0.14, 0.14, 0.14, 0.14])
            
            table.auto_set_font_size(False)
            table.set_fontsize(10)
            table.scale(1.2, 1.5)
            
            # Style the table
            for i in range(len(stats_data) + 1):
                for j in range(6):
                    cell = table[(i, j)]
                    if i == 0:  # Header
                        cell.set_facecolor('#4CAF50')
                        cell.set_text_props(weight='bold', color='white')
                    else:
                        cell.set_facecolor('#f0f0f0' if i % 2 == 0 else '#ffffff')
            
            ax5.set_title('Feature Statistics Summary', pad=20, fontsize=14, fontweight='bold')
        
        plt.suptitle('Feature Extraction Summary Dashboard', fontsize=16, fontweight='bold')
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()


def main():
    """
    Example usage of the feature visualization module.
    """
    # Create sample data
    np.random.seed(42)
    
    # Generate sample features
    n_samples = 200
    features_dict = {
        'area': np.random.normal(100, 20, n_samples),
        'perimeter': np.random.normal(50, 10, n_samples),
        'circularity': np.random.beta(2, 5, n_samples),
        'mean_intensity': np.random.normal(128, 30, n_samples),
        'std_intensity': np.random.normal(25, 5, n_samples),
        'contrast': np.random.exponential(0.5, n_samples),
        'entropy': np.random.normal(5, 1, n_samples),
        'eccentricity': np.random.beta(3, 2, n_samples)
    }
    
    # Generate labels (0: normal, 1: tumor)
    labels = np.random.randint(0, 2, n_samples)
    
    # Make tumor samples have different characteristics
    tumor_mask = labels == 1
    features_dict['area'][tumor_mask] *= 1.5
    features_dict['circularity'][tumor_mask] *= 0.8
    features_dict['contrast'][tumor_mask] *= 1.3
    
    # Initialize visualizer
    visualizer = FeatureVisualizer()
    
    print("Creating feature visualizations...")
    
    # 1. Feature distribution
    print("1. Feature distribution plot...")
    visualizer.plot_feature_distribution(features_dict, 'area', labels)
    
    # 2. Feature comparison
    print("2. Feature comparison plot...")
    visualizer.plot_feature_comparison(features_dict, 
                                     ['area', 'circularity', 'contrast', 'entropy'], 
                                     labels)
    
    # 3. Correlation matrix
    print("3. Correlation matrix...")
    visualizer.plot_correlation_matrix(features_dict)
    
    # 4. Feature importance
    print("4. Feature importance plot...")
    feature_names = list(features_dict.keys())
    importance_scores = np.random.rand(len(feature_names))
    visualizer.plot_feature_importance(feature_names, importance_scores)
    
    # 5. Dimensionality reduction
    print("5. Dimensionality reduction...")
    feature_matrix = np.column_stack([features_dict[name] for name in feature_names])
    visualizer.plot_dimensionality_reduction(feature_matrix, labels, method='tsne')
    
    # 6. Feature clustering
    print("6. Feature clustering...")
    visualizer.plot_feature_clustering(features_dict)
    
    # 7. Comprehensive dashboard
    print("7. Creating comprehensive dashboard...")
    visualizer.create_feature_summary_dashboard(features_dict, labels, feature_names)
    
    print("Feature visualization demo completed!")


if __name__ == "__main__":
    main()"""
Feature Visualization Module
===========================

This module provides comprehensive visualization tools for feature analysis
in brain tumor detection systems.

Author: Dr. Mohammed Yagoub Esmail
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.patches import Rectangle
from matplotlib.colors import LinearSegmentedColormap
import pandas as pd
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from scipy.cluster.hierarchy import dendrogram, linkage
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore')


class FeatureVisualizer:
    """
    Comprehensive feature visualization class.
    """
    
    def __init__(self, style='seaborn-v0_8', figsize=(12, 8)):
        """
        Initialize the visualizer.
        
        Args:
            style: Matplotlib style to use
            figsize: Default figure size
        """
        plt.style.use(style)
        self.figsize = figsize
        self.colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
                      '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
    
    def plot_feature_distribution(self, features_dict, feature_name, labels=None, 
                                save_path=None, bins=50):
        """
        Plot distribution of a single feature.
        
        Args:
            features_dict: Dictionary containing feature values
            feature_name: Name of the feature to plot
            labels: Optional labels for different groups
            save_path: Path to save the plot
            bins: Number of histogram bins
        """
        if feature_name not in features_dict:
            print(f"Feature '{feature_name}' not found in features_dict")
            return
        
        plt.figure(figsize=self.figsize)
        
        values = np.array(features_dict[feature_name])
        
        if labels is None:
            # Single distribution
            plt.hist(values, bins=bins, alpha=0.7, color=self.colors[0], 
                    edgecolor='black', linewidth=0.5)
            plt.title(f'Distribution of {feature_name}')
        else:
            # Multiple distributions
            unique_labels = np.unique(labels)
            for i, label in enumerate(unique_labels):
                mask = np.array(labels) == label
                group_values = values[mask]
                label_name = f'Tumor' if label == 1 else f'Normal' if label == 0 else f'Group {label}'
                plt.hist(group_values, bins=bins, alpha=0.6, 
                        color=self.colors[i % len(self.colors)], 
                        label=label_name, edgecolor='black', linewidth=0.5)
            plt.legend()
            plt.title(f'Distribution of {feature_name} by Group')
        
        plt.xlabel(feature_name)
        plt.ylabel('Frequency')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def plot_feature_comparison(self, features_dict, feature_names, labels, 
                              save_path=None):
        """
        Compare multiple features across different groups.
        
        Args:
            features_dict: Dictionary containing feature values
            feature_names: List of feature names to compare
            labels: Labels for different groups
            save_path: Path to save the plot
        """
        n_features = len(feature_names)
        n_cols = min(3, n_features)
        n_rows = (n_features + n_cols - 1) // n_cols
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(5*n_cols, 4*n_rows))
        if n_features == 1:
            axes = [axes]
        elif n_rows == 1:
            axes = axes.reshape(1, -1)
        
        for i, feature_name in enumerate(feature_names):
            row = i // n_cols
            col = i % n_cols
            ax = axes[row, col] if n_rows > 1 else axes[col]
            
            if feature_name in features_dict:
                values = np.array(features_dict[feature_name])
                
                # Create dataframe for seaborn
                df = pd.DataFrame({
                    'value': values,
                    'group': ['Tumor' if l == 1 else 'Normal' for l in labels]
                })
                
                # Box plot
                sns.boxplot(data=df, x='group', y='value', ax=ax)
                ax.set_title(f'{feature_name}')
                ax.set_xlabel('Group')
                ax.set_ylabel('Value')
            else:
                ax.text(0.5, 0.5, f'Feature\n{feature_name}\nnot found', 
                       ha='center', va='center', transform=ax.transAxes)
        
        # Remove empty subplots
        for i in range(n_features, n_rows * n_cols):
            row = i // n_cols
            col = i % n_cols
            ax = axes[row, col] if n_rows > 1 else axes[col]
            ax.remove()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def plot_correlation_matrix(self, features_dict, feature_names=None, 
                               save_path=None, method='pearson'):
        """
        Plot correlation matrix of features.
        
        Args:
            features_dict: Dictionary containing feature values
            feature_names: List of feature names to include
            save_path: Path to save the plot
            method: Correlation method ('pearson', 'spearman', 'kendall')
        """
        if feature_names is None:
            feature_names = list(features_dict.keys())
        
        # Create feature matrix
        feature_matrix = []
        valid_features = []
        
        for name in feature_names:
            if name in features_dict:
                values = np.array(features_dict[name])
                if not np.all(np.isnan(values)) and np.var(values) > 0:
                    feature_matrix.append(values)
                    valid_features.append(name)
        
        if len(feature_matrix) < 2:
            print("Need at least 2 valid features for correlation matrix")
            return
        
        feature_matrix = np.array(feature_matrix).T
        df = pd.DataFrame(feature_matrix, columns=valid_features)
        
        # Calculate correlation
        corr_matrix = df.corr(method=method)
        
        # Plot
        plt.figure(figsize=(max(10, len(valid_features)//2), max(8, len(valid_features)//2)))
        
        # Create custom colormap
        colors = ['#d73027', '#f46d43', '#fdae61', '#fee08b', '#e6f598', 
                 '#abdda4', '#66c2a5', '#3288bd', '#5e4fa2']
        cmap = LinearSegmentedColormap.from_list('custom', colors, N=256)
        
        mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
        sns.heatmap(corr_matrix, mask=mask, annot=True, fmt='.2f', 
                   cmap=cmap, center=0, square=True, linewidths=0.5,
                   cbar_kws={"shrink": .8})
        
        plt.title(f'Feature Correlation Matrix ({method.capitalize()})')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def plot_feature_importance(self, feature_names, importance_scores, 
                              top_n=20, save_path=None):
        """
        Plot feature importance scores.
        
        Args:
            feature_names: List of feature names
            importance_scores: Corresponding importance scores
            top_n: Number of top features to show
            save_path: Path to save the plot
        """
        # Sort by importance
        sorted_indices = np.argsort(importance_scores)[::-1]
        
        # Get top N features
        top_indices = sorted_indices[:top_n]
        top_names = [feature_names[i] for i in top_indices]
        top_scores = [importance_scores[i] for i in top_indices]
        
        # Create plot
        plt.figure(figsize=(12, max(8, top_n//3)))
        
        # Horizontal bar plot
        bars = plt.barh(range(len(top_names)), top_scores, 
                       color=plt.cm.viridis(np.linspace(0, 1, len(top_names))))
        
        # Customize
        plt.yticks(range(len(top_names)), top_names)
        plt.xlabel('Importance Score')
        plt.title(f'Top {top_n} Feature Importance')
        plt.gca().invert_yaxis()  # Highest importance at top
        
        # Add value labels on bars
        for i, (bar, score) in enumerate(zip(bars, top_scores)):
            plt.text(bar.get_width() + max(top_scores)*0.01, bar.get_y() + bar.get_height()/2,
                    f'{score:.3f}', va='center', fontsize=9)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def plot_feature_selection_comparison(self, all_features, selected_features_dict, 
                                        feature_names, save_path=None):
        """
        Compare different feature selection methods.
        
        Args:
            all_features: Matrix of all features
            selected_features_dict: Dictionary of method name -> selected indices
            feature_names: List of feature names
            save_path: Path to save the plot
        """
        methods = list(selected_features_dict.keys())
        n_methods = len(methods)
        
        fig, axes = plt.subplots(2, n_methods, figsize=(5*n_methods, 10))
        if n_methods == 1:
            axes = axes.reshape(2, 1)
        
        # Plot 1: Number of features selected
        feature_counts = [len(selected_features_dict[method]) for method in methods]
        
        axes[0, 0].bar(methods, feature_counts, color=self.colors[:n_methods])
        axes[0, 0].set_title('Number of Features Selected')
        axes[0, 0].set_ylabel('Number of Features')
        
        # Plot 2: Feature overlap
        for i, method in enumerate(methods):
            selected_indices = selected_features_dict[method]
            overlap_scores = []
            
            for j, other_method in enumerate(methods):
                other_indices = selected_features_dict[other_method]
                overlap = len(set(selected_indices) & set(other_indices))
                overlap_scores.append(overlap)
            
            axes[0, 1].bar([f'{method}\nvs\n{other}' for other in methods], 
                          overlap_scores, alpha=0.7, label=method)
        
        axes[0, 1].set_title('Feature Selection Overlap')
        axes[0, 1].set_ylabel('Number of Common Features')
        axes[0, 1].legend()
        
        # Plot 3: Selected features for each method
        for i, method in enumerate(methods):
            selected_indices = selected_features_dict[method]
            selected_names = [feature_names[idx] for idx in selected_indices[:15]]  # Top 15
            
            y_pos = np.arange(len(selected_names))
            axes[1, i].barh(y_pos, [1]*len(selected_names), 
                           color=self.colors[i % len(self.colors)])
            axes[1, i].set_yticks(y_pos)
            axes[1, i].set_yticklabels(selected_names, fontsize=8)
            axes[1, i].set_title(f'{method} - Top 15 Features')
            axes[1, i].set_xlabel('Selected')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def plot_dimensionality_reduction(self, features_matrix, labels, 
                                    method='tsne', save_path=None):
        """
        Plot dimensionality reduction visualization.
        
        Args:
            features_matrix: Matrix of features
            labels: Labels for different groups
            method: Reduction method ('tsne', 'pca')
            save_path: Path to save the plot
        """
        # Standardize features
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features_matrix)
        
        # Apply dimensionality reduction
        if method.lower() == 'tsne':
            reducer = TSNE(n_components=2, random_state=42, perplexity=30)
            reduced_features = reducer.fit_transform(features_scaled)
            title = 't-SNE Visualization'
        elif method.lower() == 'pca':
            reducer = PCA(n_components=2, random_state=42)
            reduced_features = reducer.fit_transform(features_scaled)
            explained_var = reducer.explained_variance_ratio_
            title = f'PCA Visualization (Explained Variance: {explained_var[0]:.2f}, {explained_var[1]:.2f})'
        else:
            print(f"Unsupported method: {method}")
            return
        
        # Create plot
        plt.figure(figsize=self.figsize)
        
        # Plot different groups
        unique_labels = np.unique(labels)
        for i, label in enumerate(unique_labels):
            mask = np.array(labels) == label
            group_data = reduced_features[mask]
            
            label_name = f'Tumor' if label == 1 else f'Normal' if label == 0 else f'Group {label}'
            plt.scatter(group_data[:, 0], group_data[:, 1], 
                       c=self.colors[i % len(self.colors)], 
                       label=label_name, alpha=0.7, s=50)
        
        plt.xlabel(f'Component 1')
        plt.ylabel(f'Component 2')
        plt.title(title)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def plot_feature_clustering(self, features_dict, feature_names=None, 
                               save_path=None, method='ward'):
        """
        Plot hierarchical clustering of features.
        
        Args:
            features_dict: Dictionary containing feature values
            feature_names: List of feature names to include
            save_path: Path to save the plot
            method: Clustering method
        """
        if feature_names is None:
            feature_names = list(features_dict.keys())
        
        # Create feature matrix
        feature_matrix = []
        valid_features = []
        
        for name in feature_names:
            if name in features_dict:
                values = np.array(features_dict[name])
                if not np.all(np.isnan(values)) and np.var(values) > 0:
                    feature_matrix.append(values)
                    valid_features.append(name)
        
        if len(feature_matrix) < 2:
            print("Need at least 2 valid features for clustering")
            return
        
        feature_matrix = np.array(feature_matrix)
        
        # Perform hierarchical clustering
        linkage_matrix = linkage(feature_matrix, method=method)
        
        # Create dendrogram
        plt.figure(figsize=(max(12, len(valid_features)//2), 8))
        
        dendrogram(linkage_matrix, labels=valid_features, 
                  orientation='top', distance_sort='descending',
                  show_leaf_counts=True)
        
        plt.title(f'Hierarchical Clustering of Features ({method.capitalize()})')
        plt.xlabel('Features')
        plt.ylabel('Distance')
        plt.xticks(rotation=45, ha='right')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def create_feature_summary_dashboard(self, features_dict, labels, 
                                       feature_names=None, save_path=None):
        """
        Create a comprehensive dashboard summarizing all features.
        
        Args:
            features_dict: Dictionary containing feature values
            labels: Labels for different groups
            feature_names: List of feature names to include
            save_path: Path to save the plot
        """
        if feature_names is None:
            feature_names = list(features_dict.keys())[:20]  # Limit to first 20
        
        # Create subplot grid
        fig = plt.figure(figsize=(20, 16))
        gs = fig.add_gridspec(4, 4, hspace=0.3, wspace=0.3)
        
        # 1. Feature distribution (top-left)
        ax1 = fig.add_subplot(gs[0, :2])
        sample_feature = feature_names[0] if feature_names else list(features_dict.keys())[0]
        if sample_feature in features_dict:
            values = np.array(features_dict[sample_feature])
            unique_labels = np.unique(labels)
            for i, label in enumerate(unique_labels):
                mask = np.array(labels) == label
                group_values = values[mask]
                label_name = f'Tumor' if label == 1 else f'Normal' if label == 0 else f'Group {label}'
                ax1.hist(group_values, bins=30, alpha=0.6, 
                        color=self.colors[i % len(self.colors)], label=label_name)
            ax1.set_title(f'Distribution: {sample_feature}')
            ax1.legend()
        
        # 2. Feature importance (top-right)
        ax2 = fig.add_subplot(gs[0, 2:])
        if len(feature_names) > 0:
            # Mock importance scores for demonstration
            importance_scores = np.random.rand(len(feature_names))
            importance_scores = np.sort(importance_scores)[::-1]
            
            top_n = min(10, len(feature_names))
            y_pos = np.arange(top_n)
            ax2.barh(y_pos, importance_scores[:top_n], 
                    color=plt.cm.viridis(np.linspace(0, 1, top_n)))
            ax2.set_yticks(y_pos)
            ax2.set_yticklabels(feature_names[:top_n], fontsize=9)
            ax2.set_title('Top 10 Feature Importance')
            ax2.invert_yaxis()
        
        # 3. Feature correlation heatmap (middle-left)
        ax3 = fig.add_subplot(gs[1, :2])
        valid_features = []
        feature_matrix = []
        
        for name in feature_names[:10]:  # Limit to 10 for readability
            if name in features_dict:
                values = np.array(features_dict[name])
                if not np.all(np.isnan(values)) and np.var(values) > 0:
                    feature_matrix.append(values)
                    valid_features.append(name)
        
        if len(feature_matrix) >= 2:
            feature_matrix = np.array(feature_matrix).T
            df = pd.DataFrame(feature_matrix, columns=valid_features)
            corr_matrix = df.corr()
            
            sns.heatmap(corr_matrix, annot=True, fmt='.2f', cmap='coolwarm',
                       center=0, square=True, ax=ax3, cbar_kws={"shrink": .8})
            ax3.set_title('Feature Correlation Matrix')
        
        # 4. Box plot comparison (middle-right)
        ax4 = fig.add_subplot(gs[1, 2:])
        if len(feature_names) >= 3:
            sample_features = feature_names[:3]
            box_data = []
            box_labels = []
            
            for feature in sample_features:
                if feature in features_dict:
                    values = np.array(features_dict[feature])
                    box_data.append(values)
                    box_labels.append(feature)
            
            if box_data:
                ax4.boxplot(box_data, labels=box_labels)
                ax4.set_title('Feature Value Ranges')
                ax4.tick_params(axis='x', rotation=45)
        
        # 5. Feature statistics table (bottom)
        ax5 = fig.add_subplot(gs[2:, :])
        ax5.axis('off')
        
        # Create statistics table
        stats_data = []
        for feature in feature_names[:10]:  # Limit to 10 features
            if feature in features_dict:
                values = np.array(features_dict[feature])
                stats_data.append([
                    feature,
                    f"{np.mean(values):.3f}",
                    f"{np.std(values):.3f}",
                    f"{np.min(values):.3f}",
                    f"{np.max(values):.3f}",
                    f"{np.median(values):.3f}"
                ])
        
        if stats_data:
            table = ax5.table(cellText=stats_data,
                             colLabels=['Feature', 'Mean', 'Std', 'Min', 'Max', 'Median'],
                             cellLoc='center',
                             loc='center',
                             colWidths=[0.3, 0.14, 0.14, 0.14, 0.14, 0.14])
            
            table.auto_set_font_size(False)
            table.set_fontsize(10)
            table.scale(1.2, 1.5)
            
            # Style the table
            for i in range(len(stats_data) + 1):
                for j in range(6):
                    cell = table[(i, j)]
                    if i == 0:  # Header
                        cell.set_facecolor('#4CAF50')
                        cell.set_text_props(weight='bold', color='white')
                    else:
                        cell.set_facecolor('#f0f0f0' if i % 2 == 0 else '#ffffff')
            
            ax5.set_title('Feature Statistics Summary', pad=20, fontsize=14, fontweight='bold')
        
        plt.suptitle('Feature Extraction Summary Dashboard', fontsize=16, fontweight='bold')
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()


def main():
    """
    Example usage of the feature visualization module.
    """
    # Create sample data
    np.random.seed(42)
    
    # Generate sample features
    n_samples = 200
    features_dict = {
        'area': np.random.normal(100, 20, n_samples),
        'perimeter': np.random.normal(50, 10, n_samples),
        'circularity': np.random.beta(2, 5, n_samples),
        'mean_intensity': np.random.normal(128, 30, n_samples),
        'std_intensity': np.random.normal(25, 5, n_samples),
        'contrast': np.random.exponential(0.5, n_samples),
        'entropy': np.random.normal(5, 1, n_samples),
        'eccentricity': np.random.beta(3, 2, n_samples)
    }
    
    # Generate labels (0: normal, 1: tumor)
    labels = np.random.randint(0, 2, n_samples)
    
    # Make tumor samples have different characteristics
    tumor_mask = labels == 1
    features_dict['area'][tumor_mask] *= 1.5
    features_dict['circularity'][tumor_mask] *= 0.8
    features_dict['contrast'][tumor_mask] *= 1.3
    
    # Initialize visualizer
    visualizer = FeatureVisualizer()
    
    print("Creating feature visualizations...")
    
    # 1. Feature distribution
    print("1. Feature distribution plot...")
    visualizer.plot_feature_distribution(features_dict, 'area', labels)
    
    # 2. Feature comparison
    print("2. Feature comparison plot...")
    visualizer.plot_feature_comparison(features_dict, 
                                     ['area', 'circularity', 'contrast', 'entropy'], 
                                     labels)
    
    # 3. Correlation matrix
    print("3. Correlation matrix...")
    visualizer.plot_correlation_matrix(features_dict)
    
    # 4. Feature importance
    print("4. Feature importance plot...")
    feature_names = list(features_dict.keys())
    importance_scores = np.random.rand(len(feature_names))
    visualizer.plot_feature_importance(feature_names, importance_scores)
    
    # 5. Dimensionality reduction
    print("5. Dimensionality reduction...")
    feature_matrix = np.column_stack([features_dict[name] for name in feature_names])
    visualizer.plot_dimensionality_reduction(feature_matrix, labels, method='tsne')
    
    # 6. Feature clustering
    print("6. Feature clustering...")
    visualizer.plot_feature_clustering(features_dict)
    
    # 7. Comprehensive dashboard
    print("7. Creating comprehensive dashboard...")
    visualizer.create_feature_summary_dashboard(features_dict, labels, feature_names)
    
    print("Feature visualization demo completed!")


if __name__ == "__main__":
    main()