/**
 * Brain Tumor Detection in 3D MRI Images - Stylesheet
 *
 * Author: Dr. <PERSON>il
 * Affiliation: SUST - BME (Biomedical Engineering)
 * Email: <EMAIL>
 * Phone: +249912867327 | +966538076790
 *
 * Copyright © 2025 Dr. <PERSON> Esmail. All rights reserved.
 *
 * Modern CSS styling for the brain tumor detection web application
 * with responsive design, animations, and enhanced visual effects.
 */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #1f2937;
    background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 50%, #faf5ff 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(12px);
    border-bottom: 1px solid rgba(229, 231, 235, 0.5);
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-icon {
    font-size: 2rem;
    color: #2563eb;
    animation: pulse 2s infinite;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.badge {
    background: #dbeafe;
    color: #1e40af;
    padding: 0.5rem 1rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.btn-primary {
    background: #2563eb;
    color: white;
}

.btn-primary:hover {
    background: #1d4ed8;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
}

.btn-outline {
    background: transparent;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-outline:hover {
    background: #f9fafb;
    border-color: #9ca3af;
}

/* Hero Section */
.hero {
    padding: 5rem 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(147, 51, 234, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.hero-content {
    position: relative;
    z-index: 1;
    animation: fadeInUp 1s ease-out;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    color: #1f2937;
}

.gradient-text {
    background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.25rem;
    color: #6b7280;
    max-width: 48rem;
    margin: 0 auto 2rem;
    line-height: 1.7;
}

.hero-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero .btn {
    padding: 1rem 2rem;
    font-size: 1rem;
}

/* Main Content */
.main-content {
    padding: 4rem 0;
}

/* Tab Navigation */
.tab-navigation {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.5rem;
    margin-bottom: 2rem;
    background: white;
    padding: 0.5rem;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.tab-btn {
    padding: 0.75rem 1rem;
    border: none;
    background: transparent;
    color: #6b7280;
    font-weight: 500;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.tab-btn.active {
    background: #2563eb;
    color: white;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.tab-btn:hover:not(.active) {
    background: #f3f4f6;
    color: #374151;
}

/* Tab Content */
.tab-content {
    min-height: 600px;
}

.tab-pane {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.tab-pane.active {
    display: block;
}

/* Overview Grid */
.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
}

.process-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid #f3f4f6;
}

.process-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-color: #e5e7eb;
}

.card-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.icon-wrapper {
    background: #dbeafe;
    padding: 0.75rem;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
}

.process-card:hover .icon-wrapper {
    background: #bfdbfe;
    transform: scale(1.1);
}

.icon-wrapper i {
    font-size: 1.5rem;
    color: #2563eb;
}

.card-title-section {
    flex: 1;
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.step-badge {
    background: #f3f4f6;
    color: #6b7280;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.card-content {
    margin-top: 1rem;
}

.card-image {
    margin-bottom: 1rem;
}

.placeholder-image {
    height: 8rem;
    border-radius: 0.75rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-weight: 500;
    color: white;
    position: relative;
    overflow: hidden;
}

.placeholder-image i {
    font-size: 2rem;
    opacity: 0.9;
}

.placeholder-image span {
    font-size: 0.875rem;
    opacity: 0.8;
}

/* Placeholder Image Themes */
.dicom-placeholder {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
}

.preprocessing-placeholder {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
}

.segmentation-placeholder {
    background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
}

.features-placeholder {
    background: linear-gradient(135deg, #7c2d12 0%, #ea580c 100%);
}

.reconstruction-placeholder {
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
}

.results-placeholder {
    background: linear-gradient(135deg, #be185d 0%, #ec4899 100%);
}

.card-description {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.6;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-description {
        font-size: 1.125rem;
    }
    
    .tab-navigation {
        grid-template-columns: repeat(4, 1fr);
        gap: 0.25rem;
    }
    
    .tab-btn {
        padding: 0.5rem;
        font-size: 0.75rem;
    }
    
    .overview-grid {
        grid-template-columns: 1fr;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
}

/* Detail Cards */
.detail-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    border: 1px solid #f3f4f6;
}

.detail-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 2rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e2e8f0;
}

.detail-icon {
    font-size: 2rem;
    color: #2563eb;
    background: #dbeafe;
    padding: 1rem;
    border-radius: 1rem;
}

.detail-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.detail-subtitle {
    color: #6b7280;
    font-size: 1rem;
    line-height: 1.6;
}

.detail-content {
    padding: 2rem;
}

.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

.content-left {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.detail-image {
    width: 100%;
}

.detail-image .placeholder-image {
    height: 16rem;
    font-size: 1.125rem;
}

.detail-image .placeholder-image i {
    font-size: 3rem;
}

/* Detail Placeholder Themes */
.dicom-detail-placeholder {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%);
}

.preprocessing-detail-placeholder {
    background: linear-gradient(135deg, #065f46 0%, #10b981 50%, #34d399 100%);
}

.segmentation-detail-placeholder {
    background: linear-gradient(135deg, #991b1b 0%, #ef4444 50%, #f87171 100%);
}

.features-detail-placeholder {
    background: linear-gradient(135deg, #9a3412 0%, #ea580c 50%, #fb923c 100%);
}

.reconstruction-detail-placeholder {
    background: linear-gradient(135deg, #6b21a8 0%, #a855f7 50%, #c084fc 100%);
}

.results-detail-placeholder {
    background: linear-gradient(135deg, #be185d 0%, #ec4899 50%, #f472b6 100%);
}

.features-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.features-list {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.features-list li {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.6;
    padding-left: 0.5rem;
}

/* Code Tabs */
.code-tabs {
    background: #1f2937;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.code-tab-nav {
    display: flex;
    background: #374151;
    border-bottom: 1px solid #4b5563;
}

.code-tab-btn {
    flex: 1;
    padding: 1rem;
    border: none;
    background: transparent;
    color: #9ca3af;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.code-tab-btn.active {
    color: #60a5fa;
    background: #1f2937;
    border-bottom-color: #60a5fa;
}

.code-tab-btn:hover:not(.active) {
    color: #d1d5db;
    background: rgba(75, 85, 99, 0.5);
}

.code-content {
    position: relative;
}

.code-block {
    display: none;
    max-height: 400px;
    overflow-y: auto;
}

.code-block.active {
    display: block;
}

.code-block pre {
    margin: 0;
    padding: 1.5rem;
    background: #1f2937;
    color: #e5e7eb;
    font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
    font-size: 0.875rem;
    line-height: 1.6;
    overflow-x: auto;
}

.code-block code {
    color: #10b981;
    text-shadow: 0 0 10px rgba(16, 185, 129, 0.3);
}

/* Custom Scrollbar for Code Blocks */
.code-block::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.code-block::-webkit-scrollbar-track {
    background: #374151;
}

.code-block::-webkit-scrollbar-thumb {
    background: #6b7280;
    border-radius: 4px;
}

.code-block::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Footer */
.footer {
    background: #1f2937;
    color: white;
    padding: 3rem 0 1rem;
    margin-top: 4rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.footer-logo i {
    font-size: 1.5rem;
    color: #60a5fa;
}

.footer-logo-text {
    font-size: 1.125rem;
    font-weight: 600;
}

.footer-description {
    color: #9ca3af;
    line-height: 1.6;
}

.footer-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-list {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.footer-list li {
    color: #9ca3af;
    font-size: 0.875rem;
    transition: color 0.3s ease;
}

.footer-list li:hover {
    color: #d1d5db;
    cursor: pointer;
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 2rem;
    display: flex;
    flex-direction: column;
    gap: 2rem;
    text-align: center;
}

/* Author Information Styles */
.author-info {
    background: rgba(55, 65, 81, 0.5);
    padding: 2rem;
    border-radius: 1rem;
    border: 1px solid #4b5563;
}

.author-title {
    color: #60a5fa;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.author-title::before,
.author-title::after {
    content: '';
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, transparent, #60a5fa, transparent);
}

.author-name {
    color: #f9fafb;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.author-affiliation {
    color: #d1d5db;
    font-size: 1.125rem;
    font-weight: 500;
    margin-bottom: 1.5rem;
    font-style: italic;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    align-items: center;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #9ca3af;
    font-size: 0.875rem;
    transition: color 0.3s ease;
}

.contact-item:hover {
    color: #d1d5db;
}

.contact-item i {
    color: #60a5fa;
    font-size: 1rem;
    width: 20px;
    text-align: center;
}

.contact-item a {
    color: #60a5fa;
    text-decoration: none;
    transition: all 0.3s ease;
}

.contact-item a:hover {
    color: #93c5fd;
    text-decoration: underline;
}

.copyright-info {
    color: #9ca3af;
    font-size: 0.875rem;
}

.copyright-info p {
    margin-bottom: 0.5rem;
}

.system-credit {
    color: #6b7280;
    font-size: 0.75rem;
    font-style: italic;
}

/* Loading Animation */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(8px);
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-overlay.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-spinner {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.spinner {
    width: 60px;
    height: 60px;
    border: 4px solid #e5e7eb;
    border-top: 4px solid #2563eb;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-spinner p {
    color: #6b7280;
    font-weight: 500;
    font-size: 1.125rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Visual Effects */
.placeholder-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Enhanced Hover Effects */
.process-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.05) 0%, rgba(124, 58, 237, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 1rem;
    pointer-events: none;
}

.process-card:hover::before {
    opacity: 1;
}

/* Floating Animation for Icons */
.logo-icon {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Enhanced Code Block Styling */
.code-block pre {
    position: relative;
    overflow: hidden;
}

.code-block pre::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #10b981 0%, #3b82f6 50%, #8b5cf6 100%);
}

/* Gradient Borders */
.detail-card {
    position: relative;
    background: white;
}

.detail-card::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 1px;
    background: linear-gradient(135deg, #e5e7eb, #f3f4f6, #e5e7eb);
    border-radius: 1rem;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    pointer-events: none;
}

/* Enhanced Button Animations */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

/* Particle Effect Background */
.hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 30%, rgba(59, 130, 246, 0.1) 1px, transparent 1px),
        radial-gradient(circle at 80% 70%, rgba(147, 51, 234, 0.1) 1px, transparent 1px),
        radial-gradient(circle at 40% 80%, rgba(16, 185, 129, 0.1) 1px, transparent 1px);
    background-size: 50px 50px, 80px 80px, 60px 60px;
    animation: particleFloat 20s linear infinite;
    pointer-events: none;
}

@keyframes particleFloat {
    0% { transform: translateY(0px) translateX(0px); }
    33% { transform: translateY(-10px) translateX(5px); }
    66% { transform: translateY(5px) translateX(-5px); }
    100% { transform: translateY(0px) translateX(0px); }
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
    .content-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .code-tabs {
        margin-top: 1rem;
    }

    .hero-title {
        font-size: 3rem;
    }
}

@media (max-width: 768px) {
    .detail-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .detail-content {
        padding: 1.5rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .tab-navigation {
        grid-template-columns: repeat(2, 1fr);
    }

    .tab-btn {
        font-size: 0.75rem;
        padding: 0.75rem 0.5rem;
    }

    .hero-title {
        font-size: 2.25rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    /* Author info responsive */
    .author-info {
        padding: 1.5rem;
    }

    .author-name {
        font-size: 1.25rem;
    }

    .author-affiliation {
        font-size: 1rem;
    }

    .contact-info {
        gap: 1rem;
    }

    .contact-item {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }

    .hero {
        padding: 3rem 0;
    }

    .hero-title {
        font-size: 1.875rem;
    }

    .tab-navigation {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .overview-grid {
        gap: 1rem;
    }

    .process-card {
        padding: 1rem;
    }

    /* DICOM Model Responsive */
    .pipeline-steps {
        flex-direction: column;
        gap: 1rem;
    }

    .pipeline-arrow {
        transform: rotate(90deg);
        margin: 0.5rem 0;
    }

    .architecture-grid {
        grid-template-columns: 1fr;
    }

    .tab-selector {
        flex-direction: column;
    }

    .impl-tab-btn {
        padding: 0.75rem;
        font-size: 0.75rem;
    }

    .diagram-container {
        gap: 1rem;
    }

    .layer-components {
        grid-template-columns: 1fr;
    }

    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .compliance-badges {
        justify-content: center;
    }

    .enhanced-features li {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    /* Preprocessing Responsive */
    .pipeline-flow {
        flex-direction: column;
        gap: 1rem;
    }

    .flow-arrow {
        transform: rotate(90deg);
        margin: 0.5rem 0;
    }

    .techniques-grid {
        grid-template-columns: 1fr;
    }

    .parameters-grid {
        grid-template-columns: 1fr;
    }

    .specs-container {
        grid-template-columns: 1fr;
    }

    .benchmark-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .technique-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .technique-params {
        justify-content: center;
    }

    .spec-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    /* Segmentation Responsive */
    .segmentation-flow {
        flex-direction: column;
        gap: 1rem;
    }

    .seg-arrow {
        transform: rotate(90deg);
        margin: 0.5rem 0;
    }

    .methods-grid {
        grid-template-columns: 1fr;
    }

    .accuracy-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .comparison-table {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .comparison-header {
        display: none;
    }

    .comparison-row {
        display: grid;
        grid-template-columns: 1fr;
        gap: 0.5rem;
        padding: 1rem;
        background: #f8fafc;
        border-radius: 0.75rem;
        margin-bottom: 1rem;
    }

    .method-cell {
        background: white;
        border-radius: 0.5rem;
        justify-content: center;
    }

    .metric-cell {
        background: white;
        border-radius: 0.5rem;
        justify-content: space-between;
    }

    .usecase-cell {
        background: white;
        border-radius: 0.5rem;
        text-align: center;
    }

    .performance-grid {
        grid-template-columns: 1fr;
    }

    .performance-metric {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .validation-container {
        grid-template-columns: 1fr;
    }

    .method-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .method-params {
        justify-content: center;
    }
}

/* DICOM Model Specific Styles */
.pipeline-overview {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 2rem;
    border-radius: 1rem;
    margin: 2rem 0;
    border: 1px solid #e2e8f0;
}

.pipeline-steps {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.pipeline-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex: 1;
    min-width: 150px;
    padding: 1rem;
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.pipeline-step:hover {
    transform: translateY(-4px);
}

.step-icon {
    background: #dbeafe;
    color: #2563eb;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.step-content h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.step-content p {
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.4;
}

.pipeline-arrow {
    color: #9ca3af;
    font-size: 1.5rem;
    margin: 0 0.5rem;
}

.model-architecture {
    margin: 3rem 0;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e5e7eb;
}

.section-title i {
    color: #2563eb;
    font-size: 1.75rem;
}

.architecture-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.architecture-component {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.architecture-component:hover {
    border-color: #3b82f6;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.component-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.component-header i {
    color: #2563eb;
    font-size: 1.5rem;
}

.component-header h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
}

.component-details {
    padding: 1.5rem;
}

.component-details ul {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.component-details li {
    font-size: 0.875rem;
    line-height: 1.6;
    color: #4b5563;
}

.component-details strong {
    color: #1f2937;
    font-weight: 600;
}

/* Enhanced Features List */
.enhanced-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.enhanced-features li {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 0.75rem;
    border-left: 4px solid #3b82f6;
    transition: all 0.3s ease;
}

.enhanced-features li:hover {
    background: #f1f5f9;
    transform: translateX(4px);
}

.enhanced-features li i {
    color: #3b82f6;
    font-size: 1.25rem;
    margin-top: 0.25rem;
    flex-shrink: 0;
}

.enhanced-features li div {
    flex: 1;
}

.enhanced-features li strong {
    display: block;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.enhanced-features li span {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Compliance Section */
.compliance-section {
    margin-top: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
    border-radius: 1rem;
    border: 1px solid #d1fae5;
}

.compliance-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-top: 1rem;
}

.compliance-badge {
    background: #10b981;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Implementation Tabs */
.implementation-tabs {
    background: #1f2937;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.tab-selector {
    display: flex;
    background: #374151;
    border-bottom: 1px solid #4b5563;
}

.impl-tab-btn {
    flex: 1;
    padding: 1rem;
    border: none;
    background: transparent;
    color: #9ca3af;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
    font-size: 0.875rem;
}

.impl-tab-btn.active {
    color: #60a5fa;
    background: #1f2937;
    border-bottom-color: #60a5fa;
}

.impl-tab-btn:hover:not(.active) {
    color: #d1d5db;
    background: rgba(75, 85, 99, 0.5);
}

.impl-content {
    display: none;
}

.impl-content.active {
    display: block;
}

.code-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: #374151;
    border-bottom: 1px solid #4b5563;
}

.code-header h5 {
    color: #f9fafb;
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.code-header i {
    color: #60a5fa;
}

.code-badge {
    background: #059669;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Model Diagram */
.model-diagram {
    background: #f8fafc;
    border-radius: 1rem;
    padding: 2rem;
    margin: 1.5rem 0;
}

.diagram-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.model-layer {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
}

.model-layer h6 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.input-layer {
    border-left: 4px solid #3b82f6;
}

.processing-layer {
    border-left: 4px solid #10b981;
}

.output-layer {
    border-left: 4px solid #8b5cf6;
}

.layer-components {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.component {
    background: #f8fafc;
    padding: 1rem;
    border-radius: 0.5rem;
    text-align: center;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.component:hover {
    background: #f1f5f9;
    border-color: #d1d5db;
}

/* Model Specifications */
.model-specifications, .performance-metrics {
    margin: 2rem 0;
}

.model-specifications h6, .performance-metrics h6 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.spec-grid {
    display: grid;
    gap: 1rem;
}

.spec-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 0.5rem;
    border-left: 3px solid #3b82f6;
}

.spec-item strong {
    color: #1f2937;
    font-weight: 600;
    font-size: 0.875rem;
}

.spec-item span {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.5;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.metric {
    text-align: center;
    padding: 1.5rem;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #059669;
    margin-bottom: 0.5rem;
}

.metric-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

/* Preprocessing Specific Styles */
.preprocessing-pipeline {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    padding: 2rem;
    border-radius: 1rem;
    margin: 2rem 0;
    border: 1px solid #bae6fd;
}

.pipeline-flow {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.process-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex: 1;
    min-width: 120px;
    padding: 1.5rem 1rem;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.process-step:hover {
    transform: translateY(-6px);
    border-color: #3b82f6;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
}

.step-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.75rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.contrast-icon {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    color: white;
}

.brightness-icon {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    color: white;
}

.filter-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.edge-icon {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: white;
}

.step-info h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.step-info p {
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.4;
}

.flow-arrow {
    color: #3b82f6;
    font-size: 1.5rem;
    margin: 0 0.5rem;
    animation: pulse 2s infinite;
}

/* Preprocessing Techniques */
.preprocessing-techniques {
    margin: 3rem 0;
}

.techniques-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.technique-card {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.technique-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
}

.technique-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e2e8f0;
}

.technique-icon {
    width: 60px;
    height: 60px;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.contrast-bg {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.brightness-bg {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
}

.filter-bg {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.edge-bg {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.quality-bg {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.technique-title h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.technique-badge {
    background: #dbeafe;
    color: #1e40af;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.technique-content {
    padding: 1.5rem;
}

.technique-content p {
    font-weight: 600;
    color: #374151;
    margin-bottom: 1rem;
}

.technique-features {
    list-style: none;
    margin-bottom: 1.5rem;
}

.technique-features li {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

.technique-params {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.technique-params .param {
    background: #f3f4f6;
    color: #374151;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Parameters Section */
.parameters-section {
    margin-top: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border-radius: 1rem;
    border: 1px solid #f59e0b;
}

.parameters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.param-group h5 {
    font-size: 1rem;
    font-weight: 600;
    color: #92400e;
    margin-bottom: 0.75rem;
}

.param-items {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.param-item {
    background: white;
    color: #92400e;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    border: 1px solid #f59e0b;
}

/* Processing Flow Diagram */
.processing-flow-diagram {
    background: #f8fafc;
    border-radius: 1rem;
    padding: 2rem;
    margin: 2rem 0;
}

.processing-flow-diagram h6 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 2rem;
    text-align: center;
}

.flow-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.flow-stage {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
}

.input-stage {
    border-left: 4px solid #3b82f6;
}

.processing-stage {
    border-left: 4px solid #10b981;
}

.validation-stage {
    border-left: 4px solid #f59e0b;
}

.output-stage {
    border-left: 4px solid #8b5cf6;
}

.stage-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    font-weight: 600;
    color: #1f2937;
}

.stage-header i {
    font-size: 1.25rem;
    color: #6b7280;
}

.stage-content {
    display: grid;
    gap: 0.5rem;
}

.flow-item {
    background: #f8fafc;
    padding: 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    color: #374151;
    text-align: center;
    border: 1px solid #e2e8f0;
}

.flow-arrow-down {
    color: #6b7280;
    font-size: 1.5rem;
    margin: 0.5rem 0;
}

/* Algorithm Specifications */
.algorithm-specs {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    margin: 2rem 0;
    border: 1px solid #e5e7eb;
}

.algorithm-specs h6 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1.5rem;
}

.specs-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.spec-category {
    background: #f8fafc;
    border-radius: 0.75rem;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
}

.spec-category h7 {
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
    display: block;
    margin-bottom: 1rem;
}

.spec-details {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.spec-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e5e7eb;
}

.spec-row:last-child {
    border-bottom: none;
}

.spec-label {
    font-weight: 500;
    color: #6b7280;
    font-size: 0.875rem;
}

.spec-value {
    font-weight: 600;
    color: #1f2937;
    font-size: 0.875rem;
}

/* Performance Benchmarks */
.performance-benchmarks {
    background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
    border-radius: 1rem;
    padding: 2rem;
    margin: 2rem 0;
    border: 1px solid #d1fae5;
}

.performance-benchmarks h6 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1.5rem;
    text-align: center;
}

.benchmark-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.benchmark-item {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #d1fae5;
    transition: transform 0.3s ease;
}

.benchmark-item:hover {
    transform: translateY(-2px);
}

.benchmark-icon {
    background: #10b981;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.25rem;
}

.benchmark-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #059669;
    margin-bottom: 0.5rem;
}

.benchmark-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

/* Segmentation Specific Styles */
.segmentation-pipeline {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    padding: 2rem;
    border-radius: 1rem;
    margin: 2rem 0;
    border: 1px solid #f59e0b;
}

.segmentation-flow {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.seg-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex: 1;
    min-width: 140px;
    padding: 1.5rem 1rem;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.seg-step:hover {
    transform: translateY(-6px);
    border-color: #f59e0b;
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.2);
}

.seg-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.75rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    color: white;
}

.roi-icon {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.contour-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.watershed-icon {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
}

.deform-icon {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.seg-info h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.seg-info p {
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.4;
}

.seg-arrow {
    color: #f59e0b;
    font-size: 1.5rem;
    margin: 0 0.5rem;
    animation: pulse 2s infinite;
}

/* Segmentation Methods */
.segmentation-methods {
    margin: 3rem 0;
}

.methods-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.method-card {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.method-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
}

.method-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e2e8f0;
}

.method-icon {
    width: 60px;
    height: 60px;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.roi-bg {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.contour-bg {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.watershed-bg {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
}

.deform-bg {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.atlas-bg {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.deep-bg {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.method-title h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.method-badge {
    background: #dbeafe;
    color: #1e40af;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.method-content {
    padding: 1.5rem;
}

.method-content p {
    font-weight: 600;
    color: #374151;
    margin-bottom: 1rem;
}

.method-features {
    list-style: none;
    margin-bottom: 1.5rem;
}

.method-features li {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

.method-params {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.method-params .param {
    background: #f3f4f6;
    color: #374151;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Accuracy Metrics */
.accuracy-section {
    margin-top: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
    border-radius: 1rem;
    border: 1px solid #d1fae5;
}

.accuracy-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.accuracy-metric {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #d1fae5;
    transition: transform 0.3s ease;
}

.accuracy-metric:hover {
    transform: translateY(-2px);
}

.accuracy-metric .metric-icon {
    background: #10b981;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.25rem;
}

.accuracy-metric .metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #059669;
    margin-bottom: 0.5rem;
}

.accuracy-metric .metric-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

/* Segmentation Flow Diagram */
.segmentation-flow-diagram {
    background: #f8fafc;
    border-radius: 1rem;
    padding: 2rem;
    margin: 2rem 0;
}

.segmentation-flow-diagram h6 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 2rem;
    text-align: center;
}

.workflow-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.workflow-stage {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
}

.input-stage {
    border-left: 4px solid #3b82f6;
}

.segmentation-stage {
    border-left: 4px solid #10b981;
}

.fusion-stage {
    border-left: 4px solid #f59e0b;
}

.output-stage {
    border-left: 4px solid #8b5cf6;
}

.stage-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    font-weight: 600;
    color: #1f2937;
}

.stage-header i {
    font-size: 1.25rem;
    color: #6b7280;
}

.stage-content {
    display: grid;
    gap: 0.5rem;
}

.workflow-item {
    background: #f8fafc;
    padding: 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    color: #374151;
    text-align: center;
    border: 1px solid #e2e8f0;
}

.workflow-arrow-down {
    color: #6b7280;
    font-size: 1.5rem;
    margin: 0.5rem 0;
}

/* Algorithm Comparison */
.algorithm-comparison {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    margin: 2rem 0;
    border: 1px solid #e5e7eb;
}

.algorithm-comparison h6 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1.5rem;
    text-align: center;
}

.comparison-table {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 2fr;
    gap: 0.5rem;
}

.comparison-header {
    display: contents;
}

.header-cell {
    background: #f3f4f6;
    padding: 1rem;
    font-weight: 600;
    color: #374151;
    text-align: center;
    border-radius: 0.5rem;
    font-size: 0.875rem;
}

.comparison-row {
    display: contents;
}

.method-cell {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.method-cell i {
    color: #6b7280;
    font-size: 1.125rem;
}

.metric-cell {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 0.5rem;
}

.metric-bar {
    height: 8px;
    background: #10b981;
    border-radius: 4px;
    flex: 1;
}

.metric-cell span {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    min-width: 35px;
}

.usecase-cell {
    padding: 1rem;
    background: #f8fafc;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
    display: flex;
    align-items: center;
}

/* Performance Metrics */
.segmentation-performance {
    background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
    border-radius: 1rem;
    padding: 2rem;
    margin: 2rem 0;
    border: 1px solid #d1fae5;
}

.segmentation-performance h6 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1.5rem;
    text-align: center;
}

.performance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.performance-metric {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #d1fae5;
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.performance-metric:hover {
    transform: translateY(-2px);
}

.performance-metric .metric-icon {
    background: #10b981;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.performance-metric .metric-content {
    flex: 1;
}

.performance-metric .metric-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #059669;
    margin-bottom: 0.25rem;
}

.performance-metric .metric-label {
    font-size: 0.875rem;
    color: #374151;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.performance-metric .metric-description {
    font-size: 0.75rem;
    color: #6b7280;
}

/* Clinical Validation */
.clinical-validation {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border-radius: 1rem;
    padding: 2rem;
    margin: 2rem 0;
    border: 1px solid #f59e0b;
}

.clinical-validation h6 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1.5rem;
    text-align: center;
}

.validation-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.validation-item {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #f59e0b;
}

.validation-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    font-weight: 600;
    color: #92400e;
}

.validation-header i {
    font-size: 1.25rem;
    color: #f59e0b;
}

.validation-content p {
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

.validation-content strong {
    color: #374151;
    font-weight: 600;
}

/* Print Styles */
@media print {
    .header, .footer, .tab-navigation, .loading-overlay {
        display: none !important;
    }

    .tab-pane {
        display: block !important;
        page-break-inside: avoid;
    }

    .process-card, .detail-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #e5e7eb;
    }
}
