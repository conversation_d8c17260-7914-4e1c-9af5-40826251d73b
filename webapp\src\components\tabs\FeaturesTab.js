import React, { useState } from 'react';

const FeaturesTab = ({ analysisResults }) => {
  const [activeCategory, setActiveCategory] = useState('texture');

  const featureCategories = [
    {
      id: 'texture',
      title: 'Texture Features',
      icon: 'fas fa-grip-horizontal',
      description: 'GLCM, LBP, and statistical texture descriptors',
      color: '#e74c3c',
      features: [
        { name: 'Contrast', description: 'Measure of local variations in GLCM' },
        { name: 'Dissimilarity', description: 'Linear dependency of gray levels' },
        { name: 'Homogeneity', description: 'Closeness of distribution to diagonal' },
        { name: 'Energy', description: 'Measure of uniformity' },
        { name: 'Correlation', description: 'Linear dependency of gray levels' },
        { name: 'LBP Uniformity', description: 'Local binary pattern uniformity' },
        { name: 'LBP Entropy', description: 'Randomness in LBP distribution' }
      ]
    },
    {
      id: 'shape',
      title: 'Shape Features',
      icon: 'fas fa-shapes',
      description: 'Morphological and geometric descriptors',
      color: '#3498db',
      features: [
        { name: 'Area', description: 'Number of pixels in region' },
        { name: 'Perimeter', description: 'Boundary length of region' },
        { name: 'Eccentricity', description: 'Ratio of foci distance to major axis' },
        { name: 'Solidity', description: 'Ratio of area to convex hull area' },
        { name: 'Extent', description: 'Ratio of area to bounding box' },
        { name: 'Compactness', description: 'Measure of roundness' },
        { name: 'Circularity', description: 'Similarity to perfect circle' },
        { name: 'Equivalent Diameter', description: 'Diameter of circle with same area' }
      ]
    },
    {
      id: 'statistical',
      title: 'Statistical Features',
      icon: 'fas fa-chart-bar',
      description: 'Intensity distribution and statistical moments',
      color: '#27ae60',
      features: [
        { name: 'Mean', description: 'Average intensity value' },
        { name: 'Standard Deviation', description: 'Measure of intensity spread' },
        { name: 'Variance', description: 'Squared standard deviation' },
        { name: 'Skewness', description: 'Asymmetry of distribution' },
        { name: 'Kurtosis', description: 'Tail heaviness of distribution' },
        { name: 'Entropy', description: 'Randomness measure' },
        { name: 'Range', description: 'Difference between max and min' },
        { name: 'Median', description: 'Middle value of sorted data' }
      ]
    }
  ];

  const getFeatureData = () => {
    if (analysisResults?.features) {
      return analysisResults.features;
    }
    return {
      texture_features: {
        contrast: 0.523,
        dissimilarity: 0.421,
        homogeneity: 0.652,
        energy: 0.341,
        correlation: 0.724,
        lbp_uniformity: 0.432,
        lbp_entropy: 5.23
      },
      shape_features: {
        area: 1247,
        perimeter: 142.3,
        eccentricity: 0.634,
        solidity: 0.821,
        extent: 0.723,
        compactness: 1.35,
        circularity: 0.741,
        equivalent_diameter: 39.8
      },
      statistical_features: {
        mean: 0.523,
        std: 0.142,
        variance: 0.020,
        skewness: 0.832,
        kurtosis: 3.42,
        entropy: 6.23,
        range: 0.823,
        median: 0.501
      }
    };
  };

  const featureData = getFeatureData();
  const activeFeatures = featureCategories.find(cat => cat.id === activeCategory);

  return (
    <div className="features-tab">
      <div className="detail-header">
        <i className="fas fa-chart-bar detail-icon"></i>
        <div>
          <h3 className="detail-title">4. Advanced Feature Extraction</h3>
          <p className="detail-subtitle">
            Extract comprehensive texture, shape, and statistical features for tumor characterization
          </p>
        </div>
      </div>

      <div className="feature-categories">
        <div className="category-navigation">
          {featureCategories.map((category) => (
            <button
              key={category.id}
              className={`category-btn ${activeCategory === category.id ? 'active' : ''}`}
              onClick={() => setActiveCategory(category.id)}
            >
              <div className="category-icon" style={{ background: category.color }}>
                <i className={category.icon}></i>
              </div>
              <div className="category-info">
                <h4>{category.title}</h4>
                <p>{category.description}</p>
              </div>
            </button>
          ))}
        </div>

        <div className="feature-details">
          {activeFeatures && (
            <div className="feature-category-details">
              <div className="category-header">
                <div className="category-icon-large" style={{ background: activeFeatures.color }}>
                  <i className={activeFeatures.icon}></i>
                </div>
                <div>
                  <h3>{activeFeatures.title}</h3>
                  <p>{activeFeatures.description}</p>
                </div>
              </div>

              <div className="features-grid">
                {activeFeatures.features.map((feature, index) => (
                  <div key={index} className="feature-card">
                    <div className="feature-header">
                      <h4>{feature.name}</h4>
                    </div>
                    <div className="feature-content">
                      <p>{feature.description}</p>
                      <div className="feature-value">
                        <span className="value-label">Current Value:</span>
                        <span className="value-number">
                          {featureData[`${activeCategory}_features`]?.[feature.name.toLowerCase()] || 
                           featureData[`${activeCategory}_features`]?.[feature.name.toLowerCase().replace(' ', '_')] || 
                           '0.000'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="feature-summary">
        <h4>Feature Extraction Summary</h4>
        <div className="summary-grid">
          <div className="summary-card">
            <div className="summary-icon">
              <i className="fas fa-grip-horizontal"></i>
            </div>
            <div className="summary-content">
              <div className="summary-value">
                {Object.keys(featureData.texture_features || {}).length}
              </div>
              <div className="summary-label">Texture Features</div>
            </div>
          </div>
          <div className="summary-card">
            <div className="summary-icon">
              <i className="fas fa-shapes"></i>
            </div>
            <div className="summary-content">
              <div className="summary-value">
                {Object.keys(featureData.shape_features || {}).length}
              </div>
              <div className="summary-label">Shape Features</div>
            </div>
          </div>
          <div className="summary-card">
            <div className="summary-icon">
              <i className="fas fa-chart-bar"></i>
            </div>
            <div className="summary-content">
              <div className="summary-value">
                {Object.keys(featureData.statistical_features || {}).length}
              </div>
              <div className="summary-label">Statistical Features</div>
            </div>
          </div>
          <div className="summary-card">
            <div className="summary-icon">
              <i className="fas fa-check-circle"></i>
            </div>
            <div className="summary-content">
              <div className="summary-value">
                {Object.keys(featureData.texture_features || {}).length + 
                 Object.keys(featureData.shape_features || {}).length + 
                 Object.keys(featureData.statistical_features || {}).length}
              </div>
              <div className="summary-label">Total Features</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeaturesTab;