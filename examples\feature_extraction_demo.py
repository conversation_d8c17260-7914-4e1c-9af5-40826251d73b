"""
Feature Extraction Demo
======================

This script demonstrates comprehensive feature extraction capabilities
for brain tumor characterization from MRI images.

Author: Dr. <PERSON>
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, confusion_matrix
import seaborn as sns
import pandas as pd

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from feature_extraction import (
    FeatureExtractor, FeatureSelector, FeatureAnalyzer,
    TextureFeatures, ShapeFeatures, StatisticalFeatures
)


def create_synthetic_mri_data(n_samples=200, image_size=(64, 64)):
    """
    Create synthetic MRI data for demonstration.
    
    Args:
        n_samples: Number of samples to generate
        image_size: Size of each image
        
    Returns:
        Tuple of (images, masks, labels)
    """
    np.random.seed(42)
    
    images = []
    masks = []
    labels = []
    
    for i in range(n_samples):
        # Create base image with noise
        image = np.random.rand(*image_size) * 100 + 50
        
        # Add some structure
        x, y = np.meshgrid(np.arange(image_size[0]), np.arange(image_size[1]))
        center_x, center_y = image_size[0]//2, image_size[1]//2
        
        # Create tumor-like region
        if i < n_samples // 2:  # Tumor cases
            # Irregular tumor shape
            tumor_radius = np.random.randint(8, 20)
            tumor_mask = ((x - center_x)**2 + (y - center_y)**2) < tumor_radius**2
            
            # Add irregularity
            noise = np.random.rand(*image_size) * 0.3
            tumor_mask = tumor_mask & (noise > 0.15)
            
            # Modify image in tumor region
            image[tumor_mask] = image[tumor_mask] * 1.5 + 30
            
            # Create mask
            mask = tumor_mask.astype(np.uint8)
            labels.append(1)  # Tumor
        else:  # Normal cases
            # Create normal tissue region
            normal_radius = np.random.randint(10, 15)
            normal_mask = ((x - center_x)**2 + (y - center_y)**2) < normal_radius**2
            
            # Create mask
            mask = normal_mask.astype(np.uint8)
            labels.append(0)  # Normal
        
        images.append(image)
        masks.append(mask)
    
    return images, masks, labels


def demonstrate_texture_features():
    """Demonstrate texture feature extraction."""
    print("=" * 60)
    print("TEXTURE FEATURES DEMONSTRATION")
    print("=" * 60)
    
    # Create sample data
    image = np.random.rand(100, 100) * 255
    mask = np.zeros((100, 100), dtype=np.uint8)
    
    # Create circular mask
    y, x = np.ogrid[:100, :100]
    center = (50, 50)
    radius = 20
    mask_circle = (x - center[0])**2 + (y - center[1])**2 <= radius**2
    mask[mask_circle] = 1
    
    # Extract texture features
    texture_extractor = TextureFeatures()
    
    print("\n1. GLCM Features:")
    glcm_features = texture_extractor.extract_glcm_features(image, mask)
    for name, value in list(glcm_features.items())[:8]:
        print(f"   {name}: {value:.4f}")
    
    print("\n2. Local Binary Pattern Features:")
    lbp_features = texture_extractor.extract_lbp_features(image, mask)
    for name, value in lbp_features.items():
        print(f"   {name}: {value:.4f}")
    
    print("\n3. Edge-based Features:")
    edge_features = texture_extractor.extract_edge_features(image, mask)
    for name, value in list(edge_features.items())[:6]:
        print(f"   {name}: {value:.4f}")
    
    print(f"\nTotal texture features extracted: {len(glcm_features) + len(lbp_features) + len(edge_features)}")


def demonstrate_shape_features():
    """Demonstrate shape feature extraction."""
    print("\n" + "=" * 60)
    print("SHAPE FEATURES DEMONSTRATION")
    print("=" * 60)
    
    # Create irregular shape
    mask = np.zeros((100, 100), dtype=np.uint8)
    
    # Create complex shape
    y, x = np.ogrid[:100, :100]
    # Multiple overlapping circles to create irregular shape
    mask1 = (x - 40)**2 + (y - 40)**2 <= 15**2
    mask2 = (x - 60)**2 + (y - 45)**2 <= 20**2
    mask3 = (x - 50)**2 + (y - 65)**2 <= 12**2
    
    mask = (mask1 | mask2 | mask3).astype(np.uint8)
    
    # Extract shape features
    shape_extractor = ShapeFeatures()
    
    print("\n1. Basic Shape Features:")
    basic_features = shape_extractor.extract_basic_shape_features(mask)
    for name, value in basic_features.items():
        print(f"   {name}: {value:.4f}")
    
    print("\n2. Advanced Shape Features:")
    advanced_features = shape_extractor.extract_advanced_shape_features(mask)
    for name, value in advanced_features.items():
        print(f"   {name}: {value:.4f}")
    
    print(f"\nTotal shape features extracted: {len(basic_features) + len(advanced_features)}")


def demonstrate_statistical_features():
    """Demonstrate statistical feature extraction."""
    print("\n" + "=" * 60)
    print("STATISTICAL FEATURES DEMONSTRATION")
    print("=" * 60)
    
    # Create sample image with different intensity regions
    image = np.random.rand(100, 100) * 255
    
    # Add structured patterns
    image[20:40, 20:40] = 200  # Bright region
    image[60:80, 60:80] = 50   # Dark region
    
    # Create mask
    mask = np.ones((100, 100), dtype=np.uint8)
    mask[10:90, 10:90] = 1
    
    # Extract statistical features
    stats_extractor = StatisticalFeatures()
    
    print("\n1. First-order Statistics:")
    first_order = stats_extractor.extract_first_order_statistics(image, mask)
    for name, value in list(first_order.items())[:10]:
        print(f"   {name}: {value:.4f}")
    
    print("\n2. Second-order Statistics:")
    second_order = stats_extractor.extract_second_order_statistics(image, mask)
    for name, value in second_order.items():
        print(f"   {name}: {value:.4f}")
    
    print(f"\nTotal statistical features extracted: {len(first_order) + len(second_order)}")


def demonstrate_feature_selection():
    """Demonstrate feature selection methods."""
    print("\n" + "=" * 60)
    print("FEATURE SELECTION DEMONSTRATION")
    print("=" * 60)
    
    # Generate synthetic dataset
    print("\nGenerating synthetic MRI dataset...")
    images, masks, labels = create_synthetic_mri_data(n_samples=100, image_size=(32, 32))
    
    # Extract features
    print("Extracting features from all samples...")
    extractor = FeatureExtractor()
    features_list = extractor.extract_features_batch(images, masks)
    
    # Convert to matrix format
    feature_names = list(features_list[0].keys())
    X = np.array([[features.get(name, 0) for name in feature_names] for features in features_list])
    X = np.nan_to_num(X)  # Replace NaN with 0
    y = np.array(labels)
    
    print(f"Dataset shape: {X.shape}")
    print(f"Number of features: {len(feature_names)}")
    print(f"Number of samples: {len(y)}")
    print(f"Class distribution: {np.bincount(y)}")
    
    # Feature selection
    selector = FeatureSelector()
    
    print("\n1. Univariate Feature Selection:")
    X_univariate, selected_indices = selector.select_features_univariate(X, y, k=15)
    print(f"   Selected {len(selected_indices)} features")
    print("   Top 5 selected features:")
    for i, idx in enumerate(selected_indices[:5]):
        print(f"   {i+1}. {feature_names[idx]}")
    
    print("\n2. Recursive Feature Elimination:")
    X_rfe, selected_indices_rfe = selector.select_features_rfe(X, y, n_features=15)
    print(f"   Selected {len(selected_indices_rfe)} features")
    print("   Top 5 selected features:")
    for i, idx in enumerate(selected_indices_rfe[:5]):
        print(f"   {i+1}. {feature_names[idx]}")
    
    print("\n3. Feature Importance Selection:")
    X_importance, selected_indices_imp = selector.select_features_importance(X, y, threshold=0.01)
    print(f"   Selected {len(selected_indices_imp)} features")
    print("   Top 5 important features:")
    for i, idx in enumerate(selected_indices_imp[:5]):
        print(f"   {i+1}. {feature_names[idx]}")
    
    return X, y, feature_names, X_univariate, selected_indices


def demonstrate_classification_performance(X, y, feature_names, X_selected, selected_indices):
    """Demonstrate classification performance with and without feature selection."""
    print("\n" + "=" * 60)
    print("CLASSIFICATION PERFORMANCE COMPARISON")
    print("=" * 60)
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    X_sel_train, X_sel_test, _, _ = train_test_split(X_selected, y, test_size=0.3, random_state=42)
    
    # Train classifier with all features
    print("\n1. Classification with ALL features:")
    clf_all = RandomForestClassifier(n_estimators=100, random_state=42)
    clf_all.fit(X_train, y_train)
    score_all = clf_all.score(X_test, y_test)
    print(f"   Accuracy: {score_all:.4f}")
    print(f"   Number of features: {X.shape[1]}")
    
    # Train classifier with selected features
    print("\n2. Classification with SELECTED features:")
    clf_selected = RandomForestClassifier(n_estimators=100, random_state=42)
    clf_selected.fit(X_sel_train, y_train)
    score_selected = clf_selected.score(X_sel_test, y_test)
    print(f"   Accuracy: {score_selected:.4f}")
    print(f"   Number of features: {X_selected.shape[1]}")
    
    # Performance comparison
    print(f"\n3. Performance Comparison:")
    print(f"   Accuracy improvement: {score_selected - score_all:.4f}")
    print(f"   Feature reduction: {(1 - X_selected.shape[1]/X.shape[1])*100:.1f}%")
    
    # Feature importance from full model
    print(f"\n4. Top 10 Important Features (from full model):")
    importances = clf_all.feature_importances_
    indices = np.argsort(importances)[::-1]
    for i in range(min(10, len(indices))):
        idx = indices[i]
        print(f"   {i+1}. {feature_names[idx]}: {importances[idx]:.4f}")


def demonstrate_feature_analysis():
    """Demonstrate feature analysis and visualization."""
    print("\n" + "=" * 60)
    print("FEATURE ANALYSIS DEMONSTRATION")
    print("=" * 60)
    
    # Generate synthetic dataset
    images, masks, labels = create_synthetic_mri_data(n_samples=50, image_size=(32, 32))
    
    # Extract features
    extractor = FeatureExtractor()
    features_list = extractor.extract_features_batch(images, masks)
    
    # Convert to dictionary format for analysis
    feature_names = list(features_list[0].keys())
    features_dict = {}
    for name in feature_names:
        features_dict[name] = [features.get(name, 0) for features in features_list]
    
    # Feature analysis
    analyzer = FeatureAnalyzer()
    
    print("\n1. Feature Distribution Analysis:")
    sample_features = ['area', 'mean', 'std', 'glcm_contrast_mean']
    for feature in sample_features:
        if feature in features_dict:
            analysis = analyzer.analyze_feature_distribution(features_dict, feature)
            print(f"\n   {feature}:")
            print(f"     Mean: {analysis['mean']:.4f}")
            print(f"     Std: {analysis['std']:.4f}")
            print(f"     Range: [{analysis['min']:.4f}, {analysis['max']:.4f}]")
            print(f"     Skewness: {analysis['skewness']:.4f}")
    
    print("\n2. Feature Comparison between Groups:")
    comparison = analyzer.compare_feature_groups(features_dict, labels)
    
    print(f"\n   Sample Feature Comparison (area):")
    if 'area' in comparison:
        for label, stats in comparison['area'].items():
            group_name = "Tumor" if label == 1 else "Normal"
            print(f"     {group_name}: mean={stats['mean']:.2f}, std={stats['std']:.2f}, n={stats['count']}")


def create_comprehensive_report():
    """Create a comprehensive feature extraction report."""
    print("\n" + "=" * 80)
    print("COMPREHENSIVE FEATURE EXTRACTION REPORT")
    print("=" * 80)
    
    # Generate larger dataset for comprehensive analysis
    print("\nGenerating comprehensive dataset...")
    images, masks, labels = create_synthetic_mri_data(n_samples=200, image_size=(64, 64))
    
    # Extract all features
    print("Extracting comprehensive feature set...")
    extractor = FeatureExtractor()
    features_list = extractor.extract_features_batch(images, masks)
    
    # Convert to matrix format
    feature_names = list(features_list[0].keys())
    X = np.array([[features.get(name, 0) for name in feature_names] for features in features_list])
    X = np.nan_to_num(X)
    y = np.array(labels)
    
    print(f"\nDataset Summary:")
    print(f"  Total samples: {len(y)}")
    print(f"  Total features: {len(feature_names)}")
    print(f"  Tumor samples: {np.sum(y == 1)}")
    print(f"  Normal samples: {np.sum(y == 0)}")
    
    # Feature categories
    texture_features = [name for name in feature_names if any(x in name for x in ['glcm', 'lbp', 'edge'])]
    shape_features = [name for name in feature_names if any(x in name for x in ['area', 'perimeter', 'circularity', 'eccentricity', 'hu_moment'])]
    statistical_features = [name for name in feature_names if any(x in name for x in ['mean', 'std', 'variance', 'entropy', 'percentile'])]
    
    print(f"\nFeature Categories:")
    print(f"  Texture features: {len(texture_features)}")
    print(f"  Shape features: {len(shape_features)}")
    print(f"  Statistical features: {len(statistical_features)}")
    print(f"  Other features: {len(feature_names) - len(texture_features) - len(shape_features) - len(statistical_features)}")
    
    # Feature selection comparison
    print(f"\nFeature Selection Comparison:")
    selector = FeatureSelector()
    
    # Univariate selection
    X_uni, indices_uni = selector.select_features_univariate(X, y, k=20)
    print(f"  Univariate selection: {len(indices_uni)} features selected")
    
    # RFE selection
    X_rfe, indices_rfe = selector.select_features_rfe(X, y, n_features=20)
    print(f"  RFE selection: {len(indices_rfe)} features selected")
    
    # Importance selection
    X_imp, indices_imp = selector.select_features_importance(X, y, threshold=0.01)
    print(f"  Importance selection: {len(indices_imp)} features selected")
    
    # Classification performance
    print(f"\nClassification Performance:")
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    # All features
    clf_all = RandomForestClassifier(n_estimators=100, random_state=42)
    clf_all.fit(X_train, y_train)
    score_all = clf_all.score(X_test, y_test)
    print(f"  All features ({X.shape[1]}): {score_all:.4f}")
    
    # Selected features
    X_sel_train, X_sel_test, _, _ = train_test_split(X_uni, y, test_size=0.3, random_state=42)
    clf_sel = RandomForestClassifier(n_estimators=100, random_state=42)
    clf_sel.fit(X_sel_train, y_train)
    score_sel = clf_sel.score(X_sel_test, y_test)
    print(f"  Selected features ({X_uni.shape[1]}): {score_sel:.4f}")
    
    # Top features
    print(f"\nTop 10 Most Important Features:")
    importances = clf_all.feature_importances_
    indices = np.argsort(importances)[::-1]
    for i in range(min(10, len(indices))):
        idx = indices[i]
        print(f"  {i+1:2d}. {feature_names[idx]:<30} {importances[idx]:.4f}")
    
    print(f"\nFeature Extraction Report Complete!")
    print("=" * 80)


def main():
    """Main demonstration function."""
    print("Brain Tumor Feature Extraction Demonstration")
    print("Author: Dr. Mohammed Yagoub Esmail")
    print("=" * 80)
    
    # Individual feature type demonstrations
    demonstrate_texture_features()
    demonstrate_shape_features()
    demonstrate_statistical_features()
    
    # Feature selection demonstration
    X, y, feature_names, X_selected, selected_indices = demonstrate_feature_selection()
    
    # Classification performance
    demonstrate_classification_performance(X, y, feature_names, X_selected, selected_indices)
    
    # Feature analysis
    demonstrate_feature_analysis()
    
    # Comprehensive report
    create_comprehensive_report()
    
    print("\n" + "=" * 80)
    print("DEMONSTRATION COMPLETE")
    print("=" * 80)
    print("\nKey Takeaways:")
    print("1. Comprehensive feature extraction covers texture, shape, and statistical features")
    print("2. Feature selection significantly reduces dimensionality while maintaining performance")
    print("3. Different selection methods provide different perspectives on feature importance")
    print("4. Proper feature engineering is crucial for successful tumor characterization")
    print("5. The system can handle real-world medical imaging scenarios")


if __name__ == "__main__":
    main()"""
Feature Extraction Demo
======================

This script demonstrates comprehensive feature extraction capabilities
for brain tumor characterization from MRI images.

Author: Dr. Mohammed Yagoub Esmail
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, confusion_matrix
import seaborn as sns
import pandas as pd

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from feature_extraction import (
    FeatureExtractor, FeatureSelector, FeatureAnalyzer,
    TextureFeatures, ShapeFeatures, StatisticalFeatures
)


def create_synthetic_mri_data(n_samples=200, image_size=(64, 64)):
    """
    Create synthetic MRI data for demonstration.
    
    Args:
        n_samples: Number of samples to generate
        image_size: Size of each image
        
    Returns:
        Tuple of (images, masks, labels)
    """
    np.random.seed(42)
    
    images = []
    masks = []
    labels = []
    
    for i in range(n_samples):
        # Create base image with noise
        image = np.random.rand(*image_size) * 100 + 50
        
        # Add some structure
        x, y = np.meshgrid(np.arange(image_size[0]), np.arange(image_size[1]))
        center_x, center_y = image_size[0]//2, image_size[1]//2
        
        # Create tumor-like region
        if i < n_samples // 2:  # Tumor cases
            # Irregular tumor shape
            tumor_radius = np.random.randint(8, 20)
            tumor_mask = ((x - center_x)**2 + (y - center_y)**2) < tumor_radius**2
            
            # Add irregularity
            noise = np.random.rand(*image_size) * 0.3
            tumor_mask = tumor_mask & (noise > 0.15)
            
            # Modify image in tumor region
            image[tumor_mask] = image[tumor_mask] * 1.5 + 30
            
            # Create mask
            mask = tumor_mask.astype(np.uint8)
            labels.append(1)  # Tumor
        else:  # Normal cases
            # Create normal tissue region
            normal_radius = np.random.randint(10, 15)
            normal_mask = ((x - center_x)**2 + (y - center_y)**2) < normal_radius**2
            
            # Create mask
            mask = normal_mask.astype(np.uint8)
            labels.append(0)  # Normal
        
        images.append(image)
        masks.append(mask)
    
    return images, masks, labels


def demonstrate_texture_features():
    """Demonstrate texture feature extraction."""
    print("=" * 60)
    print("TEXTURE FEATURES DEMONSTRATION")
    print("=" * 60)
    
    # Create sample data
    image = np.random.rand(100, 100) * 255
    mask = np.zeros((100, 100), dtype=np.uint8)
    
    # Create circular mask
    y, x = np.ogrid[:100, :100]
    center = (50, 50)
    radius = 20
    mask_circle = (x - center[0])**2 + (y - center[1])**2 <= radius**2
    mask[mask_circle] = 1
    
    # Extract texture features
    texture_extractor = TextureFeatures()
    
    print("\n1. GLCM Features:")
    glcm_features = texture_extractor.extract_glcm_features(image, mask)
    for name, value in list(glcm_features.items())[:8]:
        print(f"   {name}: {value:.4f}")
    
    print("\n2. Local Binary Pattern Features:")
    lbp_features = texture_extractor.extract_lbp_features(image, mask)
    for name, value in lbp_features.items():
        print(f"   {name}: {value:.4f}")
    
    print("\n3. Edge-based Features:")
    edge_features = texture_extractor.extract_edge_features(image, mask)
    for name, value in list(edge_features.items())[:6]:
        print(f"   {name}: {value:.4f}")
    
    print(f"\nTotal texture features extracted: {len(glcm_features) + len(lbp_features) + len(edge_features)}")


def demonstrate_shape_features():
    """Demonstrate shape feature extraction."""
    print("\n" + "=" * 60)
    print("SHAPE FEATURES DEMONSTRATION")
    print("=" * 60)
    
    # Create irregular shape
    mask = np.zeros((100, 100), dtype=np.uint8)
    
    # Create complex shape
    y, x = np.ogrid[:100, :100]
    # Multiple overlapping circles to create irregular shape
    mask1 = (x - 40)**2 + (y - 40)**2 <= 15**2
    mask2 = (x - 60)**2 + (y - 45)**2 <= 20**2
    mask3 = (x - 50)**2 + (y - 65)**2 <= 12**2
    
    mask = (mask1 | mask2 | mask3).astype(np.uint8)
    
    # Extract shape features
    shape_extractor = ShapeFeatures()
    
    print("\n1. Basic Shape Features:")
    basic_features = shape_extractor.extract_basic_shape_features(mask)
    for name, value in basic_features.items():
        print(f"   {name}: {value:.4f}")
    
    print("\n2. Advanced Shape Features:")
    advanced_features = shape_extractor.extract_advanced_shape_features(mask)
    for name, value in advanced_features.items():
        print(f"   {name}: {value:.4f}")
    
    print(f"\nTotal shape features extracted: {len(basic_features) + len(advanced_features)}")


def demonstrate_statistical_features():
    """Demonstrate statistical feature extraction."""
    print("\n" + "=" * 60)
    print("STATISTICAL FEATURES DEMONSTRATION")
    print("=" * 60)
    
    # Create sample image with different intensity regions
    image = np.random.rand(100, 100) * 255
    
    # Add structured patterns
    image[20:40, 20:40] = 200  # Bright region
    image[60:80, 60:80] = 50   # Dark region
    
    # Create mask
    mask = np.ones((100, 100), dtype=np.uint8)
    mask[10:90, 10:90] = 1
    
    # Extract statistical features
    stats_extractor = StatisticalFeatures()
    
    print("\n1. First-order Statistics:")
    first_order = stats_extractor.extract_first_order_statistics(image, mask)
    for name, value in list(first_order.items())[:10]:
        print(f"   {name}: {value:.4f}")
    
    print("\n2. Second-order Statistics:")
    second_order = stats_extractor.extract_second_order_statistics(image, mask)
    for name, value in second_order.items():
        print(f"   {name}: {value:.4f}")
    
    print(f"\nTotal statistical features extracted: {len(first_order) + len(second_order)}")


def demonstrate_feature_selection():
    """Demonstrate feature selection methods."""
    print("\n" + "=" * 60)
    print("FEATURE SELECTION DEMONSTRATION")
    print("=" * 60)
    
    # Generate synthetic dataset
    print("\nGenerating synthetic MRI dataset...")
    images, masks, labels = create_synthetic_mri_data(n_samples=100, image_size=(32, 32))
    
    # Extract features
    print("Extracting features from all samples...")
    extractor = FeatureExtractor()
    features_list = extractor.extract_features_batch(images, masks)
    
    # Convert to matrix format
    feature_names = list(features_list[0].keys())
    X = np.array([[features.get(name, 0) for name in feature_names] for features in features_list])
    X = np.nan_to_num(X)  # Replace NaN with 0
    y = np.array(labels)
    
    print(f"Dataset shape: {X.shape}")
    print(f"Number of features: {len(feature_names)}")
    print(f"Number of samples: {len(y)}")
    print(f"Class distribution: {np.bincount(y)}")
    
    # Feature selection
    selector = FeatureSelector()
    
    print("\n1. Univariate Feature Selection:")
    X_univariate, selected_indices = selector.select_features_univariate(X, y, k=15)
    print(f"   Selected {len(selected_indices)} features")
    print("   Top 5 selected features:")
    for i, idx in enumerate(selected_indices[:5]):
        print(f"   {i+1}. {feature_names[idx]}")
    
    print("\n2. Recursive Feature Elimination:")
    X_rfe, selected_indices_rfe = selector.select_features_rfe(X, y, n_features=15)
    print(f"   Selected {len(selected_indices_rfe)} features")
    print("   Top 5 selected features:")
    for i, idx in enumerate(selected_indices_rfe[:5]):
        print(f"   {i+1}. {feature_names[idx]}")
    
    print("\n3. Feature Importance Selection:")
    X_importance, selected_indices_imp = selector.select_features_importance(X, y, threshold=0.01)
    print(f"   Selected {len(selected_indices_imp)} features")
    print("   Top 5 important features:")
    for i, idx in enumerate(selected_indices_imp[:5]):
        print(f"   {i+1}. {feature_names[idx]}")
    
    return X, y, feature_names, X_univariate, selected_indices


def demonstrate_classification_performance(X, y, feature_names, X_selected, selected_indices):
    """Demonstrate classification performance with and without feature selection."""
    print("\n" + "=" * 60)
    print("CLASSIFICATION PERFORMANCE COMPARISON")
    print("=" * 60)
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    X_sel_train, X_sel_test, _, _ = train_test_split(X_selected, y, test_size=0.3, random_state=42)
    
    # Train classifier with all features
    print("\n1. Classification with ALL features:")
    clf_all = RandomForestClassifier(n_estimators=100, random_state=42)
    clf_all.fit(X_train, y_train)
    score_all = clf_all.score(X_test, y_test)
    print(f"   Accuracy: {score_all:.4f}")
    print(f"   Number of features: {X.shape[1]}")
    
    # Train classifier with selected features
    print("\n2. Classification with SELECTED features:")
    clf_selected = RandomForestClassifier(n_estimators=100, random_state=42)
    clf_selected.fit(X_sel_train, y_train)
    score_selected = clf_selected.score(X_sel_test, y_test)
    print(f"   Accuracy: {score_selected:.4f}")
    print(f"   Number of features: {X_selected.shape[1]}")
    
    # Performance comparison
    print(f"\n3. Performance Comparison:")
    print(f"   Accuracy improvement: {score_selected - score_all:.4f}")
    print(f"   Feature reduction: {(1 - X_selected.shape[1]/X.shape[1])*100:.1f}%")
    
    # Feature importance from full model
    print(f"\n4. Top 10 Important Features (from full model):")
    importances = clf_all.feature_importances_
    indices = np.argsort(importances)[::-1]
    for i in range(min(10, len(indices))):
        idx = indices[i]
        print(f"   {i+1}. {feature_names[idx]}: {importances[idx]:.4f}")


def demonstrate_feature_analysis():
    """Demonstrate feature analysis and visualization."""
    print("\n" + "=" * 60)
    print("FEATURE ANALYSIS DEMONSTRATION")
    print("=" * 60)
    
    # Generate synthetic dataset
    images, masks, labels = create_synthetic_mri_data(n_samples=50, image_size=(32, 32))
    
    # Extract features
    extractor = FeatureExtractor()
    features_list = extractor.extract_features_batch(images, masks)
    
    # Convert to dictionary format for analysis
    feature_names = list(features_list[0].keys())
    features_dict = {}
    for name in feature_names:
        features_dict[name] = [features.get(name, 0) for features in features_list]
    
    # Feature analysis
    analyzer = FeatureAnalyzer()
    
    print("\n1. Feature Distribution Analysis:")
    sample_features = ['area', 'mean', 'std', 'glcm_contrast_mean']
    for feature in sample_features:
        if feature in features_dict:
            analysis = analyzer.analyze_feature_distribution(features_dict, feature)
            print(f"\n   {feature}:")
            print(f"     Mean: {analysis['mean']:.4f}")
            print(f"     Std: {analysis['std']:.4f}")
            print(f"     Range: [{analysis['min']:.4f}, {analysis['max']:.4f}]")
            print(f"     Skewness: {analysis['skewness']:.4f}")
    
    print("\n2. Feature Comparison between Groups:")
    comparison = analyzer.compare_feature_groups(features_dict, labels)
    
    print(f"\n   Sample Feature Comparison (area):")
    if 'area' in comparison:
        for label, stats in comparison['area'].items():
            group_name = "Tumor" if label == 1 else "Normal"
            print(f"     {group_name}: mean={stats['mean']:.2f}, std={stats['std']:.2f}, n={stats['count']}")


def create_comprehensive_report():
    """Create a comprehensive feature extraction report."""
    print("\n" + "=" * 80)
    print("COMPREHENSIVE FEATURE EXTRACTION REPORT")
    print("=" * 80)
    
    # Generate larger dataset for comprehensive analysis
    print("\nGenerating comprehensive dataset...")
    images, masks, labels = create_synthetic_mri_data(n_samples=200, image_size=(64, 64))
    
    # Extract all features
    print("Extracting comprehensive feature set...")
    extractor = FeatureExtractor()
    features_list = extractor.extract_features_batch(images, masks)
    
    # Convert to matrix format
    feature_names = list(features_list[0].keys())
    X = np.array([[features.get(name, 0) for name in feature_names] for features in features_list])
    X = np.nan_to_num(X)
    y = np.array(labels)
    
    print(f"\nDataset Summary:")
    print(f"  Total samples: {len(y)}")
    print(f"  Total features: {len(feature_names)}")
    print(f"  Tumor samples: {np.sum(y == 1)}")
    print(f"  Normal samples: {np.sum(y == 0)}")
    
    # Feature categories
    texture_features = [name for name in feature_names if any(x in name for x in ['glcm', 'lbp', 'edge'])]
    shape_features = [name for name in feature_names if any(x in name for x in ['area', 'perimeter', 'circularity', 'eccentricity', 'hu_moment'])]
    statistical_features = [name for name in feature_names if any(x in name for x in ['mean', 'std', 'variance', 'entropy', 'percentile'])]
    
    print(f"\nFeature Categories:")
    print(f"  Texture features: {len(texture_features)}")
    print(f"  Shape features: {len(shape_features)}")
    print(f"  Statistical features: {len(statistical_features)}")
    print(f"  Other features: {len(feature_names) - len(texture_features) - len(shape_features) - len(statistical_features)}")
    
    # Feature selection comparison
    print(f"\nFeature Selection Comparison:")
    selector = FeatureSelector()
    
    # Univariate selection
    X_uni, indices_uni = selector.select_features_univariate(X, y, k=20)
    print(f"  Univariate selection: {len(indices_uni)} features selected")
    
    # RFE selection
    X_rfe, indices_rfe = selector.select_features_rfe(X, y, n_features=20)
    print(f"  RFE selection: {len(indices_rfe)} features selected")
    
    # Importance selection
    X_imp, indices_imp = selector.select_features_importance(X, y, threshold=0.01)
    print(f"  Importance selection: {len(indices_imp)} features selected")
    
    # Classification performance
    print(f"\nClassification Performance:")
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    # All features
    clf_all = RandomForestClassifier(n_estimators=100, random_state=42)
    clf_all.fit(X_train, y_train)
    score_all = clf_all.score(X_test, y_test)
    print(f"  All features ({X.shape[1]}): {score_all:.4f}")
    
    # Selected features
    X_sel_train, X_sel_test, _, _ = train_test_split(X_uni, y, test_size=0.3, random_state=42)
    clf_sel = RandomForestClassifier(n_estimators=100, random_state=42)
    clf_sel.fit(X_sel_train, y_train)
    score_sel = clf_sel.score(X_sel_test, y_test)
    print(f"  Selected features ({X_uni.shape[1]}): {score_sel:.4f}")
    
    # Top features
    print(f"\nTop 10 Most Important Features:")
    importances = clf_all.feature_importances_
    indices = np.argsort(importances)[::-1]
    for i in range(min(10, len(indices))):
        idx = indices[i]
        print(f"  {i+1:2d}. {feature_names[idx]:<30} {importances[idx]:.4f}")
    
    print(f"\nFeature Extraction Report Complete!")
    print("=" * 80)


def main():
    """Main demonstration function."""
    print("Brain Tumor Feature Extraction Demonstration")
    print("Author: Dr. Mohammed Yagoub Esmail")
    print("=" * 80)
    
    # Individual feature type demonstrations
    demonstrate_texture_features()
    demonstrate_shape_features()
    demonstrate_statistical_features()
    
    # Feature selection demonstration
    X, y, feature_names, X_selected, selected_indices = demonstrate_feature_selection()
    
    # Classification performance
    demonstrate_classification_performance(X, y, feature_names, X_selected, selected_indices)
    
    # Feature analysis
    demonstrate_feature_analysis()
    
    # Comprehensive report
    create_comprehensive_report()
    
    print("\n" + "=" * 80)
    print("DEMONSTRATION COMPLETE")
    print("=" * 80)
    print("\nKey Takeaways:")
    print("1. Comprehensive feature extraction covers texture, shape, and statistical features")
    print("2. Feature selection significantly reduces dimensionality while maintaining performance")
    print("3. Different selection methods provide different perspectives on feature importance")
    print("4. Proper feature engineering is crucial for successful tumor characterization")
    print("5. The system can handle real-world medical imaging scenarios")


if __name__ == "__main__":
    main()