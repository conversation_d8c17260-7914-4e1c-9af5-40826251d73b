import React from 'react';

const PreprocessingTab = ({ analysisResults }) => {
  const preprocessingMethods = [
    {
      id: 'contrast',
      title: 'Contrast Enhancement',
      icon: 'fas fa-adjust',
      description: 'CLAHE and histogram equalization',
      color: '#3498db'
    },
    {
      id: 'brightness',
      title: 'Brightness Adaptation',
      icon: 'fas fa-sun',
      description: 'Gamma correction and linear adjustment',
      color: '#f39c12'
    },
    {
      id: 'filtering',
      title: 'Noise Filtering',
      icon: 'fas fa-filter',
      description: 'Gaussian, median, and bilateral filtering',
      color: '#27ae60'
    },
    {
      id: 'enhancement',
      title: 'Edge Enhancement',
      icon: 'fas fa-vector-square',
      description: 'Unsharp masking and edge detection',
      color: '#e74c3c'
    }
  ];

  return (
    <div className="preprocessing-tab">
      <div className="detail-header">
        <i className="fas fa-cog detail-icon"></i>
        <div>
          <h3 className="detail-title">2. Advanced Image Preprocessing</h3>
          <p className="detail-subtitle">
            Enhance image quality through contrast adaptation, noise reduction, and edge enhancement
          </p>
        </div>
      </div>

      <div className="preprocessing-methods">
        <h4>Preprocessing Techniques</h4>
        <div className="methods-grid">
          {preprocessingMethods.map((method) => (
            <div key={method.id} className="method-card">
              <div className="method-header">
                <div className="method-icon" style={{ background: method.color }}>
                  <i className={method.icon}></i>
                </div>
                <div>
                  <h5>{method.title}</h5>
                  <p>{method.description}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="preprocessing-pipeline">
        <h4>Preprocessing Pipeline</h4>
        <div className="pipeline-flow">
          <div className="flow-step">
            <div className="flow-icon" style={{ background: '#3498db' }}>
              <i className="fas fa-image"></i>
            </div>
            <div className="flow-content">
              <h5>Original Image</h5>
              <p>Raw MRI slice input</p>
            </div>
          </div>
          <div className="flow-arrow">→</div>
          <div className="flow-step">
            <div className="flow-icon" style={{ background: '#27ae60' }}>
              <i className="fas fa-filter"></i>
            </div>
            <div className="flow-content">
              <h5>Noise Reduction</h5>
              <p>Bilateral/Gaussian filtering</p>
            </div>
          </div>
          <div className="flow-arrow">→</div>
          <div className="flow-step">
            <div className="flow-icon" style={{ background: '#3498db' }}>
              <i className="fas fa-adjust"></i>
            </div>
            <div className="flow-content">
              <h5>Contrast Enhancement</h5>
              <p>CLAHE/Histogram equalization</p>
            </div>
          </div>
          <div className="flow-arrow">→</div>
          <div className="flow-step">
            <div className="flow-icon" style={{ background: '#f39c12' }}>
              <i className="fas fa-sun"></i>
            </div>
            <div className="flow-content">
              <h5>Brightness Adjustment</h5>
              <p>Gamma correction</p>
            </div>
          </div>
          <div className="flow-arrow">→</div>
          <div className="flow-step">
            <div className="flow-icon" style={{ background: '#e74c3c' }}>
              <i className="fas fa-vector-square"></i>
            </div>
            <div className="flow-content">
              <h5>Edge Enhancement</h5>
              <p>Unsharp masking</p>
            </div>
          </div>
        </div>
      </div>

      <div className="preprocessing-comparison">
        <h4>Before & After Preprocessing</h4>
        <div className="comparison-grid">
          <div className="comparison-item">
            <div className="comparison-image">
              <i className="fas fa-image"></i>
              <span>Original Image</span>
            </div>
            <div className="comparison-label">Raw MRI Slice</div>
          </div>
          <div className="comparison-item">
            <div className="comparison-image">
              <i className="fas fa-adjust"></i>
              <span>Enhanced Contrast</span>
            </div>
            <div className="comparison-label">CLAHE Applied</div>
          </div>
          <div className="comparison-item">
            <div className="comparison-image">
              <i className="fas fa-filter"></i>
              <span>Noise Reduced</span>
            </div>
            <div className="comparison-label">Bilateral Filter</div>
          </div>
          <div className="comparison-item">
            <div className="comparison-image">
              <i className="fas fa-check"></i>
              <span>Final Result</span>
            </div>
            <div className="comparison-label">Fully Processed</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PreprocessingTab;