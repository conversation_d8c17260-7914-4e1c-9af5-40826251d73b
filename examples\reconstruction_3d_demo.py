"""
3D Reconstruction Demo
=====================

This script demonstrates comprehensive 3D reconstruction capabilities
for brain tumor visualization from segmented 2D MRI slices.

Author: Dr. <PERSON>
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle
import time
import json

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from reconstruction_3d import (
    VolumeInterpolator, IsotropicVoxelCreator, OrthogonalViews,
    SurfaceRenderer, VolumeVisualizer, Reconstruction3D
)


def create_realistic_tumor_slices(n_slices=30, slice_size=(128, 128), 
                                tumor_type='spherical'):
    """
    Create realistic tumor slices for demonstration.
    
    Args:
        n_slices: Number of slices to create
        slice_size: Size of each slice
        tumor_type: Type of tumor ('spherical', 'irregular', 'multiple')
        
    Returns:
        List of 2D slices and corresponding masks
    """
    print(f"Creating {n_slices} realistic {tumor_type} tumor slices...")
    
    slices = []
    masks = []
    
    # Parameters for different tumor types
    if tumor_type == 'spherical':
        center = (slice_size[0]//2, slice_size[1]//2)
        max_radius = 20
        
        for i in range(n_slices):
            # Create base brain tissue
            slice_img = create_brain_tissue_slice(slice_size)
            
            # Calculate tumor radius for this slice
            z_position = (i - n_slices//2) / (n_slices//2)
            radius = max_radius * np.sqrt(max(0, 1 - z_position**2))
            
            # Create tumor mask
            mask = create_spherical_tumor(slice_size, center, radius)
            
            # Add tumor to slice
            slice_img[mask] = 180 + np.random.normal(0, 10, np.sum(mask))
            
            slices.append(slice_img)
            masks.append(mask.astype(np.uint8))
    
    elif tumor_type == 'irregular':
        base_center = (slice_size[0]//2, slice_size[1]//2)
        
        for i in range(n_slices):
            # Create base brain tissue
            slice_img = create_brain_tissue_slice(slice_size)
            
            # Create irregular tumor
            mask = create_irregular_tumor(slice_size, base_center, i, n_slices)
            
            # Add tumor to slice
            slice_img[mask] = 190 + np.random.normal(0, 15, np.sum(mask))
            
            slices.append(slice_img)
            masks.append(mask.astype(np.uint8))
    
    elif tumor_type == 'multiple':
        centers = [(40, 40), (80, 80), (60, 100)]
        
        for i in range(n_slices):
            # Create base brain tissue
            slice_img = create_brain_tissue_slice(slice_size)
            
            # Create multiple tumors
            mask = create_multiple_tumors(slice_size, centers, i, n_slices)
            
            # Add tumors to slice
            slice_img[mask] = 170 + np.random.normal(0, 12, np.sum(mask))
            
            slices.append(slice_img)
            masks.append(mask.astype(np.uint8))
    
    return slices, masks


def create_brain_tissue_slice(slice_size):
    """Create realistic brain tissue background."""
    # Base tissue intensity
    tissue = np.random.normal(100, 20, slice_size)
    
    # Add some structure
    y, x = np.ogrid[:slice_size[0], :slice_size[1]]
    center = (slice_size[0]//2, slice_size[1]//2)
    
    # Brain boundary (circular)
    brain_radius = min(slice_size) // 2 - 10
    brain_mask = (x - center[0])**2 + (y - center[1])**2 <= brain_radius**2
    
    # Set background to zero
    tissue[~brain_mask] = 0
    
    # Add some anatomical variation
    tissue += 30 * np.sin(x * 0.1) * np.cos(y * 0.1)
    
    return np.clip(tissue, 0, 255)


def create_spherical_tumor(slice_size, center, radius):
    """Create spherical tumor mask."""
    if radius <= 0:
        return np.zeros(slice_size, dtype=bool)
    
    y, x = np.ogrid[:slice_size[0], :slice_size[1]]
    mask = (x - center[0])**2 + (y - center[1])**2 <= radius**2
    
    return mask


def create_irregular_tumor(slice_size, center, slice_idx, total_slices):
    """Create irregular tumor mask."""
    # Base radius varies with slice position
    z_position = (slice_idx - total_slices//2) / (total_slices//2)
    base_radius = 15 * np.sqrt(max(0, 1 - z_position**2))
    
    if base_radius <= 0:
        return np.zeros(slice_size, dtype=bool)
    
    y, x = np.ogrid[:slice_size[0], :slice_size[1]]
    
    # Create irregular shape using multiple circles
    mask = np.zeros(slice_size, dtype=bool)
    
    # Main tumor body
    main_mask = (x - center[0])**2 + (y - center[1])**2 <= base_radius**2
    mask |= main_mask
    
    # Add irregular extensions
    n_extensions = 3
    for i in range(n_extensions):
        angle = 2 * np.pi * i / n_extensions + slice_idx * 0.2
        ext_center = (
            center[0] + base_radius * 0.7 * np.cos(angle),
            center[1] + base_radius * 0.7 * np.sin(angle)
        )
        ext_radius = base_radius * 0.4
        
        ext_mask = (x - ext_center[0])**2 + (y - ext_center[1])**2 <= ext_radius**2
        mask |= ext_mask
    
    return mask


def create_multiple_tumors(slice_size, centers, slice_idx, total_slices):
    """Create multiple tumor masks."""
    y, x = np.ogrid[:slice_size[0], :slice_size[1]]
    mask = np.zeros(slice_size, dtype=bool)
    
    for i, center in enumerate(centers):
        # Each tumor has different size variation
        z_position = (slice_idx - total_slices//2) / (total_slices//2)
        radius = (10 + i * 3) * np.sqrt(max(0, 1 - (z_position - 0.2*i)**2))
        
        if radius > 0:
            tumor_mask = (x - center[0])**2 + (y - center[1])**2 <= radius**2
            mask |= tumor_mask
    
    return mask


def demonstrate_volume_interpolation():
    """Demonstrate volume interpolation capabilities."""
    print("\n" + "="*60)
    print("VOLUME INTERPOLATION DEMONSTRATION")
    print("="*60)
    
    # Create sample slices
    slices, masks = create_realistic_tumor_slices(n_slices=10, 
                                                slice_size=(64, 64), 
                                                tumor_type='spherical')
    
    # Initialize interpolator
    interpolator = VolumeInterpolator()
    
    # Test different interpolation methods
    methods = ['linear', 'cubic', 'nearest']
    
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    for i, method in enumerate(methods):
        print(f"\nTesting {method} interpolation...")
        
        # Interpolate volume
        interpolator.method = method
        interpolated_volume = interpolator.interpolate_volume(
            slices, slice_spacing=2.0, target_spacing=1.0)
        
        print(f"Original slices: {len(slices)}")
        print(f"Interpolated volume shape: {interpolated_volume.shape}")
        
        # Show middle slice
        mid_slice = interpolated_volume.shape[0] // 2
        axes[0, i].imshow(interpolated_volume[mid_slice], cmap='gray')
        axes[0, i].set_title(f'{method.capitalize()} Interpolation')
        axes[0, i].axis('off')
        
        # Show profile along z-axis
        center_y, center_x = 32, 32
        profile = interpolated_volume[:, center_y, center_x]
        axes[1, i].plot(profile)
        axes[1, i].set_title(f'{method.capitalize()} Profile')
        axes[1, i].set_xlabel('Slice Index')
        axes[1, i].set_ylabel('Intensity')
        axes[1, i].grid(True)
    
    plt.tight_layout()
    plt.show()
    
    # Demonstrate smoothing
    print("\nDemonstrating smoothing...")
    smoothed_volume = interpolator.apply_smoothing(interpolated_volume, sigma=1.0)
    
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    
    axes[0].imshow(interpolated_volume[mid_slice], cmap='gray')
    axes[0].set_title('Original')
    axes[0].axis('off')
    
    axes[1].imshow(smoothed_volume[mid_slice], cmap='gray')
    axes[1].set_title('Smoothed (σ=1.0)')
    axes[1].axis('off')
    
    plt.tight_layout()
    plt.show()


def demonstrate_isotropic_voxel_creation():
    """Demonstrate isotropic voxel creation."""
    print("\n" + "="*60)
    print("ISOTROPIC VOXEL CREATION DEMONSTRATION")
    print("="*60)
    
    # Create anisotropic volume
    slices, masks = create_realistic_tumor_slices(n_slices=15, 
                                                slice_size=(64, 64), 
                                                tumor_type='irregular')
    
    # Stack into volume
    volume = np.stack(slices, axis=0)
    
    # Define anisotropic spacing
    original_spacing = (3.0, 1.0, 1.0)  # z, y, x
    
    print(f"Original volume shape: {volume.shape}")
    print(f"Original spacing: {original_spacing}")
    
    # Create isotropic voxels
    creator = IsotropicVoxelCreator()
    isotropic_volume = creator.create_isotropic_volume(volume, original_spacing)
    
    print(f"Isotropic volume shape: {isotropic_volume.shape}")
    print(f"Target spacing: {creator.target_spacing}")
    
    # Calculate volume metrics
    mask_volume = np.stack(masks, axis=0)
    isotropic_mask = creator.create_isotropic_volume(mask_volume, original_spacing)
    
    metrics = creator.calculate_volume_metrics(isotropic_mask, 
                                             (creator.target_spacing,) * 3)
    
    print(f"\nVolume Metrics:")
    print(f"Total volume: {metrics['total_volume_ml']:.2f} ml")
    print(f"Surface area: {metrics['surface_area_mm2']:.2f} mm²")
    print(f"Voxel count: {metrics['voxel_count']}")
    print(f"Bounding box size: {metrics['bounding_box_size']}")
    
    # Visualize comparison
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # Original volume
    axes[0, 0].imshow(volume[volume.shape[0]//2], cmap='gray')
    axes[0, 0].set_title('Original Volume (Axial)')
    axes[0, 0].axis('off')
    
    axes[0, 1].imshow(volume[:, volume.shape[1]//2], cmap='gray')
    axes[0, 1].set_title('Original Volume (Coronal)')
    axes[0, 1].axis('off')
    
    # Isotropic volume
    axes[1, 0].imshow(isotropic_volume[isotropic_volume.shape[0]//2], cmap='gray')
    axes[1, 0].set_title('Isotropic Volume (Axial)')
    axes[1, 0].axis('off')
    
    axes[1, 1].imshow(isotropic_volume[:, isotropic_volume.shape[1]//2], cmap='gray')
    axes[1, 1].set_title('Isotropic Volume (Coronal)')
    axes[1, 1].axis('off')
    
    plt.tight_layout()
    plt.show()


def demonstrate_orthogonal_views():
    """Demonstrate orthogonal views generation."""
    print("\n" + "="*60)
    print("ORTHOGONAL VIEWS DEMONSTRATION")
    print("="*60)
    
    # Create 3D volume
    slices, masks = create_realistic_tumor_slices(n_slices=40, 
                                                slice_size=(80, 80), 
                                                tumor_type='multiple')
    
    volume = np.stack(slices, axis=0)
    
    # Generate orthogonal views
    ortho_viewer = OrthogonalViews()
    
    # Standard orthogonal views
    views = ortho_viewer.generate_orthogonal_views(volume)
    
    print(f"Volume shape: {volume.shape}")
    print(f"Axial view shape: {views['axial'].shape}")
    print(f"Sagittal view shape: {views['sagittal'].shape}")
    print(f"Coronal view shape: {views['coronal'].shape}")
    
    # Visualize orthogonal views
    ortho_viewer.visualize_orthogonal_views(volume, 
                                          title="Multi-tumor Orthogonal Views")
    
    # Create MIP views
    print("\nCreating Maximum Intensity Projections...")
    mip_views = ortho_viewer.create_mip_views(volume)
    
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    axes[0].imshow(mip_views['axial_mip'], cmap='gray')
    axes[0].set_title('Axial MIP')
    axes[0].axis('off')
    
    axes[1].imshow(mip_views['sagittal_mip'], cmap='gray')
    axes[1].set_title('Sagittal MIP')
    axes[1].axis('off')
    
    axes[2].imshow(mip_views['coronal_mip'], cmap='gray')
    axes[2].set_title('Coronal MIP')
    axes[2].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    # Create mean views
    print("Creating Mean Intensity Projections...")
    mean_views = ortho_viewer.create_mean_views(volume)
    
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    axes[0].imshow(mean_views['axial_mean'], cmap='gray')
    axes[0].set_title('Axial Mean')
    axes[0].axis('off')
    
    axes[1].imshow(mean_views['sagittal_mean'], cmap='gray')
    axes[1].set_title('Sagittal Mean')
    axes[1].axis('off')
    
    axes[2].imshow(mean_views['coronal_mean'], cmap='gray')
    axes[2].set_title('Coronal Mean')
    axes[2].axis('off')
    
    plt.tight_layout()
    plt.show()


def demonstrate_surface_rendering():
    """Demonstrate 3D surface rendering."""
    print("\n" + "="*60)
    print("SURFACE RENDERING DEMONSTRATION")
    print("="*60)
    
    # Create 3D tumor mask
    slices, masks = create_realistic_tumor_slices(n_slices=30, 
                                                slice_size=(64, 64), 
                                                tumor_type='spherical')
    
    mask_volume = np.stack(masks, axis=0).astype(float)
    
    # Apply some smoothing for better surface
    from scipy.ndimage import gaussian_filter
    mask_volume = gaussian_filter(mask_volume, sigma=1.0)
    
    print(f"Mask volume shape: {mask_volume.shape}")
    print(f"Non-zero voxels: {np.sum(mask_volume > 0)}")
    
    # Initialize surface renderer
    renderer = SurfaceRenderer()
    
    # Extract surface
    print("Extracting surface using marching cubes...")
    spacing = (2.0, 1.0, 1.0)
    vertices, faces, normals, values = renderer.extract_surface(
        mask_volume, level=0.5, spacing=spacing)
    
    print(f"Surface vertices: {len(vertices)}")
    print(f"Surface faces: {len(faces)}")
    
    # Calculate surface metrics
    metrics = renderer.calculate_surface_metrics(mask_volume, spacing=spacing)
    
    print(f"\nSurface Metrics:")
    print(f"Surface area: {metrics['surface_area_mm2']:.2f} mm²")
    print(f"Convex volume: {metrics['convex_volume_mm3']:.2f} mm³")
    print(f"Centroid: {metrics['centroid']}")
    print(f"Bounding box size: {metrics['bounding_box_size']}")
    
    # Render surface
    print("\nRendering 3D surface...")
    renderer.render_surface_3d(mask_volume, level=0.5, spacing=spacing, 
                             color='red', alpha=0.7, 
                             title="Brain Tumor 3D Surface")
    
    # Create wireframe
    print("Creating wireframe visualization...")
    renderer.create_wireframe(mask_volume, level=0.5, spacing=spacing, 
                            title="Brain Tumor Wireframe")


def demonstrate_volume_visualization():
    """Demonstrate volume visualization capabilities."""
    print("\n" + "="*60)
    print("VOLUME VISUALIZATION DEMONSTRATION")
    print("="*60)
    
    # Create 3D volume
    slices, masks = create_realistic_tumor_slices(n_slices=25, 
                                                slice_size=(96, 96), 
                                                tumor_type='irregular')
    
    volume = np.stack(slices, axis=0)
    mask_volume = np.stack(masks, axis=0)
    
    # Initialize volume visualizer
    visualizer = VolumeVisualizer()
    
    # Create volume slices
    print("Creating volume slice visualization...")
    visualizer.create_volume_slices(volume, n_slices=9, axis=0,
                                  title="Irregular Tumor Volume Slices")
    
    # Create volume overlay
    print("Creating volume overlay...")
    mid_slice = volume.shape[0] // 2
    visualizer.create_volume_overlay(volume, mask_volume, 
                                   slice_index=mid_slice, axis=0,
                                   title="Tumor Overlay Visualization")
    
    # Create 3D scatter plot
    print("Creating 3D scatter plot...")
    visualizer.create_3d_scatter(mask_volume, threshold=0.5, 
                               sample_ratio=0.2,
                               title="Tumor 3D Scatter Plot")


def demonstrate_complete_reconstruction():
    """Demonstrate complete 3D reconstruction pipeline."""
    print("\n" + "="*60)
    print("COMPLETE 3D RECONSTRUCTION DEMONSTRATION")
    print("="*60)
    
    # Create realistic tumor data
    print("Creating realistic tumor data...")
    slices, masks = create_realistic_tumor_slices(n_slices=20, 
                                                slice_size=(128, 128), 
                                                tumor_type='irregular')
    
    # Initialize reconstruction system
    reconstructor = Reconstruction3D()
    
    # Perform complete reconstruction
    print("\nPerforming complete reconstruction...")
    reconstructed_volume = reconstructor.reconstruct_from_slices(
        slices=slices,
        slice_spacing=2.5,
        pixel_spacing=(1.0, 1.0),
        target_isotropic_spacing=1.0,
        interpolation_method='linear',
        smoothing_sigma=0.8
    )
    
    print(f"Reconstruction completed!")
    print(f"Final volume shape: {reconstructed_volume.shape}")
    
    # Analyze reconstruction
    print("\nAnalyzing reconstruction...")
    analysis = reconstructor.analyze_reconstruction()
    
    # Print detailed analysis
    print("\nDetailed Analysis Results:")
    print("-" * 40)
    
    recon_info = analysis['reconstruction_info']
    print(f"Original slices: {recon_info['original_slices']}")
    print(f"Slice spacing: {recon_info['original_slice_spacing']} mm")
    print(f"Pixel spacing: {recon_info['original_pixel_spacing']} mm")
    print(f"Target spacing: {recon_info['target_isotropic_spacing']} mm")
    print(f"Interpolation method: {recon_info['interpolation_method']}")
    print(f"Smoothing sigma: {recon_info['smoothing_sigma']}")
    print(f"Reconstruction time: {recon_info['reconstruction_time']:.2f} seconds")
    
    volume_metrics = analysis['volume_metrics']
    print(f"\nVolume Metrics:")
    print(f"Total volume: {volume_metrics['total_volume_ml']:.2f} ml")
    print(f"Surface area: {volume_metrics['surface_area_mm2']:.2f} mm²")
    print(f"Voxel count: {volume_metrics['voxel_count']}")
    print(f"Bounding box: {volume_metrics['bounding_box_size']}")
    
    surface_metrics = analysis['surface_metrics']
    print(f"\nSurface Metrics:")
    print(f"Surface area: {surface_metrics['surface_area_mm2']:.2f} mm²")
    print(f"Vertices: {surface_metrics['n_vertices']}")
    print(f"Faces: {surface_metrics['n_faces']}")
    print(f"Centroid: {surface_metrics['centroid']}")
    
    # Create comprehensive visualization
    print("\nCreating comprehensive visualization...")
    reconstructor.create_comprehensive_visualization(
        title="Complete Brain Tumor Reconstruction")
    
    # Export reconstruction data
    print("\nExporting reconstruction data...")
    reconstructor.export_reconstruction_data("complete_reconstruction_demo", 
                                           analysis=analysis)
    
    return reconstructor, analysis


def create_comparison_study():
    """Create a comparison study of different reconstruction parameters."""
    print("\n" + "="*60)
    print("RECONSTRUCTION PARAMETER COMPARISON STUDY")
    print("="*60)
    
    # Create test data
    slices, masks = create_realistic_tumor_slices(n_slices=15, 
                                                slice_size=(64, 64), 
                                                tumor_type='spherical')
    
    # Test different parameters
    test_configs = [
        {'method': 'linear', 'spacing': 1.0, 'smoothing': 0.0},
        {'method': 'linear', 'spacing': 1.0, 'smoothing': 1.0},
        {'method': 'cubic', 'spacing': 1.0, 'smoothing': 0.5},
        {'method': 'linear', 'spacing': 0.5, 'smoothing': 0.5},
    ]
    
    results = []
    
    for i, config in enumerate(test_configs):
        print(f"\nTesting configuration {i+1}: {config}")
        
        # Initialize reconstructor
        reconstructor = Reconstruction3D()
        
        # Perform reconstruction
        start_time = time.time()
        volume = reconstructor.reconstruct_from_slices(
            slices=slices,
            slice_spacing=2.0,
            pixel_spacing=(1.0, 1.0),
            target_isotropic_spacing=config['spacing'],
            interpolation_method=config['method'],
            smoothing_sigma=config['smoothing']
        )
        
        # Analyze
        analysis = reconstructor.analyze_reconstruction()
        
        # Store results
        results.append({
            'config': config,
            'volume_shape': volume.shape,
            'reconstruction_time': time.time() - start_time,
            'volume_ml': analysis['volume_metrics']['total_volume_ml'],
            'surface_area': analysis['surface_metrics']['surface_area_mm2'],
            'n_vertices': analysis['surface_metrics']['n_vertices']
        })
    
    # Create comparison table
    print("\n" + "="*60)
    print("RECONSTRUCTION COMPARISON RESULTS")
    print("="*60)
    
    print(f"{'Config':<8} {'Method':<8} {'Spacing':<8} {'Smooth':<8} {'Time':<8} {'Volume':<10} {'Surface':<10}")
    print("-" * 70)
    
    for i, result in enumerate(results):
        config = result['config']
        print(f"{i+1:<8} {config['method']:<8} {config['spacing']:<8} {config['smoothing']:<8} "
              f"{result['reconstruction_time']:<8.2f} {result['volume_ml']:<10.2f} {result['surface_area']:<10.2f}")
    
    # Visualize comparison
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # Reconstruction time
    times = [r['reconstruction_time'] for r in results]
    axes[0, 0].bar(range(len(times)), times)
    axes[0, 0].set_title('Reconstruction Time')
    axes[0, 0].set_xlabel('Configuration')
    axes[0, 0].set_ylabel('Time (seconds)')
    
    # Volume
    volumes = [r['volume_ml'] for r in results]
    axes[0, 1].bar(range(len(volumes)), volumes)
    axes[0, 1].set_title('Tumor Volume')
    axes[0, 1].set_xlabel('Configuration')
    axes[0, 1].set_ylabel('Volume (ml)')
    
    # Surface area
    surfaces = [r['surface_area'] for r in results]
    axes[1, 0].bar(range(len(surfaces)), surfaces)
    axes[1, 0].set_title('Surface Area')
    axes[1, 0].set_xlabel('Configuration')
    axes[1, 0].set_ylabel('Surface Area (mm²)')
    
    # Vertices
    vertices = [r['n_vertices'] for r in results]
    axes[1, 1].bar(range(len(vertices)), vertices)
    axes[1, 1].set_title('Surface Vertices')
    axes[1, 1].set_xlabel('Configuration')
    axes[1, 1].set_ylabel('Number of Vertices')
    
    plt.tight_layout()
    plt.show()


def main():
    """Main demonstration function."""
    print("3D Reconstruction Comprehensive Demo")
    print("Author: Dr. Mohammed Yagoub Esmail")
    print("=" * 80)
    
    print("\nThis demo will showcase all 3D reconstruction capabilities:")
    print("1. Volume Interpolation")
    print("2. Isotropic Voxel Creation")
    print("3. Orthogonal Views")
    print("4. Surface Rendering")
    print("5. Volume Visualization")
    print("6. Complete Reconstruction Pipeline")
    print("7. Parameter Comparison Study")
    
    input("\nPress Enter to start the demonstration...")
    
    # Run demonstrations
    demonstrate_volume_interpolation()
    demonstrate_isotropic_voxel_creation()
    demonstrate_orthogonal_views()
    demonstrate_surface_rendering()
    demonstrate_volume_visualization()
    
    # Complete reconstruction demo
    reconstructor, analysis = demonstrate_complete_reconstruction()
    
    # Comparison study
    create_comparison_study()
    
    print("\n" + "=" * 80)
    print("DEMONSTRATION COMPLETE")
    print("=" * 80)
    print("\nKey Takeaways:")
    print("1. Volume interpolation enables smooth 3D reconstruction from sparse slices")
    print("2. Isotropic voxel creation ensures consistent spatial resolution")
    print("3. Orthogonal views provide comprehensive visualization perspectives")
    print("4. Surface rendering creates detailed 3D models for analysis")
    print("5. The complete pipeline integrates all techniques seamlessly")
    print("6. Parameter selection significantly affects reconstruction quality")
    print("\nThe system is ready for real-world medical imaging applications!")


if __name__ == "__main__":
    main()