"""
Feature Extraction and Selection Module
======================================

This module implements comprehensive feature extraction techniques for tumor characterization
from segmented brain MRI images. It includes texture, shape, statistical features, and
Local Binary Patterns with feature selection capabilities.

Author: Dr<PERSON>smail
"""

import numpy as np
import cv2
from scipy import ndimage
from scipy.stats import skew, kurtosis
from sklearn.feature_selection import SelectKBest, f_classif, RFE
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from skimage import measure, morphology
from skimage.feature import graycomatrix, graycoprops, local_binary_pattern
from skimage.filters import roberts, sobel, scharr, prewitt
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')


class TextureFeatures:
    """
    Extract texture features using Gray-Level Co-occurrence Matrix (GLCM)
    and other texture analysis methods.
    """
    
    def __init__(self, distances: List[int] = [1, 2, 3], 
                 angles: List[float] = [0, np.pi/4, np.pi/2, 3*np.pi/4]):
        """
        Initialize texture feature extractor.
        
        Args:
            distances: List of pixel distances for GLCM calculation
            angles: List of angles for GLCM calculation
        """
        self.distances = distances
        self.angles = angles
        
    def extract_glcm_features(self, image: np.ndarray, mask: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        Extract GLCM (Gray-Level Co-occurrence Matrix) features.
        
        Args:
            image: Input grayscale image
            mask: Binary mask for region of interest
            
        Returns:
            Dictionary containing GLCM features
        """
        if mask is not None:
            image = image * mask
        
        # Normalize image to 0-255 range
        image = ((image - image.min()) / (image.max() - image.min()) * 255).astype(np.uint8)
        
        # Calculate GLCM
        glcm = graycomatrix(image, distances=self.distances, angles=self.angles, 
                          levels=256, symmetric=True, normed=True)
        
        # Extract GLCM properties
        features = {}
        properties = ['contrast', 'dissimilarity', 'homogeneity', 'energy', 
                     'correlation', 'ASM']
        
        for prop in properties:
            values = graycoprops(glcm, prop)
            features[f'glcm_{prop}_mean'] = np.mean(values)
            features[f'glcm_{prop}_std'] = np.std(values)
            features[f'glcm_{prop}_min'] = np.min(values)
            features[f'glcm_{prop}_max'] = np.max(values)
        
        return features
    
    def extract_lbp_features(self, image: np.ndarray, mask: Optional[np.ndarray] = None,
                           radius: int = 3, n_points: int = 24) -> Dict[str, float]:
        """
        Extract Local Binary Pattern (LBP) features.
        
        Args:
            image: Input grayscale image
            mask: Binary mask for region of interest
            radius: Radius of LBP
            n_points: Number of sampling points
            
        Returns:
            Dictionary containing LBP features
        """
        if mask is not None:
            image = image * mask
        
        # Calculate LBP
        lbp = local_binary_pattern(image, n_points, radius, method='uniform')
        
        # Calculate LBP histogram
        hist, _ = np.histogram(lbp.ravel(), bins=n_points+2, range=(0, n_points+2))
        hist = hist.astype(float)
        hist /= (hist.sum() + 1e-7)  # Normalize
        
        features = {}
        
        # Basic LBP statistics
        features['lbp_uniformity'] = np.sum(hist ** 2)
        features['lbp_entropy'] = -np.sum(hist * np.log2(hist + 1e-7))
        features['lbp_contrast'] = np.sum([(i - np.mean(range(len(hist))))**2 * hist[i] 
                                          for i in range(len(hist))])
        
        # LBP histogram features
        features['lbp_mean'] = np.mean(lbp)
        features['lbp_std'] = np.std(lbp)
        features['lbp_skewness'] = skew(lbp.ravel())
        features['lbp_kurtosis'] = kurtosis(lbp.ravel())
        
        # Rotation invariant measures
        features['lbp_rotation_invariant'] = np.sum(hist[:-1])  # Exclude non-uniform patterns
        
        return features
    
    def extract_edge_features(self, image: np.ndarray, mask: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        Extract edge-based texture features.
        
        Args:
            image: Input grayscale image
            mask: Binary mask for region of interest
            
        Returns:
            Dictionary containing edge features
        """
        if mask is not None:
            image = image * mask
        
        # Apply different edge detection operators
        edges_roberts = roberts(image)
        edges_sobel = sobel(image)
        edges_scharr = scharr(image)
        edges_prewitt = prewitt(image)
        
        features = {}
        
        # Edge statistics for each operator
        for name, edges in [('roberts', edges_roberts), ('sobel', edges_sobel),
                           ('scharr', edges_scharr), ('prewitt', edges_prewitt)]:
            features[f'edge_{name}_mean'] = np.mean(edges)
            features[f'edge_{name}_std'] = np.std(edges)
            features[f'edge_{name}_max'] = np.max(edges)
            features[f'edge_{name}_energy'] = np.sum(edges ** 2)
        
        return features


class ShapeFeatures:
    """
    Extract shape features from segmented regions.
    """
    
    def extract_basic_shape_features(self, mask: np.ndarray) -> Dict[str, float]:
        """
        Extract basic shape features from binary mask.
        
        Args:
            mask: Binary mask of segmented region
            
        Returns:
            Dictionary containing shape features
        """
        # Find contours
        contours, _ = cv2.findContours(mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return {}
        
        # Use the largest contour
        contour = max(contours, key=cv2.contourArea)
        
        features = {}
        
        # Basic measurements
        area = cv2.contourArea(contour)
        perimeter = cv2.arcLength(contour, True)
        
        features['area'] = area
        features['perimeter'] = perimeter
        
        # Derived measurements
        if perimeter > 0:
            features['circularity'] = 4 * np.pi * area / (perimeter ** 2)
            features['compactness'] = perimeter ** 2 / area
        else:
            features['circularity'] = 0
            features['compactness'] = 0
        
        # Bounding rectangle
        x, y, w, h = cv2.boundingRect(contour)
        features['bounding_box_area'] = w * h
        features['extent'] = area / (w * h) if (w * h) > 0 else 0
        features['aspect_ratio'] = w / h if h > 0 else 0
        
        # Convex hull
        hull = cv2.convexHull(contour)
        hull_area = cv2.contourArea(hull)
        features['convex_area'] = hull_area
        features['solidity'] = area / hull_area if hull_area > 0 else 0
        
        # Equivalent diameter
        features['equivalent_diameter'] = np.sqrt(4 * area / np.pi)
        
        # Moments
        moments = cv2.moments(contour)
        if moments['m00'] != 0:
            features['centroid_x'] = moments['m10'] / moments['m00']
            features['centroid_y'] = moments['m01'] / moments['m00']
        else:
            features['centroid_x'] = 0
            features['centroid_y'] = 0
        
        return features
    
    def extract_advanced_shape_features(self, mask: np.ndarray) -> Dict[str, float]:
        """
        Extract advanced shape features using moments and other methods.
        
        Args:
            mask: Binary mask of segmented region
            
        Returns:
            Dictionary containing advanced shape features
        """
        # Label the mask
        labeled_mask = measure.label(mask)
        regions = measure.regionprops(labeled_mask)
        
        if not regions:
            return {}
        
        # Use the largest region
        region = max(regions, key=lambda r: r.area)
        
        features = {}
        
        # Moment invariants (Hu moments)
        moments = cv2.moments(mask.astype(np.uint8))
        hu_moments = cv2.HuMoments(moments).flatten()
        for i, hu in enumerate(hu_moments):
            features[f'hu_moment_{i+1}'] = -np.sign(hu) * np.log10(abs(hu) + 1e-7)
        
        # Shape descriptors from regionprops
        features['eccentricity'] = region.eccentricity
        features['major_axis_length'] = region.major_axis_length
        features['minor_axis_length'] = region.minor_axis_length
        features['orientation'] = region.orientation
        features['filled_area'] = region.filled_area
        
        # Additional shape measures
        features['elongation'] = region.major_axis_length / region.minor_axis_length if region.minor_axis_length > 0 else 0
        features['roundness'] = 4 * np.pi * region.area / (region.perimeter ** 2) if region.perimeter > 0 else 0
        
        # Fractal dimension (box-counting method)
        features['fractal_dimension'] = self._calculate_fractal_dimension(mask)
        
        return features
    
    def _calculate_fractal_dimension(self, mask: np.ndarray) -> float:
        """
        Calculate fractal dimension using box-counting method.
        
        Args:
            mask: Binary mask
            
        Returns:
            Fractal dimension
        """
        # Implement box-counting method
        def boxcount(image, k):
            s = np.add.reduceat(
                np.add.reduceat(image, np.arange(0, image.shape[0], k), axis=0),
                np.arange(0, image.shape[1], k), axis=1)
            return len(np.where((s > 0) & (s < k*k))[0])
        
        # Apply box-counting for different scales
        scales = np.logspace(0, 3, num=20, dtype=int)
        scales = scales[scales < min(mask.shape)]
        
        if len(scales) < 2:
            return 1.0
        
        counts = []
        for scale in scales:
            counts.append(boxcount(mask, scale))
        
        # Fit line to log-log plot
        coeffs = np.polyfit(np.log(scales), np.log(counts), 1)
        return -coeffs[0]


class StatisticalFeatures:
    """
    Extract statistical features from image regions.
    """
    
    def extract_first_order_statistics(self, image: np.ndarray, mask: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        Extract first-order statistical features.
        
        Args:
            image: Input grayscale image
            mask: Binary mask for region of interest
            
        Returns:
            Dictionary containing first-order statistics
        """
        if mask is not None:
            pixels = image[mask > 0]
        else:
            pixels = image.ravel()
        
        if len(pixels) == 0:
            return {}
        
        features = {}
        
        # Basic statistics
        features['mean'] = np.mean(pixels)
        features['std'] = np.std(pixels)
        features['variance'] = np.var(pixels)
        features['median'] = np.median(pixels)
        features['mode'] = float(np.bincount(pixels.astype(int)).argmax())
        
        # Range statistics
        features['min'] = np.min(pixels)
        features['max'] = np.max(pixels)
        features['range'] = features['max'] - features['min']
        features['iqr'] = np.percentile(pixels, 75) - np.percentile(pixels, 25)
        
        # Distribution shape
        features['skewness'] = skew(pixels)
        features['kurtosis'] = kurtosis(pixels)
        
        # Percentiles
        percentiles = [10, 25, 50, 75, 90, 95, 99]
        for p in percentiles:
            features[f'percentile_{p}'] = np.percentile(pixels, p)
        
        # Entropy
        hist, _ = np.histogram(pixels, bins=256, range=(pixels.min(), pixels.max()))
        hist = hist.astype(float)
        hist /= hist.sum()
        features['entropy'] = -np.sum(hist * np.log2(hist + 1e-7))
        
        # Energy
        features['energy'] = np.sum(hist ** 2)
        
        # Coefficient of variation
        features['coefficient_of_variation'] = features['std'] / features['mean'] if features['mean'] != 0 else 0
        
        return features
    
    def extract_second_order_statistics(self, image: np.ndarray, mask: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        Extract second-order statistical features.
        
        Args:
            image: Input grayscale image
            mask: Binary mask for region of interest
            
        Returns:
            Dictionary containing second-order statistics
        """
        if mask is not None:
            image = image * mask
        
        # Calculate gradient magnitude
        grad_x = np.gradient(image, axis=1)
        grad_y = np.gradient(image, axis=0)
        grad_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        
        # Calculate Laplacian
        laplacian = ndimage.laplace(image)
        
        features = {}
        
        # Gradient statistics
        if mask is not None:
            grad_pixels = grad_magnitude[mask > 0]
            laplacian_pixels = laplacian[mask > 0]
        else:
            grad_pixels = grad_magnitude.ravel()
            laplacian_pixels = laplacian.ravel()
        
        features['gradient_mean'] = np.mean(grad_pixels)
        features['gradient_std'] = np.std(grad_pixels)
        features['gradient_max'] = np.max(grad_pixels)
        
        features['laplacian_mean'] = np.mean(laplacian_pixels)
        features['laplacian_std'] = np.std(laplacian_pixels)
        features['laplacian_variance'] = np.var(laplacian_pixels)
        
        return features


class FeatureExtractor:
    """
    Main feature extraction class that combines all feature types.
    """
    
    def __init__(self):
        """Initialize feature extractor with all feature type extractors."""
        self.texture_extractor = TextureFeatures()
        self.shape_extractor = ShapeFeatures()
        self.statistical_extractor = StatisticalFeatures()
    
    def extract_all_features(self, image: np.ndarray, mask: np.ndarray) -> Dict[str, float]:
        """
        Extract all types of features from image and mask.
        
        Args:
            image: Input grayscale image
            mask: Binary mask of segmented region
            
        Returns:
            Dictionary containing all extracted features
        """
        features = {}
        
        # Extract texture features
        try:
            glcm_features = self.texture_extractor.extract_glcm_features(image, mask)
            features.update(glcm_features)
        except Exception as e:
            print(f"Error extracting GLCM features: {e}")
        
        try:
            lbp_features = self.texture_extractor.extract_lbp_features(image, mask)
            features.update(lbp_features)
        except Exception as e:
            print(f"Error extracting LBP features: {e}")
        
        try:
            edge_features = self.texture_extractor.extract_edge_features(image, mask)
            features.update(edge_features)
        except Exception as e:
            print(f"Error extracting edge features: {e}")
        
        # Extract shape features
        try:
            basic_shape_features = self.shape_extractor.extract_basic_shape_features(mask)
            features.update(basic_shape_features)
        except Exception as e:
            print(f"Error extracting basic shape features: {e}")
        
        try:
            advanced_shape_features = self.shape_extractor.extract_advanced_shape_features(mask)
            features.update(advanced_shape_features)
        except Exception as e:
            print(f"Error extracting advanced shape features: {e}")
        
        # Extract statistical features
        try:
            first_order_stats = self.statistical_extractor.extract_first_order_statistics(image, mask)
            features.update(first_order_stats)
        except Exception as e:
            print(f"Error extracting first-order statistics: {e}")
        
        try:
            second_order_stats = self.statistical_extractor.extract_second_order_statistics(image, mask)
            features.update(second_order_stats)
        except Exception as e:
            print(f"Error extracting second-order statistics: {e}")
        
        return features
    
    def extract_features_batch(self, images: List[np.ndarray], masks: List[np.ndarray]) -> List[Dict[str, float]]:
        """
        Extract features from multiple images and masks.
        
        Args:
            images: List of input images
            masks: List of corresponding masks
            
        Returns:
            List of feature dictionaries
        """
        features_list = []
        
        for image, mask in zip(images, masks):
            features = self.extract_all_features(image, mask)
            features_list.append(features)
        
        return features_list


class FeatureSelector:
    """
    Feature selection methods for reducing dimensionality and improving performance.
    """
    
    def __init__(self):
        """Initialize feature selector."""
        self.scaler = StandardScaler()
        self.selected_features = None
        self.feature_scores = None
    
    def select_features_univariate(self, X: np.ndarray, y: np.ndarray, k: int = 20) -> Tuple[np.ndarray, List[int]]:
        """
        Select features using univariate statistical tests.
        
        Args:
            X: Feature matrix
            y: Target labels
            k: Number of features to select
            
        Returns:
            Tuple of (selected features, selected indices)
        """
        # Normalize features
        X_scaled = self.scaler.fit_transform(X)
        
        # Select k best features
        selector = SelectKBest(score_func=f_classif, k=k)
        X_selected = selector.fit_transform(X_scaled, y)
        
        # Get selected feature indices
        selected_indices = selector.get_support(indices=True)
        
        # Store feature scores
        self.feature_scores = selector.scores_
        self.selected_features = selected_indices
        
        return X_selected, selected_indices.tolist()
    
    def select_features_rfe(self, X: np.ndarray, y: np.ndarray, n_features: int = 20) -> Tuple[np.ndarray, List[int]]:
        """
        Select features using Recursive Feature Elimination.
        
        Args:
            X: Feature matrix
            y: Target labels
            n_features: Number of features to select
            
        Returns:
            Tuple of (selected features, selected indices)
        """
        # Normalize features
        X_scaled = self.scaler.fit_transform(X)
        
        # Use Random Forest as base estimator
        estimator = RandomForestClassifier(n_estimators=100, random_state=42)
        
        # Perform RFE
        selector = RFE(estimator=estimator, n_features_to_select=n_features)
        X_selected = selector.fit_transform(X_scaled, y)
        
        # Get selected feature indices
        selected_indices = np.where(selector.support_)[0]
        
        # Store feature rankings
        self.feature_scores = selector.ranking_
        self.selected_features = selected_indices
        
        return X_selected, selected_indices.tolist()
    
    def select_features_importance(self, X: np.ndarray, y: np.ndarray, threshold: float = 0.01) -> Tuple[np.ndarray, List[int]]:
        """
        Select features based on Random Forest feature importance.
        
        Args:
            X: Feature matrix
            y: Target labels
            threshold: Importance threshold for feature selection
            
        Returns:
            Tuple of (selected features, selected indices)
        """
        # Normalize features
        X_scaled = self.scaler.fit_transform(X)
        
        # Train Random Forest
        rf = RandomForestClassifier(n_estimators=100, random_state=42)
        rf.fit(X_scaled, y)
        
        # Get feature importances
        importances = rf.feature_importances_
        
        # Select features above threshold
        selected_indices = np.where(importances > threshold)[0]
        X_selected = X_scaled[:, selected_indices]
        
        # Store feature importances
        self.feature_scores = importances
        self.selected_features = selected_indices
        
        return X_selected, selected_indices.tolist()
    
    def get_feature_ranking(self, feature_names: List[str]) -> List[Tuple[str, float]]:
        """
        Get ranking of features based on their scores.
        
        Args:
            feature_names: List of feature names
            
        Returns:
            List of (feature_name, score) tuples sorted by score
        """
        if self.feature_scores is None:
            return []
        
        # Create feature-score pairs
        feature_scores = [(feature_names[i], score) for i, score in enumerate(self.feature_scores)]
        
        # Sort by score (descending)
        feature_scores.sort(key=lambda x: x[1], reverse=True)
        
        return feature_scores


class FeatureAnalyzer:
    """
    Feature analysis and visualization utilities.
    """
    
    def __init__(self):
        """Initialize feature analyzer."""
        pass
    
    def analyze_feature_distribution(self, features_dict: Dict[str, List[float]], 
                                   feature_name: str) -> Dict[str, Any]:
        """
        Analyze distribution of a specific feature.
        
        Args:
            features_dict: Dictionary of feature name to list of values
            feature_name: Name of feature to analyze
            
        Returns:
            Dictionary containing distribution statistics
        """
        if feature_name not in features_dict:
            return {}
        
        values = np.array(features_dict[feature_name])
        
        analysis = {
            'mean': np.mean(values),
            'std': np.std(values),
            'min': np.min(values),
            'max': np.max(values),
            'median': np.median(values),
            'skewness': skew(values),
            'kurtosis': kurtosis(values),
            'percentile_25': np.percentile(values, 25),
            'percentile_75': np.percentile(values, 75)
        }
        
        return analysis
    
    def compare_feature_groups(self, features_dict: Dict[str, List[float]], 
                              labels: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        Compare features between different groups (e.g., tumor vs normal).
        
        Args:
            features_dict: Dictionary of feature name to list of values
            labels: List of labels for each sample
            
        Returns:
            Dictionary containing comparison results
        """
        unique_labels = list(set(labels))
        comparison_results = {}
        
        for feature_name, values in features_dict.items():
            feature_comparison = {}
            
            for label in unique_labels:
                indices = [i for i, l in enumerate(labels) if l == label]
                group_values = np.array(values)[indices]
                
                feature_comparison[label] = {
                    'mean': np.mean(group_values),
                    'std': np.std(group_values),
                    'count': len(group_values)
                }
            
            comparison_results[feature_name] = feature_comparison
        
        return comparison_results
    
    def visualize_feature_importance(self, feature_names: List[str], 
                                   importance_scores: List[float], 
                                   top_n: int = 20) -> None:
        """
        Visualize feature importance scores.
        
        Args:
            feature_names: List of feature names
            importance_scores: List of importance scores
            top_n: Number of top features to display
        """
        # Sort features by importance
        sorted_indices = np.argsort(importance_scores)[::-1]
        
        # Get top N features
        top_indices = sorted_indices[:top_n]
        top_names = [feature_names[i] for i in top_indices]
        top_scores = [importance_scores[i] for i in top_indices]
        
        # Create plot
        plt.figure(figsize=(12, 8))
        plt.barh(range(len(top_names)), top_scores)
        plt.yticks(range(len(top_names)), top_names)
        plt.xlabel('Importance Score')
        plt.title(f'Top {top_n} Feature Importance')
        plt.tight_layout()
        plt.show()


def main():
    """
    Example usage of the feature extraction module.
    """
    # Create sample data
    np.random.seed(42)
    
    # Generate sample image and mask
    image = np.random.rand(100, 100) * 255
    mask = np.zeros((100, 100), dtype=np.uint8)
    mask[30:70, 30:70] = 1  # Square region
    
    # Initialize feature extractor
    extractor = FeatureExtractor()
    
    # Extract features
    print("Extracting features...")
    features = extractor.extract_all_features(image, mask)
    
    print(f"Extracted {len(features)} features:")
    for feature_name, value in list(features.items())[:10]:  # Show first 10
        print(f"  {feature_name}: {value:.4f}")
    
    # Feature selection example
    print("\nFeature selection example:")
    
    # Create sample dataset
    n_samples = 100
    images = [np.random.rand(50, 50) * 255 for _ in range(n_samples)]
    masks = [np.random.randint(0, 2, (50, 50)) for _ in range(n_samples)]
    labels = np.random.randint(0, 2, n_samples)  # Binary labels
    
    # Extract features for all samples
    features_list = extractor.extract_features_batch(images, masks)
    
    # Convert to matrix format
    feature_names = list(features_list[0].keys())
    X = np.array([[features.get(name, 0) for name in feature_names] for features in features_list])
    
    # Remove NaN values
    X = np.nan_to_num(X)
    
    # Feature selection
    selector = FeatureSelector()
    X_selected, selected_indices = selector.select_features_univariate(X, labels, k=10)
    
    print(f"Selected {len(selected_indices)} features out of {len(feature_names)}")
    print("Selected features:")
    for idx in selected_indices:
        print(f"  {feature_names[idx]}")
    
    # Get feature ranking
    ranking = selector.get_feature_ranking(feature_names)
    print(f"\nTop 5 features by importance:")
    for name, score in ranking[:5]:
        print(f"  {name}: {score:.4f}")


if __name__ == "__main__":
    main()"""
Feature Extraction and Selection Module
======================================

This module implements comprehensive feature extraction techniques for tumor characterization
from segmented brain MRI images. It includes texture, shape, statistical features, and
Local Binary Patterns with feature selection capabilities.

Author: Dr. Mohammed Yagoub Esmail
"""

import numpy as np
import cv2
from scipy import ndimage
from scipy.stats import skew, kurtosis
from sklearn.feature_selection import SelectKBest, f_classif, RFE
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from skimage import measure, morphology
from skimage.feature import graycomatrix, graycoprops, local_binary_pattern
from skimage.filters import roberts, sobel, scharr, prewitt
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')


class TextureFeatures:
    """
    Extract texture features using Gray-Level Co-occurrence Matrix (GLCM)
    and other texture analysis methods.
    """
    
    def __init__(self, distances: List[int] = [1, 2, 3], 
                 angles: List[float] = [0, np.pi/4, np.pi/2, 3*np.pi/4]):
        """
        Initialize texture feature extractor.
        
        Args:
            distances: List of pixel distances for GLCM calculation
            angles: List of angles for GLCM calculation
        """
        self.distances = distances
        self.angles = angles
        
    def extract_glcm_features(self, image: np.ndarray, mask: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        Extract GLCM (Gray-Level Co-occurrence Matrix) features.
        
        Args:
            image: Input grayscale image
            mask: Binary mask for region of interest
            
        Returns:
            Dictionary containing GLCM features
        """
        if mask is not None:
            image = image * mask
        
        # Normalize image to 0-255 range
        image = ((image - image.min()) / (image.max() - image.min()) * 255).astype(np.uint8)
        
        # Calculate GLCM
        glcm = graycomatrix(image, distances=self.distances, angles=self.angles, 
                          levels=256, symmetric=True, normed=True)
        
        # Extract GLCM properties
        features = {}
        properties = ['contrast', 'dissimilarity', 'homogeneity', 'energy', 
                     'correlation', 'ASM']
        
        for prop in properties:
            values = graycoprops(glcm, prop)
            features[f'glcm_{prop}_mean'] = np.mean(values)
            features[f'glcm_{prop}_std'] = np.std(values)
            features[f'glcm_{prop}_min'] = np.min(values)
            features[f'glcm_{prop}_max'] = np.max(values)
        
        return features
    
    def extract_lbp_features(self, image: np.ndarray, mask: Optional[np.ndarray] = None,
                           radius: int = 3, n_points: int = 24) -> Dict[str, float]:
        """
        Extract Local Binary Pattern (LBP) features.
        
        Args:
            image: Input grayscale image
            mask: Binary mask for region of interest
            radius: Radius of LBP
            n_points: Number of sampling points
            
        Returns:
            Dictionary containing LBP features
        """
        if mask is not None:
            image = image * mask
        
        # Calculate LBP
        lbp = local_binary_pattern(image, n_points, radius, method='uniform')
        
        # Calculate LBP histogram
        hist, _ = np.histogram(lbp.ravel(), bins=n_points+2, range=(0, n_points+2))
        hist = hist.astype(float)
        hist /= (hist.sum() + 1e-7)  # Normalize
        
        features = {}
        
        # Basic LBP statistics
        features['lbp_uniformity'] = np.sum(hist ** 2)
        features['lbp_entropy'] = -np.sum(hist * np.log2(hist + 1e-7))
        features['lbp_contrast'] = np.sum([(i - np.mean(range(len(hist))))**2 * hist[i] 
                                          for i in range(len(hist))])
        
        # LBP histogram features
        features['lbp_mean'] = np.mean(lbp)
        features['lbp_std'] = np.std(lbp)
        features['lbp_skewness'] = skew(lbp.ravel())
        features['lbp_kurtosis'] = kurtosis(lbp.ravel())
        
        # Rotation invariant measures
        features['lbp_rotation_invariant'] = np.sum(hist[:-1])  # Exclude non-uniform patterns
        
        return features
    
    def extract_edge_features(self, image: np.ndarray, mask: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        Extract edge-based texture features.
        
        Args:
            image: Input grayscale image
            mask: Binary mask for region of interest
            
        Returns:
            Dictionary containing edge features
        """
        if mask is not None:
            image = image * mask
        
        # Apply different edge detection operators
        edges_roberts = roberts(image)
        edges_sobel = sobel(image)
        edges_scharr = scharr(image)
        edges_prewitt = prewitt(image)
        
        features = {}
        
        # Edge statistics for each operator
        for name, edges in [('roberts', edges_roberts), ('sobel', edges_sobel),
                           ('scharr', edges_scharr), ('prewitt', edges_prewitt)]:
            features[f'edge_{name}_mean'] = np.mean(edges)
            features[f'edge_{name}_std'] = np.std(edges)
            features[f'edge_{name}_max'] = np.max(edges)
            features[f'edge_{name}_energy'] = np.sum(edges ** 2)
        
        return features


class ShapeFeatures:
    """
    Extract shape features from segmented regions.
    """
    
    def extract_basic_shape_features(self, mask: np.ndarray) -> Dict[str, float]:
        """
        Extract basic shape features from binary mask.
        
        Args:
            mask: Binary mask of segmented region
            
        Returns:
            Dictionary containing shape features
        """
        # Find contours
        contours, _ = cv2.findContours(mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return {}
        
        # Use the largest contour
        contour = max(contours, key=cv2.contourArea)
        
        features = {}
        
        # Basic measurements
        area = cv2.contourArea(contour)
        perimeter = cv2.arcLength(contour, True)
        
        features['area'] = area
        features['perimeter'] = perimeter
        
        # Derived measurements
        if perimeter > 0:
            features['circularity'] = 4 * np.pi * area / (perimeter ** 2)
            features['compactness'] = perimeter ** 2 / area
        else:
            features['circularity'] = 0
            features['compactness'] = 0
        
        # Bounding rectangle
        x, y, w, h = cv2.boundingRect(contour)
        features['bounding_box_area'] = w * h
        features['extent'] = area / (w * h) if (w * h) > 0 else 0
        features['aspect_ratio'] = w / h if h > 0 else 0
        
        # Convex hull
        hull = cv2.convexHull(contour)
        hull_area = cv2.contourArea(hull)
        features['convex_area'] = hull_area
        features['solidity'] = area / hull_area if hull_area > 0 else 0
        
        # Equivalent diameter
        features['equivalent_diameter'] = np.sqrt(4 * area / np.pi)
        
        # Moments
        moments = cv2.moments(contour)
        if moments['m00'] != 0:
            features['centroid_x'] = moments['m10'] / moments['m00']
            features['centroid_y'] = moments['m01'] / moments['m00']
        else:
            features['centroid_x'] = 0
            features['centroid_y'] = 0
        
        return features
    
    def extract_advanced_shape_features(self, mask: np.ndarray) -> Dict[str, float]:
        """
        Extract advanced shape features using moments and other methods.
        
        Args:
            mask: Binary mask of segmented region
            
        Returns:
            Dictionary containing advanced shape features
        """
        # Label the mask
        labeled_mask = measure.label(mask)
        regions = measure.regionprops(labeled_mask)
        
        if not regions:
            return {}
        
        # Use the largest region
        region = max(regions, key=lambda r: r.area)
        
        features = {}
        
        # Moment invariants (Hu moments)
        moments = cv2.moments(mask.astype(np.uint8))
        hu_moments = cv2.HuMoments(moments).flatten()
        for i, hu in enumerate(hu_moments):
            features[f'hu_moment_{i+1}'] = -np.sign(hu) * np.log10(abs(hu) + 1e-7)
        
        # Shape descriptors from regionprops
        features['eccentricity'] = region.eccentricity
        features['major_axis_length'] = region.major_axis_length
        features['minor_axis_length'] = region.minor_axis_length
        features['orientation'] = region.orientation
        features['filled_area'] = region.filled_area
        
        # Additional shape measures
        features['elongation'] = region.major_axis_length / region.minor_axis_length if region.minor_axis_length > 0 else 0
        features['roundness'] = 4 * np.pi * region.area / (region.perimeter ** 2) if region.perimeter > 0 else 0
        
        # Fractal dimension (box-counting method)
        features['fractal_dimension'] = self._calculate_fractal_dimension(mask)
        
        return features
    
    def _calculate_fractal_dimension(self, mask: np.ndarray) -> float:
        """
        Calculate fractal dimension using box-counting method.
        
        Args:
            mask: Binary mask
            
        Returns:
            Fractal dimension
        """
        # Implement box-counting method
        def boxcount(image, k):
            s = np.add.reduceat(
                np.add.reduceat(image, np.arange(0, image.shape[0], k), axis=0),
                np.arange(0, image.shape[1], k), axis=1)
            return len(np.where((s > 0) & (s < k*k))[0])
        
        # Apply box-counting for different scales
        scales = np.logspace(0, 3, num=20, dtype=int)
        scales = scales[scales < min(mask.shape)]
        
        if len(scales) < 2:
            return 1.0
        
        counts = []
        for scale in scales:
            counts.append(boxcount(mask, scale))
        
        # Fit line to log-log plot
        coeffs = np.polyfit(np.log(scales), np.log(counts), 1)
        return -coeffs[0]


class StatisticalFeatures:
    """
    Extract statistical features from image regions.
    """
    
    def extract_first_order_statistics(self, image: np.ndarray, mask: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        Extract first-order statistical features.
        
        Args:
            image: Input grayscale image
            mask: Binary mask for region of interest
            
        Returns:
            Dictionary containing first-order statistics
        """
        if mask is not None:
            pixels = image[mask > 0]
        else:
            pixels = image.ravel()
        
        if len(pixels) == 0:
            return {}
        
        features = {}
        
        # Basic statistics
        features['mean'] = np.mean(pixels)
        features['std'] = np.std(pixels)
        features['variance'] = np.var(pixels)
        features['median'] = np.median(pixels)
        features['mode'] = float(np.bincount(pixels.astype(int)).argmax())
        
        # Range statistics
        features['min'] = np.min(pixels)
        features['max'] = np.max(pixels)
        features['range'] = features['max'] - features['min']
        features['iqr'] = np.percentile(pixels, 75) - np.percentile(pixels, 25)
        
        # Distribution shape
        features['skewness'] = skew(pixels)
        features['kurtosis'] = kurtosis(pixels)
        
        # Percentiles
        percentiles = [10, 25, 50, 75, 90, 95, 99]
        for p in percentiles:
            features[f'percentile_{p}'] = np.percentile(pixels, p)
        
        # Entropy
        hist, _ = np.histogram(pixels, bins=256, range=(pixels.min(), pixels.max()))
        hist = hist.astype(float)
        hist /= hist.sum()
        features['entropy'] = -np.sum(hist * np.log2(hist + 1e-7))
        
        # Energy
        features['energy'] = np.sum(hist ** 2)
        
        # Coefficient of variation
        features['coefficient_of_variation'] = features['std'] / features['mean'] if features['mean'] != 0 else 0
        
        return features
    
    def extract_second_order_statistics(self, image: np.ndarray, mask: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        Extract second-order statistical features.
        
        Args:
            image: Input grayscale image
            mask: Binary mask for region of interest
            
        Returns:
            Dictionary containing second-order statistics
        """
        if mask is not None:
            image = image * mask
        
        # Calculate gradient magnitude
        grad_x = np.gradient(image, axis=1)
        grad_y = np.gradient(image, axis=0)
        grad_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        
        # Calculate Laplacian
        laplacian = ndimage.laplace(image)
        
        features = {}
        
        # Gradient statistics
        if mask is not None:
            grad_pixels = grad_magnitude[mask > 0]
            laplacian_pixels = laplacian[mask > 0]
        else:
            grad_pixels = grad_magnitude.ravel()
            laplacian_pixels = laplacian.ravel()
        
        features['gradient_mean'] = np.mean(grad_pixels)
        features['gradient_std'] = np.std(grad_pixels)
        features['gradient_max'] = np.max(grad_pixels)
        
        features['laplacian_mean'] = np.mean(laplacian_pixels)
        features['laplacian_std'] = np.std(laplacian_pixels)
        features['laplacian_variance'] = np.var(laplacian_pixels)
        
        return features


class FeatureExtractor:
    """
    Main feature extraction class that combines all feature types.
    """
    
    def __init__(self):
        """Initialize feature extractor with all feature type extractors."""
        self.texture_extractor = TextureFeatures()
        self.shape_extractor = ShapeFeatures()
        self.statistical_extractor = StatisticalFeatures()
    
    def extract_all_features(self, image: np.ndarray, mask: np.ndarray) -> Dict[str, float]:
        """
        Extract all types of features from image and mask.
        
        Args:
            image: Input grayscale image
            mask: Binary mask of segmented region
            
        Returns:
            Dictionary containing all extracted features
        """
        features = {}
        
        # Extract texture features
        try:
            glcm_features = self.texture_extractor.extract_glcm_features(image, mask)
            features.update(glcm_features)
        except Exception as e:
            print(f"Error extracting GLCM features: {e}")
        
        try:
            lbp_features = self.texture_extractor.extract_lbp_features(image, mask)
            features.update(lbp_features)
        except Exception as e:
            print(f"Error extracting LBP features: {e}")
        
        try:
            edge_features = self.texture_extractor.extract_edge_features(image, mask)
            features.update(edge_features)
        except Exception as e:
            print(f"Error extracting edge features: {e}")
        
        # Extract shape features
        try:
            basic_shape_features = self.shape_extractor.extract_basic_shape_features(mask)
            features.update(basic_shape_features)
        except Exception as e:
            print(f"Error extracting basic shape features: {e}")
        
        try:
            advanced_shape_features = self.shape_extractor.extract_advanced_shape_features(mask)
            features.update(advanced_shape_features)
        except Exception as e:
            print(f"Error extracting advanced shape features: {e}")
        
        # Extract statistical features
        try:
            first_order_stats = self.statistical_extractor.extract_first_order_statistics(image, mask)
            features.update(first_order_stats)
        except Exception as e:
            print(f"Error extracting first-order statistics: {e}")
        
        try:
            second_order_stats = self.statistical_extractor.extract_second_order_statistics(image, mask)
            features.update(second_order_stats)
        except Exception as e:
            print(f"Error extracting second-order statistics: {e}")
        
        return features
    
    def extract_features_batch(self, images: List[np.ndarray], masks: List[np.ndarray]) -> List[Dict[str, float]]:
        """
        Extract features from multiple images and masks.
        
        Args:
            images: List of input images
            masks: List of corresponding masks
            
        Returns:
            List of feature dictionaries
        """
        features_list = []
        
        for image, mask in zip(images, masks):
            features = self.extract_all_features(image, mask)
            features_list.append(features)
        
        return features_list


class FeatureSelector:
    """
    Feature selection methods for reducing dimensionality and improving performance.
    """
    
    def __init__(self):
        """Initialize feature selector."""
        self.scaler = StandardScaler()
        self.selected_features = None
        self.feature_scores = None
    
    def select_features_univariate(self, X: np.ndarray, y: np.ndarray, k: int = 20) -> Tuple[np.ndarray, List[int]]:
        """
        Select features using univariate statistical tests.
        
        Args:
            X: Feature matrix
            y: Target labels
            k: Number of features to select
            
        Returns:
            Tuple of (selected features, selected indices)
        """
        # Normalize features
        X_scaled = self.scaler.fit_transform(X)
        
        # Select k best features
        selector = SelectKBest(score_func=f_classif, k=k)
        X_selected = selector.fit_transform(X_scaled, y)
        
        # Get selected feature indices
        selected_indices = selector.get_support(indices=True)
        
        # Store feature scores
        self.feature_scores = selector.scores_
        self.selected_features = selected_indices
        
        return X_selected, selected_indices.tolist()
    
    def select_features_rfe(self, X: np.ndarray, y: np.ndarray, n_features: int = 20) -> Tuple[np.ndarray, List[int]]:
        """
        Select features using Recursive Feature Elimination.
        
        Args:
            X: Feature matrix
            y: Target labels
            n_features: Number of features to select
            
        Returns:
            Tuple of (selected features, selected indices)
        """
        # Normalize features
        X_scaled = self.scaler.fit_transform(X)
        
        # Use Random Forest as base estimator
        estimator = RandomForestClassifier(n_estimators=100, random_state=42)
        
        # Perform RFE
        selector = RFE(estimator=estimator, n_features_to_select=n_features)
        X_selected = selector.fit_transform(X_scaled, y)
        
        # Get selected feature indices
        selected_indices = np.where(selector.support_)[0]
        
        # Store feature rankings
        self.feature_scores = selector.ranking_
        self.selected_features = selected_indices
        
        return X_selected, selected_indices.tolist()
    
    def select_features_importance(self, X: np.ndarray, y: np.ndarray, threshold: float = 0.01) -> Tuple[np.ndarray, List[int]]:
        """
        Select features based on Random Forest feature importance.
        
        Args:
            X: Feature matrix
            y: Target labels
            threshold: Importance threshold for feature selection
            
        Returns:
            Tuple of (selected features, selected indices)
        """
        # Normalize features
        X_scaled = self.scaler.fit_transform(X)
        
        # Train Random Forest
        rf = RandomForestClassifier(n_estimators=100, random_state=42)
        rf.fit(X_scaled, y)
        
        # Get feature importances
        importances = rf.feature_importances_
        
        # Select features above threshold
        selected_indices = np.where(importances > threshold)[0]
        X_selected = X_scaled[:, selected_indices]
        
        # Store feature importances
        self.feature_scores = importances
        self.selected_features = selected_indices
        
        return X_selected, selected_indices.tolist()
    
    def get_feature_ranking(self, feature_names: List[str]) -> List[Tuple[str, float]]:
        """
        Get ranking of features based on their scores.
        
        Args:
            feature_names: List of feature names
            
        Returns:
            List of (feature_name, score) tuples sorted by score
        """
        if self.feature_scores is None:
            return []
        
        # Create feature-score pairs
        feature_scores = [(feature_names[i], score) for i, score in enumerate(self.feature_scores)]
        
        # Sort by score (descending)
        feature_scores.sort(key=lambda x: x[1], reverse=True)
        
        return feature_scores


class FeatureAnalyzer:
    """
    Feature analysis and visualization utilities.
    """
    
    def __init__(self):
        """Initialize feature analyzer."""
        pass
    
    def analyze_feature_distribution(self, features_dict: Dict[str, List[float]], 
                                   feature_name: str) -> Dict[str, Any]:
        """
        Analyze distribution of a specific feature.
        
        Args:
            features_dict: Dictionary of feature name to list of values
            feature_name: Name of feature to analyze
            
        Returns:
            Dictionary containing distribution statistics
        """
        if feature_name not in features_dict:
            return {}
        
        values = np.array(features_dict[feature_name])
        
        analysis = {
            'mean': np.mean(values),
            'std': np.std(values),
            'min': np.min(values),
            'max': np.max(values),
            'median': np.median(values),
            'skewness': skew(values),
            'kurtosis': kurtosis(values),
            'percentile_25': np.percentile(values, 25),
            'percentile_75': np.percentile(values, 75)
        }
        
        return analysis
    
    def compare_feature_groups(self, features_dict: Dict[str, List[float]], 
                              labels: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        Compare features between different groups (e.g., tumor vs normal).
        
        Args:
            features_dict: Dictionary of feature name to list of values
            labels: List of labels for each sample
            
        Returns:
            Dictionary containing comparison results
        """
        unique_labels = list(set(labels))
        comparison_results = {}
        
        for feature_name, values in features_dict.items():
            feature_comparison = {}
            
            for label in unique_labels:
                indices = [i for i, l in enumerate(labels) if l == label]
                group_values = np.array(values)[indices]
                
                feature_comparison[label] = {
                    'mean': np.mean(group_values),
                    'std': np.std(group_values),
                    'count': len(group_values)
                }
            
            comparison_results[feature_name] = feature_comparison
        
        return comparison_results
    
    def visualize_feature_importance(self, feature_names: List[str], 
                                   importance_scores: List[float], 
                                   top_n: int = 20) -> None:
        """
        Visualize feature importance scores.
        
        Args:
            feature_names: List of feature names
            importance_scores: List of importance scores
            top_n: Number of top features to display
        """
        # Sort features by importance
        sorted_indices = np.argsort(importance_scores)[::-1]
        
        # Get top N features
        top_indices = sorted_indices[:top_n]
        top_names = [feature_names[i] for i in top_indices]
        top_scores = [importance_scores[i] for i in top_indices]
        
        # Create plot
        plt.figure(figsize=(12, 8))
        plt.barh(range(len(top_names)), top_scores)
        plt.yticks(range(len(top_names)), top_names)
        plt.xlabel('Importance Score')
        plt.title(f'Top {top_n} Feature Importance')
        plt.tight_layout()
        plt.show()


def main():
    """
    Example usage of the feature extraction module.
    """
    # Create sample data
    np.random.seed(42)
    
    # Generate sample image and mask
    image = np.random.rand(100, 100) * 255
    mask = np.zeros((100, 100), dtype=np.uint8)
    mask[30:70, 30:70] = 1  # Square region
    
    # Initialize feature extractor
    extractor = FeatureExtractor()
    
    # Extract features
    print("Extracting features...")
    features = extractor.extract_all_features(image, mask)
    
    print(f"Extracted {len(features)} features:")
    for feature_name, value in list(features.items())[:10]:  # Show first 10
        print(f"  {feature_name}: {value:.4f}")
    
    # Feature selection example
    print("\nFeature selection example:")
    
    # Create sample dataset
    n_samples = 100
    images = [np.random.rand(50, 50) * 255 for _ in range(n_samples)]
    masks = [np.random.randint(0, 2, (50, 50)) for _ in range(n_samples)]
    labels = np.random.randint(0, 2, n_samples)  # Binary labels
    
    # Extract features for all samples
    features_list = extractor.extract_features_batch(images, masks)
    
    # Convert to matrix format
    feature_names = list(features_list[0].keys())
    X = np.array([[features.get(name, 0) for name in feature_names] for features in features_list])
    
    # Remove NaN values
    X = np.nan_to_num(X)
    
    # Feature selection
    selector = FeatureSelector()
    X_selected, selected_indices = selector.select_features_univariate(X, labels, k=10)
    
    print(f"Selected {len(selected_indices)} features out of {len(feature_names)}")
    print("Selected features:")
    for idx in selected_indices:
        print(f"  {feature_names[idx]}")
    
    # Get feature ranking
    ranking = selector.get_feature_ranking(feature_names)
    print(f"\nTop 5 features by importance:")
    for name, score in ranking[:5]:
        print(f"  {name}: {score:.4f}")


if __name__ == "__main__":
    main()