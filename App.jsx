import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button.jsx'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card.jsx'
import { Badge } from '@/components/ui/badge.jsx'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs.jsx'
import { Brain, Database, Image, Layers, BarChart3, Cpu, Github, Download, Play, Code } from 'lucide-react'
import { motion } from 'framer-motion'
import './App.css'

// Import images
import brainTumorClassification from './assets/images/brain-tumor-classification.png'
import brainTumorMRI from './assets/images/brain-tumor-mri.jpg'
import brain3DReconstruction from './assets/images/3d-brain-reconstruction.jpg'
import medicalWorkflow from './assets/images/medical-workflow.jpg'
import dicomBrain from './assets/images/dicom-brain.jpg'

function App() {
  const [activeSection, setActiveSection] = useState('overview')

  const sections = [
    {
      id: 'dicom',
      title: 'DICOM Loading',
      icon: Database,
      description: 'Load and process DICOM image series from medical scanners',
      image: dicomBrain
    },
    {
      id: 'preprocessing',
      title: 'Preprocessing',
      icon: Image,
      description: 'Apply contrast adaptation, brightness adjustment, and filtering',
      image: brainTumorMRI
    },
    {
      id: 'segmentation',
      title: 'Image Segmentation',
      icon: Layers,
      description: 'Segment brain regions using deformable models and watershed algorithms',
      image: brainTumorClassification
    },
    {
      id: 'features',
      title: 'Feature Extraction',
      icon: BarChart3,
      description: 'Extract texture and shape features for tumor characterization',
      image: medicalWorkflow
    },
    {
      id: 'reconstruction',
      title: '3D Reconstruction',
      icon: Cpu,
      description: 'Reconstruct 3D tumor models from segmented slices',
      image: brain3DReconstruction
    },
    {
      id: 'results',
      title: 'Results & Comparison',
      icon: Brain,
      description: 'Calculate performance metrics and compare methods',
      image: brainTumorClassification
    }
  ]

  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  }

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b sticky top-0 z-50">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Brain className="h-8 w-8 text-blue-600" />
              <h1 className="text-2xl font-bold text-gray-900">Brain Tumor Detection</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                3D MRI Analysis
              </Badge>
              <Button variant="outline" size="sm">
                <Github className="h-4 w-4 mr-2" />
                View Code
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-6">
        <div className="container mx-auto text-center">
          <motion.div {...fadeInUp}>
            <h2 className="text-5xl font-bold text-gray-900 mb-6">
              Advanced Brain Tumor Detection in
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
                {" "}Reconstructed 3D MRI Images
              </span>
            </h2>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              A comprehensive pipeline for detecting and analyzing brain tumors using state-of-the-art 
              image processing techniques, machine learning algorithms, and 3D reconstruction methods.
            </p>
            <div className="flex justify-center space-x-4">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                <Play className="h-5 w-5 mr-2" />
                Start Analysis
              </Button>
              <Button variant="outline" size="lg">
                <Download className="h-5 w-5 mr-2" />
                Download Dataset
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16 px-6">
        <div className="container mx-auto">
          <Tabs value={activeSection} onValueChange={setActiveSection} className="w-full">
            <TabsList className="grid w-full grid-cols-7 mb-8">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              {sections.map((section) => (
                <TabsTrigger key={section.id} value={section.id} className="text-xs">
                  {section.title.split(' ')[0]}
                </TabsTrigger>
              ))}
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview">
              <motion.div 
                variants={staggerContainer}
                initial="initial"
                animate="animate"
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
              >
                {sections.map((section, index) => {
                  const IconComponent = section.icon
                  return (
                    <motion.div key={section.id} variants={fadeInUp}>
                      <Card 
                        className="h-full hover:shadow-lg transition-all duration-300 cursor-pointer group"
                        onClick={() => setActiveSection(section.id)}
                      >
                        <CardHeader>
                          <div className="flex items-center space-x-3">
                            <div className="p-2 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors">
                              <IconComponent className="h-6 w-6 text-blue-600" />
                            </div>
                            <div>
                              <CardTitle className="text-lg">{section.title}</CardTitle>
                              <Badge variant="outline" className="mt-1">
                                Step {index + 1}
                              </Badge>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <img 
                            src={section.image} 
                            alt={section.title}
                            className="w-full h-32 object-cover rounded-lg mb-3"
                          />
                          <CardDescription className="text-sm">
                            {section.description}
                          </CardDescription>
                        </CardContent>
                      </Card>
                    </motion.div>
                  )
                })}
              </motion.div>
            </TabsContent>

            {/* DICOM Loading Tab */}
            <TabsContent value="dicom">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Database className="h-6 w-6 text-blue-600" />
                    <span>1. Load DICOM Image Set</span>
                  </CardTitle>
                  <CardDescription>
                    Load and organize DICOM series from medical imaging devices
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <img 
                        src={dicomBrain} 
                        alt="DICOM Brain Images"
                        className="w-full h-64 object-cover rounded-lg mb-4"
                      />
                      <h4 className="font-semibold mb-2">Key Features:</h4>
                      <ul className="space-y-2 text-sm text-gray-600">
                        <li>• Load DICOM series from folders</li>
                        <li>• Sort by slice location</li>
                        <li>• Extract pixel arrays</li>
                        <li>• Handle metadata information</li>
                      </ul>
                    </div>
                    <div>
                      <Tabs defaultValue="python" className="w-full">
                        <TabsList>
                          <TabsTrigger value="python">Python</TabsTrigger>
                          <TabsTrigger value="matlab">MATLAB</TabsTrigger>
                        </TabsList>
                        <TabsContent value="python">
                          <div className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto">
                            <pre>{`import pydicom
import numpy as np
import os

def load_dicom_series(dicom_folder):
    """Load DICOM series from a folder"""
    dicom_files = []
    for filename in os.listdir(dicom_folder):
        if filename.endswith('.dcm'):
            filepath = os.path.join(dicom_folder, filename)
            dicom_files.append(pydicom.dcmread(filepath))
    
    # Sort by slice location
    dicom_files.sort(key=lambda x: float(x.SliceLocation))
    
    # Extract pixel arrays
    image_stack = np.stack([ds.pixel_array for ds in dicom_files])
    
    return image_stack, dicom_files`}</pre>
                          </div>
                        </TabsContent>
                        <TabsContent value="matlab">
                          <div className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto">
                            <pre>{`function [imageVolume, dicomInfo] = loadDicomSeries(dicomFolder)
    % Load DICOM series from folder
    dicomFiles = dir(fullfile(dicomFolder, '*.dcm'));
    numFiles = length(dicomFiles);
    
    % Read first image to get dimensions
    firstImage = dicomread(fullfile(dicomFolder, dicomFiles(1).name));
    [rows, cols] = size(firstImage);
    
    % Initialize volume
    imageVolume = zeros(rows, cols, numFiles);
    
    % Load all slices
    for i = 1:numFiles
        filename = fullfile(dicomFolder, dicomFiles(i).name);
        imageVolume(:,:,i) = dicomread(filename);
    end
end`}</pre>
                          </div>
                        </TabsContent>
                      </Tabs>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Preprocessing Tab */}
            <TabsContent value="preprocessing">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Image className="h-6 w-6 text-blue-600" />
                    <span>2. Preprocessing</span>
                  </CardTitle>
                  <CardDescription>
                    Enhance image quality through contrast adaptation, brightness adjustment, and filtering
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <img 
                        src={brainTumorMRI} 
                        alt="Brain Tumor MRI"
                        className="w-full h-64 object-cover rounded-lg mb-4"
                      />
                      <h4 className="font-semibold mb-2">Processing Steps:</h4>
                      <ul className="space-y-2 text-sm text-gray-600">
                        <li>• Contrast Adaptation (CLAHE)</li>
                        <li>• Brightness Adaptation</li>
                        <li>• Median Filtering</li>
                        <li>• Gaussian Filtering</li>
                        <li>• Edge Enhancement</li>
                      </ul>
                    </div>
                    <div>
                      <Tabs defaultValue="python" className="w-full">
                        <TabsList>
                          <TabsTrigger value="python">Python</TabsTrigger>
                          <TabsTrigger value="matlab">MATLAB</TabsTrigger>
                        </TabsList>
                        <TabsContent value="python">
                          <div className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto">
                            <pre>{`import cv2
from skimage import exposure, filters

def preprocess_mri_volume(image_volume):
    """Comprehensive preprocessing pipeline"""
    processed_volume = np.zeros_like(image_volume, dtype=np.float32)
    
    for i in range(image_volume.shape[0]):
        slice_img = image_volume[i].astype(np.float32)
        
        # Contrast Adaptation - CLAHE
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        slice_img = clahe.apply(slice_img.astype(np.uint8))
        
        # Brightness Adaptation
        slice_img = exposure.equalize_hist(slice_img)
        
        # Apply Median Filter
        slice_img = filters.median(slice_img, disk(3))
        
        processed_volume[i] = slice_img
    
    return processed_volume`}</pre>
                          </div>
                        </TabsContent>
                        <TabsContent value="matlab">
                          <div className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto">
                            <pre>{`function processedVolume = preprocessMRIVolume(imageVolume)
    [rows, cols, slices] = size(imageVolume);
    processedVolume = zeros(rows, cols, slices);
    
    for i = 1:slices
        sliceImg = double(imageVolume(:,:,i));
        
        % Contrast Adaptation - CLAHE
        sliceImg = adapthisteq(uint8(sliceImg));
        
        % Brightness Adaptation
        sliceImg = histeq(uint8(sliceImg));
        
        % Apply Median Filter
        sliceImg = medfilt2(sliceImg, [3 3]);
        
        processedVolume(:,:,i) = sliceImg;
    end
end`}</pre>
                          </div>
                        </TabsContent>
                      </Tabs>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Segmentation Tab */}
            <TabsContent value="segmentation">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Layers className="h-6 w-6 text-blue-600" />
                    <span>3. Image Segmentation</span>
                  </CardTitle>
                  <CardDescription>
                    Segment brain regions and identify tumor boundaries using advanced algorithms
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <img 
                        src={brainTumorClassification} 
                        alt="Brain Tumor Classification"
                        className="w-full h-64 object-cover rounded-lg mb-4"
                      />
                      <h4 className="font-semibold mb-2">Segmentation Methods:</h4>
                      <ul className="space-y-2 text-sm text-gray-600">
                        <li>• Region of Interest (ROI) Setting</li>
                        <li>• Active Contour Models</li>
                        <li>• Watershed Segmentation</li>
                        <li>• Deformable Models</li>
                      </ul>
                    </div>
                    <div>
                      <Tabs defaultValue="python" className="w-full">
                        <TabsList>
                          <TabsTrigger value="python">Python</TabsTrigger>
                          <TabsTrigger value="matlab">MATLAB</TabsTrigger>
                        </TabsList>
                        <TabsContent value="python">
                          <div className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto">
                            <pre>{`from skimage.segmentation import active_contour, watershed
from skimage.feature import peak_local_maxima

def deformable_segmentation(image_slice, initial_contour=None):
    """Apply deformable segmentation models"""
    if initial_contour is None:
        # Create circular initial contour
        center = np.array(image_slice.shape) // 2
        radius = min(image_slice.shape) // 4
        theta = np.linspace(0, 2*np.pi, 100)
        initial_contour = np.column_stack([
            center[1] + radius * np.cos(theta),
            center[0] + radius * np.sin(theta)
        ])
    
    # Active contour segmentation
    snake = active_contour(
        image_slice, 
        initial_contour,
        alpha=0.015,
        beta=10,
        gamma=0.001
    )
    
    return snake`}</pre>
                          </div>
                        </TabsContent>
                        <TabsContent value="matlab">
                          <div className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto">
                            <pre>{`function segmentedImage = deformableSegmentation(imageSlice)
    % Active contour using activecontour function
    initialMask = false(size(imageSlice));
    [rows, cols] = size(imageSlice);
    center = [rows/2, cols/2];
    radius = min(rows, cols)/4;
    
    % Create circular initial mask
    [X, Y] = meshgrid(1:cols, 1:rows);
    initialMask = (X - center(2)).^2 + (Y - center(1)).^2 <= radius^2;
    
    % Active contour evolution
    segmentedImage = activecontour(imageSlice, initialMask, 300, 'Chan-Vese');
end`}</pre>
                          </div>
                        </TabsContent>
                      </Tabs>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Feature Extraction Tab */}
            <TabsContent value="features">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <BarChart3 className="h-6 w-6 text-blue-600" />
                    <span>4. Feature Extraction and Selection</span>
                  </CardTitle>
                  <CardDescription>
                    Extract meaningful features from segmented regions for tumor characterization
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <img 
                        src={medicalWorkflow} 
                        alt="Medical Workflow"
                        className="w-full h-64 object-cover rounded-lg mb-4"
                      />
                      <h4 className="font-semibold mb-2">Feature Types:</h4>
                      <ul className="space-y-2 text-sm text-gray-600">
                        <li>• Texture Features (GLCM)</li>
                        <li>• Shape Features</li>
                        <li>• Statistical Features</li>
                        <li>• Local Binary Patterns</li>
                        <li>• Feature Selection</li>
                      </ul>
                    </div>
                    <div>
                      <Tabs defaultValue="python" className="w-full">
                        <TabsList>
                          <TabsTrigger value="python">Python</TabsTrigger>
                          <TabsTrigger value="matlab">MATLAB</TabsTrigger>
                        </TabsList>
                        <TabsContent value="python">
                          <div className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto">
                            <pre>{`from skimage.feature import graycomatrix, graycoprops
from scipy.stats import skew, kurtosis

def extract_texture_features(image_slice, segmentation_mask):
    """Extract comprehensive texture features"""
    masked_image = image_slice * segmentation_mask
    masked_image_uint8 = (masked_image * 255).astype(np.uint8)
    
    features = {}
    
    # Gray Level Co-occurrence Matrix features
    glcm = graycomatrix(masked_image_uint8, [1], [0, 45, 90, 135], levels=256)
    features['contrast'] = graycoprops(glcm, 'contrast').mean()
    features['homogeneity'] = graycoprops(glcm, 'homogeneity').mean()
    features['energy'] = graycoprops(glcm, 'energy').mean()
    
    # Statistical features
    pixel_values = masked_image[segmentation_mask]
    features['mean'] = np.mean(pixel_values)
    features['std'] = np.std(pixel_values)
    features['skewness'] = skew(pixel_values)
    
    return features`}</pre>
                          </div>
                        </TabsContent>
                        <TabsContent value="matlab">
                          <div className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto">
                            <pre>{`function features = extractTextureFeatures(imageSlice, segmentationMask)
    % Extract comprehensive texture features
    maskedImage = imageSlice .* segmentationMask;
    maskedImageUint8 = uint8(maskedImage * 255);
    
    % Gray Level Co-occurrence Matrix features
    glcm = graycomatrix(maskedImageUint8, 'Offset', [0 1; -1 1; -1 0; -1 -1]);
    stats = graycoprops(glcm, {'contrast', 'correlation', 'energy', 'homogeneity'});
    
    features.contrast = mean(stats.Contrast);
    features.correlation = mean(stats.Correlation);
    features.energy = mean(stats.Energy);
    features.homogeneity = mean(stats.Homogeneity);
    
    % Statistical features
    pixelValues = maskedImage(segmentationMask);
    features.mean = mean(pixelValues);
    features.std = std(pixelValues);
end`}</pre>
                          </div>
                        </TabsContent>
                      </Tabs>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* 3D Reconstruction Tab */}
            <TabsContent value="reconstruction">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Cpu className="h-6 w-6 text-blue-600" />
                    <span>5. 3D Image Reconstruction</span>
                  </CardTitle>
                  <CardDescription>
                    Reconstruct 3D tumor models from segmented 2D slices
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <img 
                        src={brain3DReconstruction} 
                        alt="3D Brain Reconstruction"
                        className="w-full h-64 object-cover rounded-lg mb-4"
                      />
                      <h4 className="font-semibold mb-2">Reconstruction Features:</h4>
                      <ul className="space-y-2 text-sm text-gray-600">
                        <li>• Volume Interpolation</li>
                        <li>• Isotropic Voxel Creation</li>
                        <li>• 3D Visualization</li>
                        <li>• Orthogonal Views</li>
                        <li>• Surface Rendering</li>
                      </ul>
                    </div>
                    <div>
                      <Tabs defaultValue="python" className="w-full">
                        <TabsList>
                          <TabsTrigger value="python">Python</TabsTrigger>
                          <TabsTrigger value="matlab">MATLAB</TabsTrigger>
                        </TabsList>
                        <TabsContent value="python">
                          <div className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto">
                            <pre>{`from scipy.interpolate import RegularGridInterpolator
from skimage.measure import marching_cubes

def reconstruct_3d_volume(segmented_slices, slice_thickness=1.0):
    """Reconstruct 3D volume from segmented slices"""
    # Stack slices to form 3D volume
    volume_3d = np.stack(segmented_slices, axis=2)
    
    # Interpolate to create isotropic voxels
    original_shape = volume_3d.shape
    target_shape = (original_shape[0], original_shape[1], 
                   int(original_shape[2] * slice_thickness))
    
    # Create coordinate grids
    x = np.arange(original_shape[0])
    y = np.arange(original_shape[1])
    z = np.arange(original_shape[2])
    
    # Create interpolator
    interpolator = RegularGridInterpolator((x, y, z), volume_3d, 
                                         method='linear')
    
    return reconstructed`}</pre>
                          </div>
                        </TabsContent>
                        <TabsContent value="matlab">
                          <div className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto">
                            <pre>{`function reconstructed3D = reconstruct3DVolume(segmentedSlices, sliceThickness)
    if nargin < 2
        sliceThickness = 1.0;
    end
    
    % Stack slices to form 3D volume
    volume3D = cat(3, segmentedSlices{:});
    
    % Get original dimensions
    [rows, cols, slices] = size(volume3D);
    
    % Create isotropic volume
    targetSlices = round(slices * sliceThickness);
    
    % Interpolate along z-axis
    [X, Y, Z] = meshgrid(1:cols, 1:rows, 1:slices);
    [Xi, Yi, Zi] = meshgrid(1:cols, 1:rows, linspace(1, slices, targetSlices));
    
    reconstructed3D = interp3(X, Y, Z, volume3D, Xi, Yi, Zi, 'linear');
end`}</pre>
                          </div>
                        </TabsContent>
                      </Tabs>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Results Tab */}
            <TabsContent value="results">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Brain className="h-6 w-6 text-blue-600" />
                    <span>6. Results Calculation and Comparison</span>
                  </CardTitle>
                  <CardDescription>
                    Evaluate segmentation performance and compare different methods
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <img 
                        src={brainTumorClassification} 
                        alt="Results Comparison"
                        className="w-full h-64 object-cover rounded-lg mb-4"
                      />
                      <h4 className="font-semibold mb-2">Performance Metrics:</h4>
                      <ul className="space-y-2 text-sm text-gray-600">
                        <li>• Dice Coefficient</li>
                        <li>• Jaccard Index</li>
                        <li>• Accuracy & Precision</li>
                        <li>• Sensitivity & Specificity</li>
                        <li>• Tumor Volume Calculation</li>
                      </ul>
                    </div>
                    <div>
                      <Tabs defaultValue="python" className="w-full">
                        <TabsList>
                          <TabsTrigger value="python">Python</TabsTrigger>
                          <TabsTrigger value="matlab">MATLAB</TabsTrigger>
                        </TabsList>
                        <TabsContent value="python">
                          <div className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto">
                            <pre>{`from sklearn.metrics import accuracy_score, precision_score

def calculate_segmentation_metrics(ground_truth, predicted_mask):
    """Calculate segmentation performance metrics"""
    gt_flat = ground_truth.flatten()
    pred_flat = predicted_mask.flatten()
    
    # Calculate metrics
    accuracy = accuracy_score(gt_flat, pred_flat)
    precision = precision_score(gt_flat, pred_flat, average='weighted')
    
    # Calculate Dice coefficient
    intersection = np.sum(gt_flat * pred_flat)
    dice = (2.0 * intersection) / (np.sum(gt_flat) + np.sum(pred_flat))
    
    # Calculate Jaccard index
    union = np.sum(gt_flat) + np.sum(pred_flat) - intersection
    jaccard = intersection / union if union > 0 else 0
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'dice_coefficient': dice,
        'jaccard_index': jaccard
    }`}</pre>
                          </div>
                        </TabsContent>
                        <TabsContent value="matlab">
                          <div className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto">
                            <pre>{`function metrics = calculateSegmentationMetrics(groundTruth, predictedMask)
    % Calculate segmentation performance metrics
    gtFlat = groundTruth(:);
    predFlat = predictedMask(:);
    
    % Calculate confusion matrix
    confMat = confusionmat(gtFlat, predFlat);
    
    if size(confMat, 1) >= 2
        TP = confMat(2,2);
        TN = confMat(1,1);
        FP = confMat(1,2);
        FN = confMat(2,1);
        
        metrics.accuracy = (TP + TN) / (TP + TN + FP + FN);
        metrics.precision = TP / (TP + FP);
        
        % Dice coefficient
        intersection = sum(gtFlat .* predFlat);
        metrics.diceCoefficient = (2 * intersection) / (sum(gtFlat) + sum(predFlat));
    end
end`}</pre>
                          </div>
                        </TabsContent>
                      </Tabs>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Brain className="h-6 w-6" />
                <span className="text-lg font-semibold">Brain Tumor Detection</span>
              </div>
              <p className="text-gray-400">
                Advanced medical imaging analysis for brain tumor detection and 3D reconstruction.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Technologies</h4>
              <ul className="space-y-2 text-gray-400">
                <li>Python & MATLAB</li>
                <li>OpenCV & scikit-image</li>
                <li>DICOM Processing</li>
                <li>3D Reconstruction</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Resources</h4>
              <ul className="space-y-2 text-gray-400">
                <li>Documentation</li>
                <li>Sample Datasets</li>
                <li>Research Papers</li>
                <li>Code Repository</li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Brain Tumor Detection System. Advanced Medical Imaging Analysis.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default App

