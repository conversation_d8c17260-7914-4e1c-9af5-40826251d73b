Brain Tumor Detection in Reconstructed 3D MRI Images
1. Load DICOM Image Set
Python Implementation
pythonimport pydicom
import numpy as np
import os
from pathlib import Path
import matplotlib.pyplot as plt

def load_dicom_series(dicom_folder):
    """Load DICOM series from a folder"""
    dicom_files = []
    for filename in os.listdir(dicom_folder):
        if filename.endswith('.dcm'):
            filepath = os.path.join(dicom_folder, filename)
            dicom_files.append(pydicom.dcmread(filepath))
    
    # Sort by slice location
    dicom_files.sort(key=lambda x: float(x.SliceLocation))
    
    # Extract pixel arrays
    image_stack = np.stack([ds.pixel_array for ds in dicom_files])
    
    return image_stack, dicom_files

# Usage
dicom_folder = "path/to/dicom/files"
image_volume, dicom_info = load_dicom_series(dicom_folder)
print(f"Loaded volume shape: {image_volume.shape}")
MATLAB Implementation
matlabfunction [imageVolume, dicomInfo] = loadDicomSeries(dicomFolder)
    % Load DICOM series from folder
    dicomFiles = dir(fullfile(dicomFolder, '*.dcm'));
    numFiles = length(dicomFiles);
    
    % Read first image to get dimensions
    firstImage = dicomread(fullfile(dicomFolder, dicomFiles(1).name));
    [rows, cols] = size(firstImage);
    
    % Initialize volume
    imageVolume = zeros(rows, cols, numFiles);
    dicomInfo = cell(numFiles, 1);
    
    % Load all slices
    for i = 1:numFiles
        filename = fullfile(dicomFolder, dicomFiles(i).name);
        imageVolume(:,:,i) = dicomread(filename);
        dicomInfo{i} = dicominfo(filename);
    end
    
    fprintf('Loaded volume size: %d x %d x %d\n', size(imageVolume));
end
2. Preprocessing
Python Implementation
pythonimport cv2
from skimage import exposure, filters
from scipy import ndimage

def preprocess_mri_volume(image_volume):
    """Comprehensive preprocessing pipeline"""
    processed_volume = np.zeros_like(image_volume, dtype=np.float32)
    
    for i in range(image_volume.shape[0]):
        slice_img = image_volume[i].astype(np.float32)
        
        # Contrast Adaptation - CLAHE
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        slice_img = clahe.apply(slice_img.astype(np.uint8)).astype(np.float32)
        
        # Brightness Adaptation - Histogram Equalization
        slice_img = exposure.equalize_hist(slice_img)
        
        # Apply Median Filter
        slice_img = filters.median(slice_img, disk(3))
        
        # RGB to Grayscale (if needed)
        if len(slice_img.shape) == 3:
            slice_img = cv2.cvtColor(slice_img, cv2.COLOR_RGB2GRAY)
        
        # Normalize to [0, 1]
        slice_img = (slice_img - np.min(slice_img)) / (np.max(slice_img) - np.min(slice_img))
        
        processed_volume[i] = slice_img
    
    return processed_volume

def apply_filters(image_volume):
    """Apply various filters for enhancement"""
    filtered_volume = np.zeros_like(image_volume)
    
    for i in range(image_volume.shape[0]):
        # Gaussian filter for noise reduction
        filtered_volume[i] = ndimage.gaussian_filter(image_volume[i], sigma=1.0)
        
        # Edge enhancement
        filtered_volume[i] = filters.unsharp_mask(filtered_volume[i], radius=1, amount=1)
    
    return filtered_volume
MATLAB Implementation
matlabfunction processedVolume = preprocessMRIVolume(imageVolume)
    % Comprehensive preprocessing pipeline
    [rows, cols, slices] = size(imageVolume);
    processedVolume = zeros(rows, cols, slices);
    
    for i = 1:slices
        sliceImg = double(imageVolume(:,:,i));
        
        % Contrast Adaptation - CLAHE
        sliceImg = adapthisteq(uint8(sliceImg));
        sliceImg = double(sliceImg);
        
        % Brightness Adaptation
        sliceImg = histeq(uint8(sliceImg));
        sliceImg = double(sliceImg);
        
        % Apply Median Filter
        sliceImg = medfilt2(sliceImg, [3 3]);
        
        % Normalize to [0, 1]
        sliceImg = (sliceImg - min(sliceImg(:))) / (max(sliceImg(:)) - min(sliceImg(:)));
        
        processedVolume(:,:,i) = sliceImg;
    end
end

function filteredVolume = applyFilters(imageVolume)
    % Apply various filters for enhancement
    [rows, cols, slices] = size(imageVolume);
    filteredVolume = zeros(rows, cols, slices);
    
    for i = 1:slices
        % Gaussian filter
        filteredVolume(:,:,i) = imgaussfilt(imageVolume(:,:,i), 1.0);
        
        % Edge enhancement using unsharp masking
        filteredVolume(:,:,i) = imsharpen(filteredVolume(:,:,i));
    end
end
3. Image Segmentation
Python Implementation
pythonfrom skimage.segmentation import active_contour, watershed
from skimage.feature import peak_local_maxima
from scipy.ndimage import distance_transform_edt

def set_roi(image_slice, roi_coordinates=None):
    """Set Region of Interest"""
    if roi_coordinates is None:
        # Auto-detect brain region
        threshold = filters.threshold_otsu(image_slice)
        brain_mask = image_slice > threshold
        
        # Find largest connected component
        labeled_mask = measure.label(brain_mask)
        regions = measure.regionprops(labeled_mask)
        largest_region = max(regions, key=lambda x: x.area)
        
        roi_mask = labeled_mask == largest_region.label
        return roi_mask
    else:
        # Manual ROI
        mask = np.zeros_like(image_slice, dtype=bool)
        x1, y1, x2, y2 = roi_coordinates
        mask[y1:y2, x1:x2] = True
        return mask

def deformable_segmentation(image_slice, initial_contour=None):
    """Apply deformable segmentation models"""
    if initial_contour is None:
        # Create circular initial contour
        center = np.array(image_slice.shape) // 2
        radius = min(image_slice.shape) // 4
        theta = np.linspace(0, 2*np.pi, 100)
        initial_contour = np.column_stack([
            center[1] + radius * np.cos(theta),
            center[0] + radius * np.sin(theta)
        ])
    
    # Active contour segmentation
    snake = active_contour(
        image_slice, 
        initial_contour,
        alpha=0.015,
        beta=10,
        gamma=0.001
    )
    
    return snake

def watershed_segmentation(image_slice):
    """Watershed segmentation for tumor detection"""
    # Distance transform
    distance = distance_transform_edt(image_slice > filters.threshold_otsu(image_slice))
    
    # Find local maxima
    local_maxima = peak_local_maxima(distance, min_distance=20, threshold_abs=0.3)
    markers = np.zeros_like(image_slice, dtype=int)
    markers[tuple(local_maxima.T)] = np.arange(len(local_maxima)) + 1
    
    # Watershed
    labels = watershed(-distance, markers, mask=image_slice > 0)
    
    return labels
MATLAB Implementation
matlabfunction roiMask = setROI(imageSlice, roiCoordinates)
    % Set Region of Interest
    if nargin < 2
        % Auto-detect brain region
        threshold = graythresh(imageSlice);
        brainMask = imageSlice > threshold;
        
        % Find largest connected component
        cc = bwconncomp(brainMask);
        numPixels = cellfun(@numel, cc.PixelIdxList);
        [~, idx] = max(numPixels);
        
        roiMask = false(size(imageSlice));
        roiMask(cc.PixelIdxList{idx}) = true;
    else
        % Manual ROI
        roiMask = false(size(imageSlice));
        x1 = roiCoordinates(1); y1 = roiCoordinates(2);
        x2 = roiCoordinates(3); y2 = roiCoordinates(4);
        roiMask(y1:y2, x1:x2) = true;
    end
end

function segmentedImage = deformableSegmentation(imageSlice)
    % Apply deformable segmentation
    
    % Active contour using activecontour function
    initialMask = false(size(imageSlice));
    [rows, cols] = size(imageSlice);
    center = [rows/2, cols/2];
    radius = min(rows, cols)/4;
    
    % Create circular initial mask
    [X, Y] = meshgrid(1:cols, 1:rows);
    initialMask = (X - center(2)).^2 + (Y - center(1)).^2 <= radius^2;
    
    % Active contour evolution
    segmentedImage = activecontour(imageSlice, initialMask, 300, 'Chan-Vese');
end

function labels = watershedSegmentation(imageSlice)
    % Watershed segmentation
    
    % Distance transform
    bw = imageSlice > graythresh(imageSlice);
    D = -bwdist(~bw);
    
    % Find local minima
    mask = imextendedmin(D, 2);
    D2 = imimposemin(D, mask);
    
    % Watershed
    labels = watershed(D2);
    labels(~bw) = 0;
end
4. Feature Extraction and Selection
Python Implementation
pythonfrom skimage.feature import graycomatrix, graycoprops, local_binary_pattern
from scipy.stats import skew, kurtosis

def extract_texture_features(image_slice, segmentation_mask):
    """Extract comprehensive texture features"""
    # Mask the image
    masked_image = image_slice * segmentation_mask
    
    # Convert to uint8 for GLCM
    masked_image_uint8 = (masked_image * 255).astype(np.uint8)
    
    features = {}
    
    # Gray Level Co-occurrence Matrix features
    glcm = graycomatrix(masked_image_uint8, [1], [0, 45, 90, 135], levels=256)
    features['contrast'] = graycoprops(glcm, 'contrast').mean()
    features['dissimilarity'] = graycoprops(glcm, 'dissimilarity').mean()
    features['homogeneity'] = graycoprops(glcm, 'homogeneity').mean()
    features['energy'] = graycoprops(glcm, 'energy').mean()
    features['correlation'] = graycoprops(glcm, 'correlation').mean()
    
    # Local Binary Pattern
    lbp = local_binary_pattern(masked_image, 8, 1, method='uniform')
    features['lbp_variance'] = np.var(lbp[segmentation_mask])
    
    # Statistical features
    pixel_values = masked_image[segmentation_mask]
    features['mean'] = np.mean(pixel_values)
    features['std'] = np.std(pixel_values)
    features['skewness'] = skew(pixel_values)
    features['kurtosis'] = kurtosis(pixel_values)
    
    return features

def extract_shape_features(segmentation_mask):
    """Extract shape-based features"""
    from skimage import measure
    
    # Label connected components
    labeled_mask = measure.label(segmentation_mask)
    regions = measure.regionprops(labeled_mask)
    
    if not regions:
        return {}
    
    # Get largest region (assumed to be tumor)
    largest_region = max(regions, key=lambda x: x.area)
    
    features = {
        'area': largest_region.area,
        'perimeter': largest_region.perimeter,
        'eccentricity': largest_region.eccentricity,
        'solidity': largest_region.solidity,
        'compactness': (largest_region.perimeter**2) / (4 * np.pi * largest_region.area),
        'major_axis_length': largest_region.major_axis_length,
        'minor_axis_length': largest_region.minor_axis_length
    }
    
    return features

def feature_selection(feature_matrix, labels, k=10):
    """Select top k features using mutual information"""
    from sklearn.feature_selection import SelectKBest, mutual_info_classif
    
    selector = SelectKBest(mutual_info_classif, k=k)
    selected_features = selector.fit_transform(feature_matrix, labels)
    selected_indices = selector.get_support(indices=True)
    
    return selected_features, selected_indices
MATLAB Implementation
matlabfunction features = extractTextureFeatures(imageSlice, segmentationMask)
    % Extract comprehensive texture features
    
    % Mask the image
    maskedImage = imageSlice .* segmentationMask;
    
    % Convert to uint8 for GLCM
    maskedImageUint8 = uint8(maskedImage * 255);
    
    % Gray Level Co-occurrence Matrix features
    glcm = graycomatrix(maskedImageUint8, 'Offset', [0 1; -1 1; -1 0; -1 -1]);
    stats = graycoprops(glcm, {'contrast', 'correlation', 'energy', 'homogeneity'});
    
    features.contrast = mean(stats.Contrast);
    features.correlation = mean(stats.Correlation);
    features.energy = mean(stats.Energy);
    features.homogeneity = mean(stats.Homogeneity);
    
    % Statistical features
    pixelValues = maskedImage(segmentationMask);
    features.mean = mean(pixelValues);
    features.std = std(pixelValues);
    features.skewness = skewness(pixelValues);
    features.kurtosis = kurtosis(pixelValues);
end

function features = extractShapeFeatures(segmentationMask)
    % Extract shape-based features
    
    % Get region properties
    props = regionprops(segmentationMask, 'Area', 'Perimeter', 'Eccentricity', ...
                       'Solidity', 'MajorAxisLength', 'MinorAxisLength');
    
    if isempty(props)
        features = struct();
        return;
    end
    
    % Get largest region
    [~, idx] = max([props.Area]);
    region = props(idx);
    
    features.area = region.Area;
    features.perimeter = region.Perimeter;
    features.eccentricity = region.Eccentricity;
    features.solidity = region.Solidity;
    features.compactness = (region.Perimeter^2) / (4 * pi * region.Area);
    features.majorAxisLength = region.MajorAxisLength;
    features.minorAxisLength = region.MinorAxisLength;
end

function [selectedFeatures, selectedIndices] = featureSelection(featureMatrix, labels, k)
    % Select top k features using Relief algorithm
    if nargin < 3
        k = 10;
    end
    
    % Use Relief algorithm for feature selection
    [indices, weights] = relieff(featureMatrix, labels, k);
    selectedIndices = indices(1:k);
    selectedFeatures = featureMatrix(:, selectedIndices);
end
5. 3D Image Reconstruction
Python Implementation
pythonimport numpy as np
from scipy.interpolate import RegularGridInterpolator
from skimage.transform import resize

def reconstruct_3d_volume(segmented_slices, slice_thickness=1.0):
    """Reconstruct 3D volume from segmented slices"""
    # Stack slices to form 3D volume
    volume_3d = np.stack(segmented_slices, axis=2)
    
    # Interpolate to create isotropic voxels
    original_shape = volume_3d.shape
    target_shape = (original_shape[0], original_shape[1], 
                   int(original_shape[2] * slice_thickness))
    
    # Create coordinate grids
    x = np.arange(original_shape[0])
    y = np.arange(original_shape[1])
    z = np.arange(original_shape[2])
    
    # Create interpolator
    interpolator = RegularGridInterpolator((x, y, z), volume_3d, 
                                         method='linear', bounds_error=False)
    
    # New coordinate grid
    new_x = np.linspace(0, original_shape[0]-1, original_shape[0])
    new_y = np.linspace(0, original_shape[1]-1, original_shape[1])
    new_z = np.linspace(0, original_shape[2]-1, target_shape[2])
    
    xx, yy, zz = np.meshgrid(new_x, new_y, new_z, indexing='ij')
    points = np.column_stack([xx.ravel(), yy.ravel(), zz.ravel()])
    
    # Interpolate
    reconstructed = interpolator(points).reshape(target_shape)
    
    return reconstructed

def reconstruct_tumor_3d(tumor_masks, original_images):
    """Reconstruct 3D tumor from segmentation masks"""
    # Combine tumor masks with original intensity information
    tumor_3d = np.zeros_like(original_images)
    
    for i in range(len(tumor_masks)):
        tumor_3d[i] = original_images[i] * tumor_masks[i]
    
    return tumor_3d

def visualize_3d_reconstruction(volume_3d):
    """Visualize 3D reconstruction using matplotlib"""
    from mpl_toolkits.mplot3d import Axes3D
    
    fig = plt.figure(figsize=(12, 8))
    
    # Volume rendering using contour plots
    ax1 = fig.add_subplot(221, projection='3d')
    x, y, z = np.meshgrid(np.arange(volume_3d.shape[1]), 
                         np.arange(volume_3d.shape[0]), 
                         np.arange(volume_3d.shape[2]))
    
    # Create isosurface
    from skimage.measure import marching_cubes
    verts, faces, _, _ = marching_cubes(volume_3d, level=0.5)
    ax1.plot_trisurf(verts[:, 0], verts[:, 1], verts[:, 2], 
                     triangles=faces, alpha=0.7, cmap='viridis')
    ax1.set_title('3D Tumor Reconstruction')
    
    # Orthogonal views
    ax2 = fig.add_subplot(222)
    ax2.imshow(volume_3d[:, :, volume_3d.shape[2]//2], cmap='gray')
    ax2.set_title('Axial View')
    
    ax3 = fig.add_subplot(223)
    ax3.imshow(volume_3d[:, volume_3d.shape[1]//2, :], cmap='gray')
    ax3.set_title('Coronal View')
    
    ax4 = fig.add_subplot(224)
    ax4.imshow(volume_3d[volume_3d.shape[0]//2, :, :], cmap='gray')
    ax4.set_title('Sagittal View')
    
    plt.tight_layout()
    plt.show()
MATLAB Implementation
matlabfunction reconstructed3D = reconstruct3DVolume(segmentedSlices, sliceThickness)
    % Reconstruct 3D volume from segmented slices
    if nargin < 2
        sliceThickness = 1.0;
    end
    
    % Stack slices to form 3D volume
    volume3D = cat(3, segmentedSlices{:});
    
    % Get original dimensions
    [rows, cols, slices] = size(volume3D);
    
    % Create isotropic volume
    targetSlices = round(slices * sliceThickness);
    
    % Interpolate along z-axis
    [X, Y, Z] = meshgrid(1:cols, 1:rows, 1:slices);
    [Xi, Yi, Zi] = meshgrid(1:cols, 1:rows, linspace(1, slices, targetSlices));
    
    reconstructed3D = interp3(X, Y, Z, volume3D, Xi, Yi, Zi, 'linear');
end

function tumor3D = reconstructTumor3D(tumorMasks, originalImages)
    % Reconstruct 3D tumor from segmentation masks
    
    numSlices = length(tumorMasks);
    [rows, cols] = size(originalImages{1});
    
    tumor3D = zeros(rows, cols, numSlices);
    
    for i = 1:numSlices
        tumor3D(:,:,i) = originalImages{i} .* tumorMasks{i};
    end
end

function visualize3DReconstruction(volume3D)
    % Visualize 3D reconstruction
    
    figure('Position', [100, 100, 1200, 800]);
    
    % 3D volume rendering
    subplot(2,2,1);
    [faces, vertices] = isosurface(volume3D, 0.5);
    p = patch('Faces', faces, 'Vertices', vertices, 'FaceColor', 'red', ...
              'EdgeColor', 'none', 'FaceAlpha', 0.7);
    daspect([1 1 1]);
    view(3);
    camlight;
    lighting gouraud;
    title('3D Tumor Reconstruction');
    
    % Orthogonal views
    [rows, cols, slices] = size(volume3D);
    
    subplot(2,2,2);
    imshow(volume3D(:,:,round(slices/2)), []);
    title('Axial View');
    
    subplot(2,2,3);
    imshow(squeeze(volume3D(:,round(cols/2),:)), []);
    title('Coronal View');
    
    subplot(2,2,4);
    imshow(squeeze(volume3D(round(rows/2),:,:)), []);
    title('Sagittal View');
end
6. Results Calculation and Comparison
Python Implementation
pythonfrom sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.metrics import confusion_matrix, classification_report
import pandas as pd

def calculate_segmentation_metrics(ground_truth, predicted_mask):
    """Calculate segmentation performance metrics"""
    # Flatten masks for calculation
    gt_flat = ground_truth.flatten()
    pred_flat = predicted_mask.flatten()
    
    # Calculate metrics
    accuracy = accuracy_score(gt_flat, pred_flat)
    precision = precision_score(gt_flat, pred_flat, average='weighted')
    recall = recall_score(gt_flat, pred_flat, average='weighted')
    f1 = f1_score(gt_flat, pred_flat, average='weighted')
    
    # Calculate Dice coefficient
    intersection = np.sum(gt_flat * pred_flat)
    dice = (2.0 * intersection) / (np.sum(gt_flat) + np.sum(pred_flat))
    
    # Calculate Jaccard index
    union = np.sum(gt_flat) + np.sum(pred_flat) - intersection
    jaccard = intersection / union if union > 0 else 0
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'dice_coefficient': dice,
        'jaccard_index': jaccard
    }

def compare_segmentation_methods(ground_truth, method_results):
    """Compare different segmentation methods"""
    comparison_results = {}
    
    for method_name, predicted_mask in method_results.items():
        metrics = calculate_segmentation_metrics(ground_truth, predicted_mask)
        comparison_results[method_name] = metrics
    
    # Create comparison DataFrame
    df = pd.DataFrame(comparison_results).T
    
    return df

def calculate_tumor_volume(segmentation_mask, voxel_size=(1.0, 1.0, 1.0)):
    """Calculate tumor volume from segmentation"""
    # Count tumor voxels
    tumor_voxels = np.sum(segmentation_mask)
    
    # Calculate volume in cubic units
    voxel_volume = np.prod(voxel_size)
    tumor_volume = tumor_voxels * voxel_volume
    
    return tumor_volume

def generate_performance_report(results_dict):
    """Generate comprehensive performance report"""
    report = {
        'segmentation_metrics': results_dict.get('segmentation_metrics', {}),
        'tumor_volume': results_dict.get('tumor_volume', 0),
        'processing_time': results_dict.get('processing_time', 0),
        'confidence_score': results_dict.get('confidence_score', 0)
    }
    
    return report

# Example usage for comparison
def main_comparison():
    # Simulate different segmentation methods
    methods = ['Active Contour', 'Watershed', 'Region Growing', 'Deep Learning']
    
    # Load ground truth and results (placeholder)
    ground_truth = np.random.randint(0, 2, (100, 100, 50))
    
    method_results = {}
    for method in methods:
        # Simulate segmentation results
        method_results[method] = np.random.randint(0, 2, (100, 100, 50))
    
    # Compare methods
    comparison_df = compare_segmentation_methods(ground_truth, method_results)
    
    print("Segmentation Methods Comparison:")
    print(comparison_df)
    
    # Find best method
    best_method = comparison_df['dice_coefficient'].idxmax()
    print(f"\nBest performing method: {best_method}")
    print(f"Dice coefficient: {comparison_df.loc[best_method, 'dice_coefficient']:.4f}")
MATLAB Implementation
matlabfunction metrics = calculateSegmentationMetrics(groundTruth, predictedMask)
    % Calculate segmentation performance metrics
    
    % Flatten masks
    gtFlat = groundTruth(:);
    predFlat = predictedMask(:);
    
    % Calculate confusion matrix
    confMat = confusionmat(gtFlat, predFlat);
    
    % Calculate metrics
    if size(confMat, 1) >= 2
        TP = confMat(2,2);
        TN = confMat(1,1);
        FP = confMat(1,2);
        FN = confMat(2,1);
        
        metrics.accuracy = (TP + TN) / (TP + TN + FP + FN);
        metrics.precision = TP / (TP + FP);
        metrics.recall = TP / (TP + FN);
        metrics.f1Score = 2 * (metrics.precision * metrics.recall) / ...
                         (metrics.precision + metrics.recall);
        
        % Dice coefficient
        intersection = sum(gtFlat .* predFlat);
        metrics.diceCoefficient = (2 * intersection) / (sum(gtFlat) + sum(predFlat));
        
        % Jaccard index
        union = sum(gtFlat) + sum(predFlat) - intersection;
        metrics.jaccardIndex = intersection / union;
    else
        metrics = struct('accuracy', 0, 'precision', 0, 'recall', 0, ...
                        'f1Score', 0, 'diceCoefficient', 0, 'jaccardIndex', 0);
    end
end

function comparisonTable = compareSegmentationMethods(groundTruth, methodResults)
    % Compare different segmentation methods
    
    methods = fieldnames(methodResults);
    numMethods = length(methods);
    
    % Initialize results table
    metricNames = {'accuracy', 'precision', 'recall', 'f1Score', ...
                   'diceCoefficient', 'jaccardIndex'};
    comparisonTable = table();
    
    for i = 1:numMethods
        methodName = methods{i};
        predictedMask = methodResults.(methodName);
        
        metrics = calculateSegmentationMetrics(groundTruth, predictedMask);
        
        % Add to table
        for j = 1:length(metricNames)
            comparisonTable.(metricNames{j})(i) = metrics.(metricNames{j});
        end
    end
    
    comparisonTable.Properties.RowNames = methods;
    
    % Display results
    fprintf('Segmentation Methods Comparison:\n');
    disp(comparisonTable);
    
    % Find best method
    [maxDice, bestIdx] = max(comparisonTable.diceCoefficient);
    fprintf('\nBest performing method: %s\n', methods{bestIdx});
    fprintf('Dice coefficient: %.4f\n', maxDice);
end

function tumorVolume = calculateTumorVolume(segmentationMask, voxelSize)
    % Calculate tumor volume from segmentation
    if nargin < 2
        voxelSize = [1.0, 1.0, 1.0];
    end
    
    % Count tumor voxels
    tumorVoxels = sum(segmentationMask(:));
    
    % Calculate volume
    voxelVolume = prod(voxelSize);
    tumorVolume = tumorVoxels * voxelVolume;
    
    fprintf('Tumor volume: %.2f cubic units\n', tumorVolume);
end

function report = generatePerformanceReport(resultsStruct)
    % Generate comprehensive performance report
    
    report = struct();
    report.segmentationMetrics = resultsStruct.segmentationMetrics;
    report.tumorVolume =