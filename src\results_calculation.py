"""
Results Calculation and Comparison Module
========================================

This module provides comprehensive evaluation metrics for segmentation performance
and comparison between different methods. It includes statistical analysis,
visualization, and comprehensive reporting capabilities.

Author: Dr<PERSON> <PERSON>
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional, Any, Union
from scipy import stats
from sklearn.metrics import (
    confusion_matrix, classification_report, roc_curve, auc,
    precision_recall_curve, average_precision_score
)
import warnings
import json
import time
from datetime import datetime
from pathlib import Path
import cv2
from scipy.spatial.distance import directed_hausdorff
from scipy.ndimage import binary_erosion, binary_dilation
warnings.filterwarnings('ignore')


class SegmentationMetrics:
    """
    Comprehensive segmentation metrics calculator.
    """
    
    def __init__(self):
        """Initialize metrics calculator."""
        self.metrics_cache = {}
        self.computation_times = {}
        
    def dice_coefficient(self, y_true: np.ndarray, y_pred: np.ndarray, 
                        smooth: float = 1e-6) -> float:
        """
        Calculate Dice Coefficient (F1 Score for segmentation).
        
        Args:
            y_true: Ground truth binary mask
            y_pred: Predicted binary mask
            smooth: Smoothing factor to avoid division by zero
            
        Returns:
            Dice coefficient value
        """
        y_true_f = y_true.flatten()
        y_pred_f = y_pred.flatten()
        
        intersection = np.sum(y_true_f * y_pred_f)
        union = np.sum(y_true_f) + np.sum(y_pred_f)
        
        dice = (2.0 * intersection + smooth) / (union + smooth)
        
        return float(dice)
    
    def jaccard_index(self, y_true: np.ndarray, y_pred: np.ndarray, 
                     smooth: float = 1e-6) -> float:
        """
        Calculate Jaccard Index (Intersection over Union).
        
        Args:
            y_true: Ground truth binary mask
            y_pred: Predicted binary mask
            smooth: Smoothing factor to avoid division by zero
            
        Returns:
            Jaccard index value
        """
        y_true_f = y_true.flatten()
        y_pred_f = y_pred.flatten()
        
        intersection = np.sum(y_true_f * y_pred_f)
        union = np.sum(y_true_f) + np.sum(y_pred_f) - intersection
        
        jaccard = (intersection + smooth) / (union + smooth)
        
        return float(jaccard)
    
    def accuracy(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """
        Calculate pixel-wise accuracy.
        
        Args:
            y_true: Ground truth binary mask
            y_pred: Predicted binary mask
            
        Returns:
            Accuracy value
        """
        y_true_f = y_true.flatten()
        y_pred_f = y_pred.flatten()
        
        correct = np.sum(y_true_f == y_pred_f)
        total = len(y_true_f)
        
        return float(correct / total)
    
    def precision(self, y_true: np.ndarray, y_pred: np.ndarray, 
                 smooth: float = 1e-6) -> float:
        """
        Calculate precision (positive predictive value).
        
        Args:
            y_true: Ground truth binary mask
            y_pred: Predicted binary mask
            smooth: Smoothing factor to avoid division by zero
            
        Returns:
            Precision value
        """
        y_true_f = y_true.flatten()
        y_pred_f = y_pred.flatten()
        
        true_positives = np.sum(y_true_f * y_pred_f)
        predicted_positives = np.sum(y_pred_f)
        
        precision = (true_positives + smooth) / (predicted_positives + smooth)
        
        return float(precision)
    
    def recall(self, y_true: np.ndarray, y_pred: np.ndarray, 
              smooth: float = 1e-6) -> float:
        """
        Calculate recall (sensitivity, true positive rate).
        
        Args:
            y_true: Ground truth binary mask
            y_pred: Predicted binary mask
            smooth: Smoothing factor to avoid division by zero
            
        Returns:
            Recall value
        """
        y_true_f = y_true.flatten()
        y_pred_f = y_pred.flatten()
        
        true_positives = np.sum(y_true_f * y_pred_f)
        actual_positives = np.sum(y_true_f)
        
        recall = (true_positives + smooth) / (actual_positives + smooth)
        
        return float(recall)
    
    def specificity(self, y_true: np.ndarray, y_pred: np.ndarray, 
                   smooth: float = 1e-6) -> float:
        """
        Calculate specificity (true negative rate).
        
        Args:
            y_true: Ground truth binary mask
            y_pred: Predicted binary mask
            smooth: Smoothing factor to avoid division by zero
            
        Returns:
            Specificity value
        """
        y_true_f = y_true.flatten()
        y_pred_f = y_pred.flatten()
        
        true_negatives = np.sum((1 - y_true_f) * (1 - y_pred_f))
        actual_negatives = np.sum(1 - y_true_f)
        
        specificity = (true_negatives + smooth) / (actual_negatives + smooth)
        
        return float(specificity)
    
    def sensitivity(self, y_true: np.ndarray, y_pred: np.ndarray, 
                   smooth: float = 1e-6) -> float:
        """
        Calculate sensitivity (same as recall).
        
        Args:
            y_true: Ground truth binary mask
            y_pred: Predicted binary mask
            smooth: Smoothing factor to avoid division by zero
            
        Returns:
            Sensitivity value
        """
        return self.recall(y_true, y_pred, smooth)
    
    def f1_score(self, y_true: np.ndarray, y_pred: np.ndarray, 
                smooth: float = 1e-6) -> float:
        """
        Calculate F1 Score.
        
        Args:
            y_true: Ground truth binary mask
            y_pred: Predicted binary mask
            smooth: Smoothing factor to avoid division by zero
            
        Returns:
            F1 score value
        """
        prec = self.precision(y_true, y_pred, smooth)
        rec = self.recall(y_true, y_pred, smooth)
        
        f1 = (2 * prec * rec + smooth) / (prec + rec + smooth)
        
        return float(f1)
    
    def hausdorff_distance(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """
        Calculate Hausdorff distance between contours.
        
        Args:
            y_true: Ground truth binary mask
            y_pred: Predicted binary mask
            
        Returns:
            Hausdorff distance
        """
        # Find contours
        contours_true, _ = cv2.findContours(
            y_true.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        contours_pred, _ = cv2.findContours(
            y_pred.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if len(contours_true) == 0 or len(contours_pred) == 0:
            return float('inf')
        
        # Get largest contours
        contour_true = max(contours_true, key=cv2.contourArea)
        contour_pred = max(contours_pred, key=cv2.contourArea)
        
        # Convert to point arrays
        points_true = contour_true.reshape(-1, 2)
        points_pred = contour_pred.reshape(-1, 2)
        
        # Calculate Hausdorff distance
        hd1 = directed_hausdorff(points_true, points_pred)[0]
        hd2 = directed_hausdorff(points_pred, points_true)[0]
        
        return float(max(hd1, hd2))
    
    def average_surface_distance(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """
        Calculate average surface distance.
        
        Args:
            y_true: Ground truth binary mask
            y_pred: Predicted binary mask
            
        Returns:
            Average surface distance
        """
        # Get surface points
        surface_true = self._get_surface_points(y_true)
        surface_pred = self._get_surface_points(y_pred)
        
        if len(surface_true) == 0 or len(surface_pred) == 0:
            return float('inf')
        
        # Calculate distances
        distances = []
        for point_true in surface_true:
            min_dist = np.min(np.linalg.norm(surface_pred - point_true, axis=1))
            distances.append(min_dist)
        
        return float(np.mean(distances))
    
    def _get_surface_points(self, mask: np.ndarray) -> np.ndarray:
        """Get surface points from binary mask."""
        # Erode mask to get inner boundary
        eroded = binary_erosion(mask)
        
        # Surface is difference between original and eroded
        surface = mask.astype(bool) & ~eroded
        
        # Get coordinates of surface points
        surface_points = np.column_stack(np.where(surface))
        
        return surface_points
    
    def volume_similarity(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """
        Calculate volume similarity coefficient.
        
        Args:
            y_true: Ground truth binary mask
            y_pred: Predicted binary mask
            
        Returns:
            Volume similarity coefficient
        """
        vol_true = np.sum(y_true)
        vol_pred = np.sum(y_pred)
        
        if vol_true == 0 and vol_pred == 0:
            return 1.0
        
        vol_sim = 1.0 - abs(vol_true - vol_pred) / (vol_true + vol_pred)
        
        return float(vol_sim)
    
    def calculate_all_metrics(self, y_true: np.ndarray, y_pred: np.ndarray,
                            include_distances: bool = True) -> Dict[str, float]:
        """
        Calculate all segmentation metrics.
        
        Args:
            y_true: Ground truth binary mask
            y_pred: Predicted binary mask
            include_distances: Whether to include distance-based metrics
            
        Returns:
            Dictionary with all metrics
        """
        start_time = time.time()
        
        # Ensure binary masks
        y_true = (y_true > 0).astype(np.uint8)
        y_pred = (y_pred > 0).astype(np.uint8)
        
        metrics = {}
        
        # Basic metrics
        metrics['dice_coefficient'] = self.dice_coefficient(y_true, y_pred)
        metrics['jaccard_index'] = self.jaccard_index(y_true, y_pred)
        metrics['accuracy'] = self.accuracy(y_true, y_pred)
        metrics['precision'] = self.precision(y_true, y_pred)
        metrics['recall'] = self.recall(y_true, y_pred)
        metrics['sensitivity'] = self.sensitivity(y_true, y_pred)
        metrics['specificity'] = self.specificity(y_true, y_pred)
        metrics['f1_score'] = self.f1_score(y_true, y_pred)
        metrics['volume_similarity'] = self.volume_similarity(y_true, y_pred)
        
        # Distance-based metrics (optional due to computational cost)
        if include_distances:
            try:
                metrics['hausdorff_distance'] = self.hausdorff_distance(y_true, y_pred)
                metrics['average_surface_distance'] = self.average_surface_distance(y_true, y_pred)
            except Exception as e:
                print(f"Warning: Could not calculate distance metrics: {e}")
                metrics['hausdorff_distance'] = float('inf')
                metrics['average_surface_distance'] = float('inf')
        
        # Store computation time
        self.computation_times['all_metrics'] = time.time() - start_time
        
        return metrics


class VolumeCalculator:
    """
    Tumor volume calculation and analysis.
    """
    
    def __init__(self):
        """Initialize volume calculator."""
        self.volume_cache = {}
        
    def calculate_volume(self, mask: np.ndarray, 
                        voxel_spacing: Tuple[float, float, float] = (1.0, 1.0, 1.0),
                        units: str = 'mm') -> Dict[str, float]:
        """
        Calculate tumor volume from binary mask.
        
        Args:
            mask: Binary mask of tumor
            voxel_spacing: Spacing between voxels (z, y, x)
            units: Units for volume calculation
            
        Returns:
            Dictionary with volume measurements
        """
        # Count non-zero voxels
        voxel_count = np.sum(mask > 0)
        
        # Calculate voxel volume
        voxel_volume = np.prod(voxel_spacing)
        
        # Calculate total volume
        total_volume = voxel_count * voxel_volume
        
        # Convert to different units
        if units == 'mm':
            volume_mm3 = total_volume
            volume_cm3 = total_volume / 1000.0
            volume_ml = volume_cm3  # 1 ml = 1 cm³
        elif units == 'cm':
            volume_cm3 = total_volume
            volume_mm3 = total_volume * 1000.0
            volume_ml = volume_cm3
        else:
            volume_mm3 = total_volume
            volume_cm3 = total_volume / 1000.0
            volume_ml = volume_cm3
        
        # Calculate bounding box volume
        coords = np.where(mask > 0)
        if len(coords[0]) > 0:
            bbox_min = [np.min(coords[i]) * voxel_spacing[i] for i in range(3)]
            bbox_max = [np.max(coords[i]) * voxel_spacing[i] for i in range(3)]
            bbox_volume = np.prod([bbox_max[i] - bbox_min[i] for i in range(3)])
            
            # Calculate fill ratio
            fill_ratio = total_volume / bbox_volume if bbox_volume > 0 else 0
        else:
            bbox_volume = 0
            fill_ratio = 0
        
        return {
            'voxel_count': int(voxel_count),
            'voxel_volume_mm3': voxel_volume,
            'total_volume_mm3': volume_mm3,
            'total_volume_cm3': volume_cm3,
            'total_volume_ml': volume_ml,
            'bounding_box_volume_mm3': bbox_volume,
            'fill_ratio': fill_ratio
        }
    
    def compare_volumes(self, mask_true: np.ndarray, mask_pred: np.ndarray,
                       voxel_spacing: Tuple[float, float, float] = (1.0, 1.0, 1.0)) -> Dict[str, float]:
        """
        Compare volumes between ground truth and prediction.
        
        Args:
            mask_true: Ground truth binary mask
            mask_pred: Predicted binary mask
            voxel_spacing: Spacing between voxels
            
        Returns:
            Dictionary with volume comparison metrics
        """
        vol_true = self.calculate_volume(mask_true, voxel_spacing)
        vol_pred = self.calculate_volume(mask_pred, voxel_spacing)
        
        # Calculate differences
        volume_diff_mm3 = vol_pred['total_volume_mm3'] - vol_true['total_volume_mm3']
        volume_diff_ml = vol_pred['total_volume_ml'] - vol_true['total_volume_ml']
        
        # Calculate relative differences
        if vol_true['total_volume_mm3'] > 0:
            relative_diff = volume_diff_mm3 / vol_true['total_volume_mm3']
            relative_error = abs(relative_diff)
        else:
            relative_diff = 0
            relative_error = 0
        
        return {
            'true_volume_mm3': vol_true['total_volume_mm3'],
            'pred_volume_mm3': vol_pred['total_volume_mm3'],
            'true_volume_ml': vol_true['total_volume_ml'],
            'pred_volume_ml': vol_pred['total_volume_ml'],
            'volume_difference_mm3': volume_diff_mm3,
            'volume_difference_ml': volume_diff_ml,
            'relative_difference': relative_diff,
            'relative_error': relative_error,
            'absolute_error_mm3': abs(volume_diff_mm3),
            'absolute_error_ml': abs(volume_diff_ml)
        }
    
    def calculate_volume_overlap(self, mask_true: np.ndarray, mask_pred: np.ndarray,
                               voxel_spacing: Tuple[float, float, float] = (1.0, 1.0, 1.0)) -> Dict[str, float]:
        """
        Calculate volume overlap metrics.
        
        Args:
            mask_true: Ground truth binary mask
            mask_pred: Predicted binary mask
            voxel_spacing: Spacing between voxels
            
        Returns:
            Dictionary with overlap metrics
        """
        # Calculate intersection and union
        intersection = np.logical_and(mask_true, mask_pred)
        union = np.logical_or(mask_true, mask_pred)
        
        # Calculate volumes
        voxel_volume = np.prod(voxel_spacing)
        
        intersection_volume = np.sum(intersection) * voxel_volume
        union_volume = np.sum(union) * voxel_volume
        true_volume = np.sum(mask_true) * voxel_volume
        pred_volume = np.sum(mask_pred) * voxel_volume
        
        # Calculate overlap metrics
        dice_volume = (2 * intersection_volume) / (true_volume + pred_volume) if (true_volume + pred_volume) > 0 else 0
        jaccard_volume = intersection_volume / union_volume if union_volume > 0 else 0
        
        return {
            'intersection_volume_mm3': intersection_volume,
            'union_volume_mm3': union_volume,
            'dice_volume': dice_volume,
            'jaccard_volume': jaccard_volume,
            'overlap_fraction': intersection_volume / true_volume if true_volume > 0 else 0
        }


class MethodComparator:
    """
    Compare multiple segmentation methods.
    """
    
    def __init__(self):
        """Initialize method comparator."""
        self.results = {}
        self.statistical_tests = {}
        
    def add_method_results(self, method_name: str, 
                          ground_truth: List[np.ndarray], 
                          predictions: List[np.ndarray],
                          voxel_spacing: Tuple[float, float, float] = (1.0, 1.0, 1.0)):
        """
        Add results for a segmentation method.
        
        Args:
            method_name: Name of the method
            ground_truth: List of ground truth masks
            predictions: List of predicted masks
            voxel_spacing: Voxel spacing
        """
        metrics_calculator = SegmentationMetrics()
        volume_calculator = VolumeCalculator()
        
        results = {
            'method_name': method_name,
            'metrics': [],
            'volume_comparisons': [],
            'processing_times': []
        }
        
        for i, (gt, pred) in enumerate(zip(ground_truth, predictions)):
            start_time = time.time()
            
            # Calculate metrics
            metrics = metrics_calculator.calculate_all_metrics(gt, pred)
            
            # Calculate volume comparison
            volume_comp = volume_calculator.compare_volumes(gt, pred, voxel_spacing)
            
            processing_time = time.time() - start_time
            
            results['metrics'].append(metrics)
            results['volume_comparisons'].append(volume_comp)
            results['processing_times'].append(processing_time)
        
        self.results[method_name] = results
    
    def calculate_summary_statistics(self, method_name: str) -> Dict[str, Dict[str, float]]:
        """
        Calculate summary statistics for a method.
        
        Args:
            method_name: Name of the method
            
        Returns:
            Dictionary with summary statistics
        """
        if method_name not in self.results:
            raise ValueError(f"Method {method_name} not found")
        
        results = self.results[method_name]
        
        # Extract all metrics
        all_metrics = {}
        for metric_name in results['metrics'][0].keys():
            values = [m[metric_name] for m in results['metrics']]
            all_metrics[metric_name] = values
        
        # Calculate statistics
        summary = {}
        for metric_name, values in all_metrics.items():
            summary[metric_name] = {
                'mean': np.mean(values),
                'std': np.std(values),
                'min': np.min(values),
                'max': np.max(values),
                'median': np.median(values),
                'q25': np.percentile(values, 25),
                'q75': np.percentile(values, 75),
                'count': len(values)
            }
        
        return summary
    
    def compare_methods(self, method1: str, method2: str, 
                       metric: str = 'dice_coefficient') -> Dict[str, Any]:
        """
        Compare two methods statistically.
        
        Args:
            method1: First method name
            method2: Second method name
            metric: Metric to compare
            
        Returns:
            Dictionary with comparison results
        """
        if method1 not in self.results or method2 not in self.results:
            raise ValueError("One or both methods not found")
        
        # Extract values
        values1 = [m[metric] for m in self.results[method1]['metrics']]
        values2 = [m[metric] for m in self.results[method2]['metrics']]
        
        # Perform statistical tests
        
        # Paired t-test
        t_stat, t_pvalue = stats.ttest_rel(values1, values2)
        
        # Wilcoxon signed-rank test
        w_stat, w_pvalue = stats.wilcoxon(values1, values2)
        
        # Mann-Whitney U test
        u_stat, u_pvalue = stats.mannwhitneyu(values1, values2)
        
        # Effect size (Cohen's d)
        pooled_std = np.sqrt(((np.std(values1) ** 2) + (np.std(values2) ** 2)) / 2)
        cohens_d = (np.mean(values1) - np.mean(values2)) / pooled_std if pooled_std > 0 else 0
        
        return {
            'method1': method1,
            'method2': method2,
            'metric': metric,
            'method1_mean': np.mean(values1),
            'method2_mean': np.mean(values2),
            'method1_std': np.std(values1),
            'method2_std': np.std(values2),
            'difference': np.mean(values1) - np.mean(values2),
            'paired_t_test': {
                'statistic': t_stat,
                'p_value': t_pvalue,
                'significant': t_pvalue < 0.05
            },
            'wilcoxon_test': {
                'statistic': w_stat,
                'p_value': w_pvalue,
                'significant': w_pvalue < 0.05
            },
            'mann_whitney_test': {
                'statistic': u_stat,
                'p_value': u_pvalue,
                'significant': u_pvalue < 0.05
            },
            'cohens_d': cohens_d,
            'effect_size': self._interpret_effect_size(cohens_d)
        }
    
    def _interpret_effect_size(self, cohens_d: float) -> str:
        """Interpret Cohen's d effect size."""
        abs_d = abs(cohens_d)
        if abs_d < 0.2:
            return 'negligible'
        elif abs_d < 0.5:
            return 'small'
        elif abs_d < 0.8:
            return 'medium'
        else:
            return 'large'
    
    def generate_comparison_report(self) -> Dict[str, Any]:
        """
        Generate comprehensive comparison report.
        
        Returns:
            Dictionary with complete comparison report
        """
        report = {
            'methods': list(self.results.keys()),
            'summary_statistics': {},
            'pairwise_comparisons': {},
            'rankings': {},
            'best_method': {}
        }
        
        # Calculate summary statistics for each method
        for method_name in self.results.keys():
            report['summary_statistics'][method_name] = self.calculate_summary_statistics(method_name)
        
        # Pairwise comparisons
        methods = list(self.results.keys())
        for i in range(len(methods)):
            for j in range(i + 1, len(methods)):
                method1, method2 = methods[i], methods[j]
                comparison_key = f"{method1}_vs_{method2}"
                
                # Compare on different metrics
                comparison_results = {}
                for metric in ['dice_coefficient', 'jaccard_index', 'accuracy', 'precision', 'recall']:
                    comparison_results[metric] = self.compare_methods(method1, method2, metric)
                
                report['pairwise_comparisons'][comparison_key] = comparison_results
        
        # Rankings
        metrics_to_rank = ['dice_coefficient', 'jaccard_index', 'accuracy', 'precision', 'recall']
        for metric in metrics_to_rank:
            rankings = []
            for method_name in methods:
                mean_value = report['summary_statistics'][method_name][metric]['mean']
                rankings.append((method_name, mean_value))
            
            # Sort by metric value (descending)
            rankings.sort(key=lambda x: x[1], reverse=True)
            report['rankings'][metric] = rankings
        
        # Best method overall
        overall_scores = {}
        for method_name in methods:
            score = 0
            for metric in metrics_to_rank:
                # Get rank (1-based)
                rank = next(i for i, (name, _) in enumerate(report['rankings'][metric]) if name == method_name) + 1
                score += rank
            overall_scores[method_name] = score / len(metrics_to_rank)
        
        best_method = min(overall_scores.items(), key=lambda x: x[1])
        report['best_method'] = {
            'method': best_method[0],
            'average_rank': best_method[1]
        }
        
        return report


class ResultsVisualizer:
    """
    Visualize segmentation results and comparisons.
    """
    
    def __init__(self):
        """Initialize results visualizer."""
        self.figure_counter = 0
        
    def plot_metrics_comparison(self, comparator: MethodComparator, 
                              metrics: List[str] = None,
                              save_path: Optional[str] = None) -> None:
        """
        Plot metrics comparison between methods.
        
        Args:
            comparator: Method comparator instance
            metrics: List of metrics to plot
            save_path: Path to save the plot
        """
        if metrics is None:
            metrics = ['dice_coefficient', 'jaccard_index', 'accuracy', 'precision', 'recall']
        
        methods = list(comparator.results.keys())
        n_metrics = len(metrics)
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()
        
        for i, metric in enumerate(metrics):
            ax = axes[i]
            
            # Collect data for box plot
            data = []
            labels = []
            
            for method in methods:
                values = [m[metric] for m in comparator.results[method]['metrics']]
                data.append(values)
                labels.append(method)
            
            # Create box plot
            bp = ax.boxplot(data, labels=labels, patch_artist=True)
            
            # Color boxes
            colors = plt.cm.Set3(np.linspace(0, 1, len(methods)))
            for patch, color in zip(bp['boxes'], colors):
                patch.set_facecolor(color)
                patch.set_alpha(0.7)
            
            ax.set_title(f'{metric.replace("_", " ").title()}')
            ax.set_ylabel('Value')
            ax.tick_params(axis='x', rotation=45)
            ax.grid(True, alpha=0.3)
        
        # Remove empty subplot
        if len(metrics) < len(axes):
            axes[-1].remove()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def plot_method_ranking(self, comparator: MethodComparator,
                           save_path: Optional[str] = None) -> None:
        """
        Plot method rankings.
        
        Args:
            comparator: Method comparator instance
            save_path: Path to save the plot
        """
        report = comparator.generate_comparison_report()
        
        metrics = ['dice_coefficient', 'jaccard_index', 'accuracy', 'precision', 'recall']
        methods = list(comparator.results.keys())
        
        # Create ranking matrix
        ranking_matrix = np.zeros((len(methods), len(metrics)))
        
        for j, metric in enumerate(metrics):
            for i, method in enumerate(methods):
                rank = next(k for k, (name, _) in enumerate(report['rankings'][metric]) if name == method)
                ranking_matrix[i, j] = rank + 1  # 1-based ranking
        
        # Create heatmap
        plt.figure(figsize=(10, 8))
        
        sns.heatmap(ranking_matrix, 
                   annot=True, 
                   fmt='d',
                   cmap='RdYlBu_r',
                   xticklabels=[m.replace('_', ' ').title() for m in metrics],
                   yticklabels=methods,
                   cbar_kws={'label': 'Rank (1=best)'})
        
        plt.title('Method Rankings by Metric')
        plt.xlabel('Metrics')
        plt.ylabel('Methods')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def plot_volume_comparison(self, comparator: MethodComparator,
                             save_path: Optional[str] = None) -> None:
        """
        Plot volume comparison.
        
        Args:
            comparator: Method comparator instance
            save_path: Path to save the plot
        """
        methods = list(comparator.results.keys())
        
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # Volume accuracy
        ax1 = axes[0]
        volume_errors = []
        labels = []
        
        for method in methods:
            errors = [vc['relative_error'] for vc in comparator.results[method]['volume_comparisons']]
            volume_errors.append(errors)
            labels.append(method)
        
        bp1 = ax1.boxplot(volume_errors, labels=labels, patch_artist=True)
        
        # Color boxes
        colors = plt.cm.Set3(np.linspace(0, 1, len(methods)))
        for patch, color in zip(bp1['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)
        
        ax1.set_title('Volume Estimation Error')
        ax1.set_ylabel('Relative Error')
        ax1.tick_params(axis='x', rotation=45)
        ax1.grid(True, alpha=0.3)
        
        # Volume correlation
        ax2 = axes[1]
        
        for i, method in enumerate(methods):
            true_volumes = [vc['true_volume_ml'] for vc in comparator.results[method]['volume_comparisons']]
            pred_volumes = [vc['pred_volume_ml'] for vc in comparator.results[method]['volume_comparisons']]
            
            ax2.scatter(true_volumes, pred_volumes, 
                       alpha=0.7, label=method, color=colors[i])
        
        # Add diagonal line
        max_vol = max([max([vc['true_volume_ml'] for vc in comparator.results[method]['volume_comparisons']]) 
                      for method in methods])
        ax2.plot([0, max_vol], [0, max_vol], 'k--', alpha=0.5, label='Perfect Correlation')
        
        ax2.set_xlabel('True Volume (ml)')
        ax2.set_ylabel('Predicted Volume (ml)')
        ax2.set_title('Volume Correlation')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def plot_performance_summary(self, comparator: MethodComparator,
                               save_path: Optional[str] = None) -> None:
        """
        Plot comprehensive performance summary.
        
        Args:
            comparator: Method comparator instance
            save_path: Path to save the plot
        """
        methods = list(comparator.results.keys())
        metrics = ['dice_coefficient', 'jaccard_index', 'precision', 'recall']
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        axes = axes.flatten()
        
        # Radar chart for each method
        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
        angles += angles[:1]  # Complete the circle
        
        colors = plt.cm.Set3(np.linspace(0, 1, len(methods)))
        
        for i, ax in enumerate(axes):
            if i < len(methods):
                method = methods[i]
                
                # Get average values
                values = []
                for metric in metrics:
                    avg_value = np.mean([m[metric] for m in comparator.results[method]['metrics']])
                    values.append(avg_value)
                
                values += values[:1]  # Complete the circle
                
                ax = plt.subplot(2, 2, i + 1, projection='polar')
                ax.plot(angles, values, 'o-', linewidth=2, color=colors[i])
                ax.fill(angles, values, alpha=0.25, color=colors[i])
                ax.set_xticks(angles[:-1])
                ax.set_xticklabels([m.replace('_', ' ').title() for m in metrics])
                ax.set_ylim(0, 1)
                ax.set_title(f'{method}', pad=20)
                ax.grid(True)
            else:
                axes[i].remove()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def create_results_dashboard(self, comparator: MethodComparator,
                               save_path: Optional[str] = None) -> None:
        """
        Create comprehensive results dashboard.
        
        Args:
            comparator: Method comparator instance
            save_path: Path to save the dashboard
        """
        # Create subplots
        fig = plt.figure(figsize=(20, 16))
        
        # 1. Metrics comparison
        ax1 = plt.subplot(3, 3, (1, 3))
        methods = list(comparator.results.keys())
        metrics = ['dice_coefficient', 'jaccard_index', 'accuracy']
        
        x = np.arange(len(methods))
        width = 0.25
        
        for i, metric in enumerate(metrics):
            values = []
            for method in methods:
                avg_value = np.mean([m[metric] for m in comparator.results[method]['metrics']])
                values.append(avg_value)
            
            ax1.bar(x + i * width, values, width, label=metric.replace('_', ' ').title())
        
        ax1.set_xlabel('Methods')
        ax1.set_ylabel('Value')
        ax1.set_title('Average Performance Metrics')
        ax1.set_xticks(x + width)
        ax1.set_xticklabels(methods)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. Volume accuracy
        ax2 = plt.subplot(3, 3, 4)
        volume_errors = []
        for method in methods:
            errors = [vc['relative_error'] for vc in comparator.results[method]['volume_comparisons']]
            volume_errors.append(np.mean(errors))
        
        bars = ax2.bar(methods, volume_errors, color=plt.cm.Set3(np.linspace(0, 1, len(methods))))
        ax2.set_ylabel('Average Relative Error')
        ax2.set_title('Volume Estimation Accuracy')
        ax2.tick_params(axis='x', rotation=45)
        ax2.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for bar, error in zip(bars, volume_errors):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'{error:.3f}', ha='center', va='bottom')
        
        # 3. Processing time
        ax3 = plt.subplot(3, 3, 5)
        processing_times = []
        for method in methods:
            avg_time = np.mean(comparator.results[method]['processing_times'])
            processing_times.append(avg_time)
        
        bars = ax3.bar(methods, processing_times, color=plt.cm.Set3(np.linspace(0, 1, len(methods))))
        ax3.set_ylabel('Average Time (seconds)')
        ax3.set_title('Processing Time')
        ax3.tick_params(axis='x', rotation=45)
        ax3.grid(True, alpha=0.3)
        
        # 4. Dice coefficient distribution
        ax4 = plt.subplot(3, 3, 6)
        dice_data = []
        for method in methods:
            dice_values = [m['dice_coefficient'] for m in comparator.results[method]['metrics']]
            dice_data.append(dice_values)
        
        bp = ax4.boxplot(dice_data, labels=methods, patch_artist=True)
        colors = plt.cm.Set3(np.linspace(0, 1, len(methods)))
        for patch, color in zip(bp['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)
        
        ax4.set_ylabel('Dice Coefficient')
        ax4.set_title('Dice Coefficient Distribution')
        ax4.tick_params(axis='x', rotation=45)
        ax4.grid(True, alpha=0.3)
        
        # 5. Precision vs Recall
        ax5 = plt.subplot(3, 3, 7)
        for i, method in enumerate(methods):
            precision_values = [m['precision'] for m in comparator.results[method]['metrics']]
            recall_values = [m['recall'] for m in comparator.results[method]['metrics']]
            
            ax5.scatter(recall_values, precision_values, 
                       alpha=0.7, label=method, color=colors[i])
        
        ax5.set_xlabel('Recall')
        ax5.set_ylabel('Precision')
        ax5.set_title('Precision vs Recall')
        ax5.legend()
        ax5.grid(True, alpha=0.3)
        
        # 6. Overall ranking
        ax6 = plt.subplot(3, 3, (8, 9))
        
        # Calculate overall score
        overall_scores = {}
        for method in methods:
            score = 0
            for metric in ['dice_coefficient', 'jaccard_index', 'accuracy', 'precision', 'recall']:
                avg_value = np.mean([m[metric] for m in comparator.results[method]['metrics']])
                score += avg_value
            overall_scores[method] = score / 5
        
        # Sort methods by overall score
        sorted_methods = sorted(overall_scores.items(), key=lambda x: x[1], reverse=True)
        
        methods_sorted = [item[0] for item in sorted_methods]
        scores_sorted = [item[1] for item in sorted_methods]
        
        bars = ax6.barh(methods_sorted, scores_sorted, color=plt.cm.Set3(np.linspace(0, 1, len(methods))))
        ax6.set_xlabel('Overall Score')
        ax6.set_title('Overall Method Ranking')
        ax6.grid(True, alpha=0.3)
        
        # Add score labels
        for bar, score in zip(bars, scores_sorted):
            width = bar.get_width()
            ax6.text(width, bar.get_y() + bar.get_height()/2.,
                    f'{score:.3f}', ha='left', va='center')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()


class ResultsReporter:
    """
    Generate comprehensive results reports.
    """
    
    def __init__(self):
        """Initialize results reporter."""
        self.report_data = {}
        
    def generate_text_report(self, comparator: MethodComparator,
                           save_path: Optional[str] = None) -> str:
        """
        Generate text-based results report.
        
        Args:
            comparator: Method comparator instance
            save_path: Path to save the report
            
        Returns:
            Report as string
        """
        report = comparator.generate_comparison_report()
        
        # Generate report text
        report_text = []
        report_text.append("SEGMENTATION RESULTS COMPARISON REPORT")
        report_text.append("=" * 50)
        report_text.append(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_text.append(f"Number of methods compared: {len(report['methods'])}")
        report_text.append(f"Methods: {', '.join(report['methods'])}")
        report_text.append("")
        
        # Summary statistics
        report_text.append("SUMMARY STATISTICS")
        report_text.append("-" * 30)
        
        for method in report['methods']:
            report_text.append(f"\n{method}:")
            stats = report['summary_statistics'][method]
            
            for metric, values in stats.items():
                report_text.append(f"  {metric}:")
                report_text.append(f"    Mean: {values['mean']:.4f} ± {values['std']:.4f}")
                report_text.append(f"    Range: [{values['min']:.4f}, {values['max']:.4f}]")
                report_text.append(f"    Median: {values['median']:.4f}")
        
        # Rankings
        report_text.append("\n\nMETHOD RANKINGS")
        report_text.append("-" * 30)
        
        for metric, rankings in report['rankings'].items():
            report_text.append(f"\n{metric}:")
            for i, (method, value) in enumerate(rankings):
                report_text.append(f"  {i+1}. {method}: {value:.4f}")
        
        # Best method
        report_text.append(f"\n\nBEST METHOD OVERALL")
        report_text.append("-" * 30)
        report_text.append(f"Method: {report['best_method']['method']}")
        report_text.append(f"Average rank: {report['best_method']['average_rank']:.2f}")
        
        # Pairwise comparisons
        report_text.append(f"\n\nPAIRWISE COMPARISONS")
        report_text.append("-" * 30)
        
        for comparison_key, comparison_data in report['pairwise_comparisons'].items():
            report_text.append(f"\n{comparison_key}:")
            
            for metric, comp_result in comparison_data.items():
                report_text.append(f"  {metric}:")
                report_text.append(f"    {comp_result['method1']}: {comp_result['method1_mean']:.4f}")
                report_text.append(f"    {comp_result['method2']}: {comp_result['method2_mean']:.4f}")
                report_text.append(f"    Difference: {comp_result['difference']:.4f}")
                report_text.append(f"    P-value (t-test): {comp_result['paired_t_test']['p_value']:.4f}")
                report_text.append(f"    Significant: {comp_result['paired_t_test']['significant']}")
                report_text.append(f"    Effect size: {comp_result['effect_size']}")
        
        report_str = "\n".join(report_text)
        
        if save_path:
            with open(save_path, 'w') as f:
                f.write(report_str)
        
        return report_str
    
    def generate_json_report(self, comparator: MethodComparator,
                           save_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate JSON-formatted results report.
        
        Args:
            comparator: Method comparator instance
            save_path: Path to save the report
            
        Returns:
            Report as dictionary
        """
        report = comparator.generate_comparison_report()
        
        # Add metadata
        report['metadata'] = {
            'generated_on': datetime.now().isoformat(),
            'version': '1.0',
            'author': 'Dr. Mohammed Yagoub Esmail'
        }
        
        if save_path:
            with open(save_path, 'w') as f:
                json.dump(report, f, indent=2)
        
        return report
    
    def generate_csv_summary(self, comparator: MethodComparator,
                           save_path: Optional[str] = None) -> pd.DataFrame:
        """
        Generate CSV summary of results.
        
        Args:
            comparator: Method comparator instance
            save_path: Path to save the CSV
            
        Returns:
            DataFrame with results summary
        """
        report = comparator.generate_comparison_report()
        
        # Create summary dataframe
        summary_data = []
        
        for method in report['methods']:
            stats = report['summary_statistics'][method]
            
            row = {'Method': method}
            
            # Add metrics
            for metric, values in stats.items():
                row[f'{metric}_mean'] = values['mean']
                row[f'{metric}_std'] = values['std']
                row[f'{metric}_min'] = values['min']
                row[f'{metric}_max'] = values['max']
            
            # Add rankings
            for metric, rankings in report['rankings'].items():
                rank = next(i for i, (name, _) in enumerate(rankings) if name == method) + 1
                row[f'{metric}_rank'] = rank
            
            summary_data.append(row)
        
        df = pd.DataFrame(summary_data)
        
        if save_path:
            df.to_csv(save_path, index=False)
        
        return df


def main():
    """
    Example usage of the results calculation module.
    """
    print("Results Calculation and Comparison Module Demo")
    print("=" * 60)
    
    # Create sample data
    np.random.seed(42)
    
    # Generate synthetic ground truth and predictions
    n_samples = 20
    image_size = (128, 128)
    
    ground_truth = []
    predictions_method1 = []
    predictions_method2 = []
    predictions_method3 = []
    
    print("Generating synthetic segmentation data...")
    
    for i in range(n_samples):
        # Create ground truth (circular tumor)
        gt = np.zeros(image_size)
        center = (64, 64)
        radius = 20 + np.random.normal(0, 5)
        
        y, x = np.ogrid[:image_size[0], :image_size[1]]
        mask = (x - center[0])**2 + (y - center[1])**2 <= radius**2
        gt[mask] = 1
        
        # Method 1: Good segmentation (high accuracy)
        pred1 = gt.copy()
        noise = np.random.normal(0, 0.1, image_size)
        pred1 = (pred1 + noise > 0.5).astype(np.uint8)
        
        # Method 2: Moderate segmentation (medium accuracy)
        pred2 = gt.copy()
        noise = np.random.normal(0, 0.3, image_size)
        pred2 = (pred2 + noise > 0.3).astype(np.uint8)
        
        # Method 3: Poor segmentation (low accuracy)
        pred3 = gt.copy()
        noise = np.random.normal(0, 0.5, image_size)
        pred3 = (pred3 + noise > 0.1).astype(np.uint8)
        
        ground_truth.append(gt)
        predictions_method1.append(pred1)
        predictions_method2.append(pred2)
        predictions_method3.append(pred3)
    
    # Initialize comparator
    comparator = MethodComparator()
    
    # Add methods
    print("Adding method results...")
    comparator.add_method_results("High Accuracy Method", ground_truth, predictions_method1)
    comparator.add_method_results("Medium Accuracy Method", ground_truth, predictions_method2)
    comparator.add_method_results("Low Accuracy Method", ground_truth, predictions_method3)
    
    # Generate comparison report
    print("Generating comparison report...")
    report = comparator.generate_comparison_report()
    
    # Print summary
    print("\nMethod Rankings (Dice Coefficient):")
    for i, (method, score) in enumerate(report['rankings']['dice_coefficient']):
        print(f"{i+1}. {method}: {score:.4f}")
    
    print(f"\nBest method overall: {report['best_method']['method']}")
    print(f"Average rank: {report['best_method']['average_rank']:.2f}")
    
    # Create visualizations
    print("\nCreating visualizations...")
    visualizer = ResultsVisualizer()
    
    # Metrics comparison
    visualizer.plot_metrics_comparison(comparator)
    
    # Method ranking
    visualizer.plot_method_ranking(comparator)
    
    # Volume comparison
    visualizer.plot_volume_comparison(comparator)
    
    # Performance summary
    visualizer.plot_performance_summary(comparator)
    
    # Results dashboard
    visualizer.create_results_dashboard(comparator)
    
    # Generate reports
    print("\nGenerating reports...")
    reporter = ResultsReporter()
    
    # Text report
    text_report = reporter.generate_text_report(comparator)
    print("\nText report generated (first 500 characters):")
    print(text_report[:500] + "...")
    
    # JSON report
    json_report = reporter.generate_json_report(comparator)
    print(f"\nJSON report generated with {len(json_report)} sections")
    
    # CSV summary
    csv_summary = reporter.generate_csv_summary(comparator)
    print(f"\nCSV summary generated with {len(csv_summary)} rows and {len(csv_summary.columns)} columns")
    
    # Statistical comparison example
    print("\nStatistical comparison example:")
    comparison = comparator.compare_methods("High Accuracy Method", "Medium Accuracy Method", "dice_coefficient")
    print(f"Comparison: {comparison['method1']} vs {comparison['method2']}")
    print(f"Mean difference: {comparison['difference']:.4f}")
    print(f"P-value (t-test): {comparison['paired_t_test']['p_value']:.4f}")
    print(f"Significant: {comparison['paired_t_test']['significant']}")
    print(f"Effect size: {comparison['effect_size']}")
    
    print("\nDemo completed successfully!")


if __name__ == "__main__":
    main()"""
Results Calculation and Comparison Module
========================================

This module provides comprehensive evaluation metrics for segmentation performance
and comparison between different methods. It includes statistical analysis,
visualization, and comprehensive reporting capabilities.

Author: Dr. Mohammed Yagoub Esmail
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional, Any, Union
from scipy import stats
from sklearn.metrics import (
    confusion_matrix, classification_report, roc_curve, auc,
    precision_recall_curve, average_precision_score
)
import warnings
import json
import time
from datetime import datetime
from pathlib import Path
import cv2
from scipy.spatial.distance import directed_hausdorff
from scipy.ndimage import binary_erosion, binary_dilation
warnings.filterwarnings('ignore')


class SegmentationMetrics:
    """
    Comprehensive segmentation metrics calculator.
    """
    
    def __init__(self):
        """Initialize metrics calculator."""
        self.metrics_cache = {}
        self.computation_times = {}
        
    def dice_coefficient(self, y_true: np.ndarray, y_pred: np.ndarray, 
                        smooth: float = 1e-6) -> float:
        """
        Calculate Dice Coefficient (F1 Score for segmentation).
        
        Args:
            y_true: Ground truth binary mask
            y_pred: Predicted binary mask
            smooth: Smoothing factor to avoid division by zero
            
        Returns:
            Dice coefficient value
        """
        y_true_f = y_true.flatten()
        y_pred_f = y_pred.flatten()
        
        intersection = np.sum(y_true_f * y_pred_f)
        union = np.sum(y_true_f) + np.sum(y_pred_f)
        
        dice = (2.0 * intersection + smooth) / (union + smooth)
        
        return float(dice)
    
    def jaccard_index(self, y_true: np.ndarray, y_pred: np.ndarray, 
                     smooth: float = 1e-6) -> float:
        """
        Calculate Jaccard Index (Intersection over Union).
        
        Args:
            y_true: Ground truth binary mask
            y_pred: Predicted binary mask
            smooth: Smoothing factor to avoid division by zero
            
        Returns:
            Jaccard index value
        """
        y_true_f = y_true.flatten()
        y_pred_f = y_pred.flatten()
        
        intersection = np.sum(y_true_f * y_pred_f)
        union = np.sum(y_true_f) + np.sum(y_pred_f) - intersection
        
        jaccard = (intersection + smooth) / (union + smooth)
        
        return float(jaccard)
    
    def accuracy(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """
        Calculate pixel-wise accuracy.
        
        Args:
            y_true: Ground truth binary mask
            y_pred: Predicted binary mask
            
        Returns:
            Accuracy value
        """
        y_true_f = y_true.flatten()
        y_pred_f = y_pred.flatten()
        
        correct = np.sum(y_true_f == y_pred_f)
        total = len(y_true_f)
        
        return float(correct / total)
    
    def precision(self, y_true: np.ndarray, y_pred: np.ndarray, 
                 smooth: float = 1e-6) -> float:
        """
        Calculate precision (positive predictive value).
        
        Args:
            y_true: Ground truth binary mask
            y_pred: Predicted binary mask
            smooth: Smoothing factor to avoid division by zero
            
        Returns:
            Precision value
        """
        y_true_f = y_true.flatten()
        y_pred_f = y_pred.flatten()
        
        true_positives = np.sum(y_true_f * y_pred_f)
        predicted_positives = np.sum(y_pred_f)
        
        precision = (true_positives + smooth) / (predicted_positives + smooth)
        
        return float(precision)
    
    def recall(self, y_true: np.ndarray, y_pred: np.ndarray, 
              smooth: float = 1e-6) -> float:
        """
        Calculate recall (sensitivity, true positive rate).
        
        Args:
            y_true: Ground truth binary mask
            y_pred: Predicted binary mask
            smooth: Smoothing factor to avoid division by zero
            
        Returns:
            Recall value
        """
        y_true_f = y_true.flatten()
        y_pred_f = y_pred.flatten()
        
        true_positives = np.sum(y_true_f * y_pred_f)
        actual_positives = np.sum(y_true_f)
        
        recall = (true_positives + smooth) / (actual_positives + smooth)
        
        return float(recall)
    
    def specificity(self, y_true: np.ndarray, y_pred: np.ndarray, 
                   smooth: float = 1e-6) -> float:
        """
        Calculate specificity (true negative rate).
        
        Args:
            y_true: Ground truth binary mask
            y_pred: Predicted binary mask
            smooth: Smoothing factor to avoid division by zero
            
        Returns:
            Specificity value
        """
        y_true_f = y_true.flatten()
        y_pred_f = y_pred.flatten()
        
        true_negatives = np.sum((1 - y_true_f) * (1 - y_pred_f))
        actual_negatives = np.sum(1 - y_true_f)
        
        specificity = (true_negatives + smooth) / (actual_negatives + smooth)
        
        return float(specificity)
    
    def sensitivity(self, y_true: np.ndarray, y_pred: np.ndarray, 
                   smooth: float = 1e-6) -> float:
        """
        Calculate sensitivity (same as recall).
        
        Args:
            y_true: Ground truth binary mask
            y_pred: Predicted binary mask
            smooth: Smoothing factor to avoid division by zero
            
        Returns:
            Sensitivity value
        """
        return self.recall(y_true, y_pred, smooth)
    
    def f1_score(self, y_true: np.ndarray, y_pred: np.ndarray, 
                smooth: float = 1e-6) -> float:
        """
        Calculate F1 Score.
        
        Args:
            y_true: Ground truth binary mask
            y_pred: Predicted binary mask
            smooth: Smoothing factor to avoid division by zero
            
        Returns:
            F1 score value
        """
        prec = self.precision(y_true, y_pred, smooth)
        rec = self.recall(y_true, y_pred, smooth)
        
        f1 = (2 * prec * rec + smooth) / (prec + rec + smooth)
        
        return float(f1)
    
    def hausdorff_distance(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """
        Calculate Hausdorff distance between contours.
        
        Args:
            y_true: Ground truth binary mask
            y_pred: Predicted binary mask
            
        Returns:
            Hausdorff distance
        """
        # Find contours
        contours_true, _ = cv2.findContours(
            y_true.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        contours_pred, _ = cv2.findContours(
            y_pred.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if len(contours_true) == 0 or len(contours_pred) == 0:
            return float('inf')
        
        # Get largest contours
        contour_true = max(contours_true, key=cv2.contourArea)
        contour_pred = max(contours_pred, key=cv2.contourArea)
        
        # Convert to point arrays
        points_true = contour_true.reshape(-1, 2)
        points_pred = contour_pred.reshape(-1, 2)
        
        # Calculate Hausdorff distance
        hd1 = directed_hausdorff(points_true, points_pred)[0]
        hd2 = directed_hausdorff(points_pred, points_true)[0]
        
        return float(max(hd1, hd2))
    
    def average_surface_distance(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """
        Calculate average surface distance.
        
        Args:
            y_true: Ground truth binary mask
            y_pred: Predicted binary mask
            
        Returns:
            Average surface distance
        """
        # Get surface points
        surface_true = self._get_surface_points(y_true)
        surface_pred = self._get_surface_points(y_pred)
        
        if len(surface_true) == 0 or len(surface_pred) == 0:
            return float('inf')
        
        # Calculate distances
        distances = []
        for point_true in surface_true:
            min_dist = np.min(np.linalg.norm(surface_pred - point_true, axis=1))
            distances.append(min_dist)
        
        return float(np.mean(distances))
    
    def _get_surface_points(self, mask: np.ndarray) -> np.ndarray:
        """Get surface points from binary mask."""
        # Erode mask to get inner boundary
        eroded = binary_erosion(mask)
        
        # Surface is difference between original and eroded
        surface = mask.astype(bool) & ~eroded
        
        # Get coordinates of surface points
        surface_points = np.column_stack(np.where(surface))
        
        return surface_points
    
    def volume_similarity(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """
        Calculate volume similarity coefficient.
        
        Args:
            y_true: Ground truth binary mask
            y_pred: Predicted binary mask
            
        Returns:
            Volume similarity coefficient
        """
        vol_true = np.sum(y_true)
        vol_pred = np.sum(y_pred)
        
        if vol_true == 0 and vol_pred == 0:
            return 1.0
        
        vol_sim = 1.0 - abs(vol_true - vol_pred) / (vol_true + vol_pred)
        
        return float(vol_sim)
    
    def calculate_all_metrics(self, y_true: np.ndarray, y_pred: np.ndarray,
                            include_distances: bool = True) -> Dict[str, float]:
        """
        Calculate all segmentation metrics.
        
        Args:
            y_true: Ground truth binary mask
            y_pred: Predicted binary mask
            include_distances: Whether to include distance-based metrics
            
        Returns:
            Dictionary with all metrics
        """
        start_time = time.time()
        
        # Ensure binary masks
        y_true = (y_true > 0).astype(np.uint8)
        y_pred = (y_pred > 0).astype(np.uint8)
        
        metrics = {}
        
        # Basic metrics
        metrics['dice_coefficient'] = self.dice_coefficient(y_true, y_pred)
        metrics['jaccard_index'] = self.jaccard_index(y_true, y_pred)
        metrics['accuracy'] = self.accuracy(y_true, y_pred)
        metrics['precision'] = self.precision(y_true, y_pred)
        metrics['recall'] = self.recall(y_true, y_pred)
        metrics['sensitivity'] = self.sensitivity(y_true, y_pred)
        metrics['specificity'] = self.specificity(y_true, y_pred)
        metrics['f1_score'] = self.f1_score(y_true, y_pred)
        metrics['volume_similarity'] = self.volume_similarity(y_true, y_pred)
        
        # Distance-based metrics (optional due to computational cost)
        if include_distances:
            try:
                metrics['hausdorff_distance'] = self.hausdorff_distance(y_true, y_pred)
                metrics['average_surface_distance'] = self.average_surface_distance(y_true, y_pred)
            except Exception as e:
                print(f"Warning: Could not calculate distance metrics: {e}")
                metrics['hausdorff_distance'] = float('inf')
                metrics['average_surface_distance'] = float('inf')
        
        # Store computation time
        self.computation_times['all_metrics'] = time.time() - start_time
        
        return metrics


class VolumeCalculator:
    """
    Tumor volume calculation and analysis.
    """
    
    def __init__(self):
        """Initialize volume calculator."""
        self.volume_cache = {}
        
    def calculate_volume(self, mask: np.ndarray, 
                        voxel_spacing: Tuple[float, float, float] = (1.0, 1.0, 1.0),
                        units: str = 'mm') -> Dict[str, float]:
        """
        Calculate tumor volume from binary mask.
        
        Args:
            mask: Binary mask of tumor
            voxel_spacing: Spacing between voxels (z, y, x)
            units: Units for volume calculation
            
        Returns:
            Dictionary with volume measurements
        """
        # Count non-zero voxels
        voxel_count = np.sum(mask > 0)
        
        # Calculate voxel volume
        voxel_volume = np.prod(voxel_spacing)
        
        # Calculate total volume
        total_volume = voxel_count * voxel_volume
        
        # Convert to different units
        if units == 'mm':
            volume_mm3 = total_volume
            volume_cm3 = total_volume / 1000.0
            volume_ml = volume_cm3  # 1 ml = 1 cm³
        elif units == 'cm':
            volume_cm3 = total_volume
            volume_mm3 = total_volume * 1000.0
            volume_ml = volume_cm3
        else:
            volume_mm3 = total_volume
            volume_cm3 = total_volume / 1000.0
            volume_ml = volume_cm3
        
        # Calculate bounding box volume
        coords = np.where(mask > 0)
        if len(coords[0]) > 0:
            bbox_min = [np.min(coords[i]) * voxel_spacing[i] for i in range(3)]
            bbox_max = [np.max(coords[i]) * voxel_spacing[i] for i in range(3)]
            bbox_volume = np.prod([bbox_max[i] - bbox_min[i] for i in range(3)])
            
            # Calculate fill ratio
            fill_ratio = total_volume / bbox_volume if bbox_volume > 0 else 0
        else:
            bbox_volume = 0
            fill_ratio = 0
        
        return {
            'voxel_count': int(voxel_count),
            'voxel_volume_mm3': voxel_volume,
            'total_volume_mm3': volume_mm3,
            'total_volume_cm3': volume_cm3,
            'total_volume_ml': volume_ml,
            'bounding_box_volume_mm3': bbox_volume,
            'fill_ratio': fill_ratio
        }
    
    def compare_volumes(self, mask_true: np.ndarray, mask_pred: np.ndarray,
                       voxel_spacing: Tuple[float, float, float] = (1.0, 1.0, 1.0)) -> Dict[str, float]:
        """
        Compare volumes between ground truth and prediction.
        
        Args:
            mask_true: Ground truth binary mask
            mask_pred: Predicted binary mask
            voxel_spacing: Spacing between voxels
            
        Returns:
            Dictionary with volume comparison metrics
        """
        vol_true = self.calculate_volume(mask_true, voxel_spacing)
        vol_pred = self.calculate_volume(mask_pred, voxel_spacing)
        
        # Calculate differences
        volume_diff_mm3 = vol_pred['total_volume_mm3'] - vol_true['total_volume_mm3']
        volume_diff_ml = vol_pred['total_volume_ml'] - vol_true['total_volume_ml']
        
        # Calculate relative differences
        if vol_true['total_volume_mm3'] > 0:
            relative_diff = volume_diff_mm3 / vol_true['total_volume_mm3']
            relative_error = abs(relative_diff)
        else:
            relative_diff = 0
            relative_error = 0
        
        return {
            'true_volume_mm3': vol_true['total_volume_mm3'],
            'pred_volume_mm3': vol_pred['total_volume_mm3'],
            'true_volume_ml': vol_true['total_volume_ml'],
            'pred_volume_ml': vol_pred['total_volume_ml'],
            'volume_difference_mm3': volume_diff_mm3,
            'volume_difference_ml': volume_diff_ml,
            'relative_difference': relative_diff,
            'relative_error': relative_error,
            'absolute_error_mm3': abs(volume_diff_mm3),
            'absolute_error_ml': abs(volume_diff_ml)
        }
    
    def calculate_volume_overlap(self, mask_true: np.ndarray, mask_pred: np.ndarray,
                               voxel_spacing: Tuple[float, float, float] = (1.0, 1.0, 1.0)) -> Dict[str, float]:
        """
        Calculate volume overlap metrics.
        
        Args:
            mask_true: Ground truth binary mask
            mask_pred: Predicted binary mask
            voxel_spacing: Spacing between voxels
            
        Returns:
            Dictionary with overlap metrics
        """
        # Calculate intersection and union
        intersection = np.logical_and(mask_true, mask_pred)
        union = np.logical_or(mask_true, mask_pred)
        
        # Calculate volumes
        voxel_volume = np.prod(voxel_spacing)
        
        intersection_volume = np.sum(intersection) * voxel_volume
        union_volume = np.sum(union) * voxel_volume
        true_volume = np.sum(mask_true) * voxel_volume
        pred_volume = np.sum(mask_pred) * voxel_volume
        
        # Calculate overlap metrics
        dice_volume = (2 * intersection_volume) / (true_volume + pred_volume) if (true_volume + pred_volume) > 0 else 0
        jaccard_volume = intersection_volume / union_volume if union_volume > 0 else 0
        
        return {
            'intersection_volume_mm3': intersection_volume,
            'union_volume_mm3': union_volume,
            'dice_volume': dice_volume,
            'jaccard_volume': jaccard_volume,
            'overlap_fraction': intersection_volume / true_volume if true_volume > 0 else 0
        }


class MethodComparator:
    """
    Compare multiple segmentation methods.
    """
    
    def __init__(self):
        """Initialize method comparator."""
        self.results = {}
        self.statistical_tests = {}
        
    def add_method_results(self, method_name: str, 
                          ground_truth: List[np.ndarray], 
                          predictions: List[np.ndarray],
                          voxel_spacing: Tuple[float, float, float] = (1.0, 1.0, 1.0)):
        """
        Add results for a segmentation method.
        
        Args:
            method_name: Name of the method
            ground_truth: List of ground truth masks
            predictions: List of predicted masks
            voxel_spacing: Voxel spacing
        """
        metrics_calculator = SegmentationMetrics()
        volume_calculator = VolumeCalculator()
        
        results = {
            'method_name': method_name,
            'metrics': [],
            'volume_comparisons': [],
            'processing_times': []
        }
        
        for i, (gt, pred) in enumerate(zip(ground_truth, predictions)):
            start_time = time.time()
            
            # Calculate metrics
            metrics = metrics_calculator.calculate_all_metrics(gt, pred)
            
            # Calculate volume comparison
            volume_comp = volume_calculator.compare_volumes(gt, pred, voxel_spacing)
            
            processing_time = time.time() - start_time
            
            results['metrics'].append(metrics)
            results['volume_comparisons'].append(volume_comp)
            results['processing_times'].append(processing_time)
        
        self.results[method_name] = results
    
    def calculate_summary_statistics(self, method_name: str) -> Dict[str, Dict[str, float]]:
        """
        Calculate summary statistics for a method.
        
        Args:
            method_name: Name of the method
            
        Returns:
            Dictionary with summary statistics
        """
        if method_name not in self.results:
            raise ValueError(f"Method {method_name} not found")
        
        results = self.results[method_name]
        
        # Extract all metrics
        all_metrics = {}
        for metric_name in results['metrics'][0].keys():
            values = [m[metric_name] for m in results['metrics']]
            all_metrics[metric_name] = values
        
        # Calculate statistics
        summary = {}
        for metric_name, values in all_metrics.items():
            summary[metric_name] = {
                'mean': np.mean(values),
                'std': np.std(values),
                'min': np.min(values),
                'max': np.max(values),
                'median': np.median(values),
                'q25': np.percentile(values, 25),
                'q75': np.percentile(values, 75),
                'count': len(values)
            }
        
        return summary
    
    def compare_methods(self, method1: str, method2: str, 
                       metric: str = 'dice_coefficient') -> Dict[str, Any]:
        """
        Compare two methods statistically.
        
        Args:
            method1: First method name
            method2: Second method name
            metric: Metric to compare
            
        Returns:
            Dictionary with comparison results
        """
        if method1 not in self.results or method2 not in self.results:
            raise ValueError("One or both methods not found")
        
        # Extract values
        values1 = [m[metric] for m in self.results[method1]['metrics']]
        values2 = [m[metric] for m in self.results[method2]['metrics']]
        
        # Perform statistical tests
        
        # Paired t-test
        t_stat, t_pvalue = stats.ttest_rel(values1, values2)
        
        # Wilcoxon signed-rank test
        w_stat, w_pvalue = stats.wilcoxon(values1, values2)
        
        # Mann-Whitney U test
        u_stat, u_pvalue = stats.mannwhitneyu(values1, values2)
        
        # Effect size (Cohen's d)
        pooled_std = np.sqrt(((np.std(values1) ** 2) + (np.std(values2) ** 2)) / 2)
        cohens_d = (np.mean(values1) - np.mean(values2)) / pooled_std if pooled_std > 0 else 0
        
        return {
            'method1': method1,
            'method2': method2,
            'metric': metric,
            'method1_mean': np.mean(values1),
            'method2_mean': np.mean(values2),
            'method1_std': np.std(values1),
            'method2_std': np.std(values2),
            'difference': np.mean(values1) - np.mean(values2),
            'paired_t_test': {
                'statistic': t_stat,
                'p_value': t_pvalue,
                'significant': t_pvalue < 0.05
            },
            'wilcoxon_test': {
                'statistic': w_stat,
                'p_value': w_pvalue,
                'significant': w_pvalue < 0.05
            },
            'mann_whitney_test': {
                'statistic': u_stat,
                'p_value': u_pvalue,
                'significant': u_pvalue < 0.05
            },
            'cohens_d': cohens_d,
            'effect_size': self._interpret_effect_size(cohens_d)
        }
    
    def _interpret_effect_size(self, cohens_d: float) -> str:
        """Interpret Cohen's d effect size."""
        abs_d = abs(cohens_d)
        if abs_d < 0.2:
            return 'negligible'
        elif abs_d < 0.5:
            return 'small'
        elif abs_d < 0.8:
            return 'medium'
        else:
            return 'large'
    
    def generate_comparison_report(self) -> Dict[str, Any]:
        """
        Generate comprehensive comparison report.
        
        Returns:
            Dictionary with complete comparison report
        """
        report = {
            'methods': list(self.results.keys()),
            'summary_statistics': {},
            'pairwise_comparisons': {},
            'rankings': {},
            'best_method': {}
        }
        
        # Calculate summary statistics for each method
        for method_name in self.results.keys():
            report['summary_statistics'][method_name] = self.calculate_summary_statistics(method_name)
        
        # Pairwise comparisons
        methods = list(self.results.keys())
        for i in range(len(methods)):
            for j in range(i + 1, len(methods)):
                method1, method2 = methods[i], methods[j]
                comparison_key = f"{method1}_vs_{method2}"
                
                # Compare on different metrics
                comparison_results = {}
                for metric in ['dice_coefficient', 'jaccard_index', 'accuracy', 'precision', 'recall']:
                    comparison_results[metric] = self.compare_methods(method1, method2, metric)
                
                report['pairwise_comparisons'][comparison_key] = comparison_results
        
        # Rankings
        metrics_to_rank = ['dice_coefficient', 'jaccard_index', 'accuracy', 'precision', 'recall']
        for metric in metrics_to_rank:
            rankings = []
            for method_name in methods:
                mean_value = report['summary_statistics'][method_name][metric]['mean']
                rankings.append((method_name, mean_value))
            
            # Sort by metric value (descending)
            rankings.sort(key=lambda x: x[1], reverse=True)
            report['rankings'][metric] = rankings
        
        # Best method overall
        overall_scores = {}
        for method_name in methods:
            score = 0
            for metric in metrics_to_rank:
                # Get rank (1-based)
                rank = next(i for i, (name, _) in enumerate(report['rankings'][metric]) if name == method_name) + 1
                score += rank
            overall_scores[method_name] = score / len(metrics_to_rank)
        
        best_method = min(overall_scores.items(), key=lambda x: x[1])
        report['best_method'] = {
            'method': best_method[0],
            'average_rank': best_method[1]
        }
        
        return report


class ResultsVisualizer:
    """
    Visualize segmentation results and comparisons.
    """
    
    def __init__(self):
        """Initialize results visualizer."""
        self.figure_counter = 0
        
    def plot_metrics_comparison(self, comparator: MethodComparator, 
                              metrics: List[str] = None,
                              save_path: Optional[str] = None) -> None:
        """
        Plot metrics comparison between methods.
        
        Args:
            comparator: Method comparator instance
            metrics: List of metrics to plot
            save_path: Path to save the plot
        """
        if metrics is None:
            metrics = ['dice_coefficient', 'jaccard_index', 'accuracy', 'precision', 'recall']
        
        methods = list(comparator.results.keys())
        n_metrics = len(metrics)
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()
        
        for i, metric in enumerate(metrics):
            ax = axes[i]
            
            # Collect data for box plot
            data = []
            labels = []
            
            for method in methods:
                values = [m[metric] for m in comparator.results[method]['metrics']]
                data.append(values)
                labels.append(method)
            
            # Create box plot
            bp = ax.boxplot(data, labels=labels, patch_artist=True)
            
            # Color boxes
            colors = plt.cm.Set3(np.linspace(0, 1, len(methods)))
            for patch, color in zip(bp['boxes'], colors):
                patch.set_facecolor(color)
                patch.set_alpha(0.7)
            
            ax.set_title(f'{metric.replace("_", " ").title()}')
            ax.set_ylabel('Value')
            ax.tick_params(axis='x', rotation=45)
            ax.grid(True, alpha=0.3)
        
        # Remove empty subplot
        if len(metrics) < len(axes):
            axes[-1].remove()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def plot_method_ranking(self, comparator: MethodComparator,
                           save_path: Optional[str] = None) -> None:
        """
        Plot method rankings.
        
        Args:
            comparator: Method comparator instance
            save_path: Path to save the plot
        """
        report = comparator.generate_comparison_report()
        
        metrics = ['dice_coefficient', 'jaccard_index', 'accuracy', 'precision', 'recall']
        methods = list(comparator.results.keys())
        
        # Create ranking matrix
        ranking_matrix = np.zeros((len(methods), len(metrics)))
        
        for j, metric in enumerate(metrics):
            for i, method in enumerate(methods):
                rank = next(k for k, (name, _) in enumerate(report['rankings'][metric]) if name == method)
                ranking_matrix[i, j] = rank + 1  # 1-based ranking
        
        # Create heatmap
        plt.figure(figsize=(10, 8))
        
        sns.heatmap(ranking_matrix, 
                   annot=True, 
                   fmt='d',
                   cmap='RdYlBu_r',
                   xticklabels=[m.replace('_', ' ').title() for m in metrics],
                   yticklabels=methods,
                   cbar_kws={'label': 'Rank (1=best)'})
        
        plt.title('Method Rankings by Metric')
        plt.xlabel('Metrics')
        plt.ylabel('Methods')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def plot_volume_comparison(self, comparator: MethodComparator,
                             save_path: Optional[str] = None) -> None:
        """
        Plot volume comparison.
        
        Args:
            comparator: Method comparator instance
            save_path: Path to save the plot
        """
        methods = list(comparator.results.keys())
        
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # Volume accuracy
        ax1 = axes[0]
        volume_errors = []
        labels = []
        
        for method in methods:
            errors = [vc['relative_error'] for vc in comparator.results[method]['volume_comparisons']]
            volume_errors.append(errors)
            labels.append(method)
        
        bp1 = ax1.boxplot(volume_errors, labels=labels, patch_artist=True)
        
        # Color boxes
        colors = plt.cm.Set3(np.linspace(0, 1, len(methods)))
        for patch, color in zip(bp1['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)
        
        ax1.set_title('Volume Estimation Error')
        ax1.set_ylabel('Relative Error')
        ax1.tick_params(axis='x', rotation=45)
        ax1.grid(True, alpha=0.3)
        
        # Volume correlation
        ax2 = axes[1]
        
        for i, method in enumerate(methods):
            true_volumes = [vc['true_volume_ml'] for vc in comparator.results[method]['volume_comparisons']]
            pred_volumes = [vc['pred_volume_ml'] for vc in comparator.results[method]['volume_comparisons']]
            
            ax2.scatter(true_volumes, pred_volumes, 
                       alpha=0.7, label=method, color=colors[i])
        
        # Add diagonal line
        max_vol = max([max([vc['true_volume_ml'] for vc in comparator.results[method]['volume_comparisons']]) 
                      for method in methods])
        ax2.plot([0, max_vol], [0, max_vol], 'k--', alpha=0.5, label='Perfect Correlation')
        
        ax2.set_xlabel('True Volume (ml)')
        ax2.set_ylabel('Predicted Volume (ml)')
        ax2.set_title('Volume Correlation')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def plot_performance_summary(self, comparator: MethodComparator,
                               save_path: Optional[str] = None) -> None:
        """
        Plot comprehensive performance summary.
        
        Args:
            comparator: Method comparator instance
            save_path: Path to save the plot
        """
        methods = list(comparator.results.keys())
        metrics = ['dice_coefficient', 'jaccard_index', 'precision', 'recall']
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        axes = axes.flatten()
        
        # Radar chart for each method
        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
        angles += angles[:1]  # Complete the circle
        
        colors = plt.cm.Set3(np.linspace(0, 1, len(methods)))
        
        for i, ax in enumerate(axes):
            if i < len(methods):
                method = methods[i]
                
                # Get average values
                values = []
                for metric in metrics:
                    avg_value = np.mean([m[metric] for m in comparator.results[method]['metrics']])
                    values.append(avg_value)
                
                values += values[:1]  # Complete the circle
                
                ax = plt.subplot(2, 2, i + 1, projection='polar')
                ax.plot(angles, values, 'o-', linewidth=2, color=colors[i])
                ax.fill(angles, values, alpha=0.25, color=colors[i])
                ax.set_xticks(angles[:-1])
                ax.set_xticklabels([m.replace('_', ' ').title() for m in metrics])
                ax.set_ylim(0, 1)
                ax.set_title(f'{method}', pad=20)
                ax.grid(True)
            else:
                axes[i].remove()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def create_results_dashboard(self, comparator: MethodComparator,
                               save_path: Optional[str] = None) -> None:
        """
        Create comprehensive results dashboard.
        
        Args:
            comparator: Method comparator instance
            save_path: Path to save the dashboard
        """
        # Create subplots
        fig = plt.figure(figsize=(20, 16))
        
        # 1. Metrics comparison
        ax1 = plt.subplot(3, 3, (1, 3))
        methods = list(comparator.results.keys())
        metrics = ['dice_coefficient', 'jaccard_index', 'accuracy']
        
        x = np.arange(len(methods))
        width = 0.25
        
        for i, metric in enumerate(metrics):
            values = []
            for method in methods:
                avg_value = np.mean([m[metric] for m in comparator.results[method]['metrics']])
                values.append(avg_value)
            
            ax1.bar(x + i * width, values, width, label=metric.replace('_', ' ').title())
        
        ax1.set_xlabel('Methods')
        ax1.set_ylabel('Value')
        ax1.set_title('Average Performance Metrics')
        ax1.set_xticks(x + width)
        ax1.set_xticklabels(methods)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. Volume accuracy
        ax2 = plt.subplot(3, 3, 4)
        volume_errors = []
        for method in methods:
            errors = [vc['relative_error'] for vc in comparator.results[method]['volume_comparisons']]
            volume_errors.append(np.mean(errors))
        
        bars = ax2.bar(methods, volume_errors, color=plt.cm.Set3(np.linspace(0, 1, len(methods))))
        ax2.set_ylabel('Average Relative Error')
        ax2.set_title('Volume Estimation Accuracy')
        ax2.tick_params(axis='x', rotation=45)
        ax2.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for bar, error in zip(bars, volume_errors):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'{error:.3f}', ha='center', va='bottom')
        
        # 3. Processing time
        ax3 = plt.subplot(3, 3, 5)
        processing_times = []
        for method in methods:
            avg_time = np.mean(comparator.results[method]['processing_times'])
            processing_times.append(avg_time)
        
        bars = ax3.bar(methods, processing_times, color=plt.cm.Set3(np.linspace(0, 1, len(methods))))
        ax3.set_ylabel('Average Time (seconds)')
        ax3.set_title('Processing Time')
        ax3.tick_params(axis='x', rotation=45)
        ax3.grid(True, alpha=0.3)
        
        # 4. Dice coefficient distribution
        ax4 = plt.subplot(3, 3, 6)
        dice_data = []
        for method in methods:
            dice_values = [m['dice_coefficient'] for m in comparator.results[method]['metrics']]
            dice_data.append(dice_values)
        
        bp = ax4.boxplot(dice_data, labels=methods, patch_artist=True)
        colors = plt.cm.Set3(np.linspace(0, 1, len(methods)))
        for patch, color in zip(bp['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)
        
        ax4.set_ylabel('Dice Coefficient')
        ax4.set_title('Dice Coefficient Distribution')
        ax4.tick_params(axis='x', rotation=45)
        ax4.grid(True, alpha=0.3)
        
        # 5. Precision vs Recall
        ax5 = plt.subplot(3, 3, 7)
        for i, method in enumerate(methods):
            precision_values = [m['precision'] for m in comparator.results[method]['metrics']]
            recall_values = [m['recall'] for m in comparator.results[method]['metrics']]
            
            ax5.scatter(recall_values, precision_values, 
                       alpha=0.7, label=method, color=colors[i])
        
        ax5.set_xlabel('Recall')
        ax5.set_ylabel('Precision')
        ax5.set_title('Precision vs Recall')
        ax5.legend()
        ax5.grid(True, alpha=0.3)
        
        # 6. Overall ranking
        ax6 = plt.subplot(3, 3, (8, 9))
        
        # Calculate overall score
        overall_scores = {}
        for method in methods:
            score = 0
            for metric in ['dice_coefficient', 'jaccard_index', 'accuracy', 'precision', 'recall']:
                avg_value = np.mean([m[metric] for m in comparator.results[method]['metrics']])
                score += avg_value
            overall_scores[method] = score / 5
        
        # Sort methods by overall score
        sorted_methods = sorted(overall_scores.items(), key=lambda x: x[1], reverse=True)
        
        methods_sorted = [item[0] for item in sorted_methods]
        scores_sorted = [item[1] for item in sorted_methods]
        
        bars = ax6.barh(methods_sorted, scores_sorted, color=plt.cm.Set3(np.linspace(0, 1, len(methods))))
        ax6.set_xlabel('Overall Score')
        ax6.set_title('Overall Method Ranking')
        ax6.grid(True, alpha=0.3)
        
        # Add score labels
        for bar, score in zip(bars, scores_sorted):
            width = bar.get_width()
            ax6.text(width, bar.get_y() + bar.get_height()/2.,
                    f'{score:.3f}', ha='left', va='center')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()


class ResultsReporter:
    """
    Generate comprehensive results reports.
    """
    
    def __init__(self):
        """Initialize results reporter."""
        self.report_data = {}
        
    def generate_text_report(self, comparator: MethodComparator,
                           save_path: Optional[str] = None) -> str:
        """
        Generate text-based results report.
        
        Args:
            comparator: Method comparator instance
            save_path: Path to save the report
            
        Returns:
            Report as string
        """
        report = comparator.generate_comparison_report()
        
        # Generate report text
        report_text = []
        report_text.append("SEGMENTATION RESULTS COMPARISON REPORT")
        report_text.append("=" * 50)
        report_text.append(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_text.append(f"Number of methods compared: {len(report['methods'])}")
        report_text.append(f"Methods: {', '.join(report['methods'])}")
        report_text.append("")
        
        # Summary statistics
        report_text.append("SUMMARY STATISTICS")
        report_text.append("-" * 30)
        
        for method in report['methods']:
            report_text.append(f"\n{method}:")
            stats = report['summary_statistics'][method]
            
            for metric, values in stats.items():
                report_text.append(f"  {metric}:")
                report_text.append(f"    Mean: {values['mean']:.4f} ± {values['std']:.4f}")
                report_text.append(f"    Range: [{values['min']:.4f}, {values['max']:.4f}]")
                report_text.append(f"    Median: {values['median']:.4f}")
        
        # Rankings
        report_text.append("\n\nMETHOD RANKINGS")
        report_text.append("-" * 30)
        
        for metric, rankings in report['rankings'].items():
            report_text.append(f"\n{metric}:")
            for i, (method, value) in enumerate(rankings):
                report_text.append(f"  {i+1}. {method}: {value:.4f}")
        
        # Best method
        report_text.append(f"\n\nBEST METHOD OVERALL")
        report_text.append("-" * 30)
        report_text.append(f"Method: {report['best_method']['method']}")
        report_text.append(f"Average rank: {report['best_method']['average_rank']:.2f}")
        
        # Pairwise comparisons
        report_text.append(f"\n\nPAIRWISE COMPARISONS")
        report_text.append("-" * 30)
        
        for comparison_key, comparison_data in report['pairwise_comparisons'].items():
            report_text.append(f"\n{comparison_key}:")
            
            for metric, comp_result in comparison_data.items():
                report_text.append(f"  {metric}:")
                report_text.append(f"    {comp_result['method1']}: {comp_result['method1_mean']:.4f}")
                report_text.append(f"    {comp_result['method2']}: {comp_result['method2_mean']:.4f}")
                report_text.append(f"    Difference: {comp_result['difference']:.4f}")
                report_text.append(f"    P-value (t-test): {comp_result['paired_t_test']['p_value']:.4f}")
                report_text.append(f"    Significant: {comp_result['paired_t_test']['significant']}")
                report_text.append(f"    Effect size: {comp_result['effect_size']}")
        
        report_str = "\n".join(report_text)
        
        if save_path:
            with open(save_path, 'w') as f:
                f.write(report_str)
        
        return report_str
    
    def generate_json_report(self, comparator: MethodComparator,
                           save_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate JSON-formatted results report.
        
        Args:
            comparator: Method comparator instance
            save_path: Path to save the report
            
        Returns:
            Report as dictionary
        """
        report = comparator.generate_comparison_report()
        
        # Add metadata
        report['metadata'] = {
            'generated_on': datetime.now().isoformat(),
            'version': '1.0',
            'author': 'Dr. Mohammed Yagoub Esmail'
        }
        
        if save_path:
            with open(save_path, 'w') as f:
                json.dump(report, f, indent=2)
        
        return report
    
    def generate_csv_summary(self, comparator: MethodComparator,
                           save_path: Optional[str] = None) -> pd.DataFrame:
        """
        Generate CSV summary of results.
        
        Args:
            comparator: Method comparator instance
            save_path: Path to save the CSV
            
        Returns:
            DataFrame with results summary
        """
        report = comparator.generate_comparison_report()
        
        # Create summary dataframe
        summary_data = []
        
        for method in report['methods']:
            stats = report['summary_statistics'][method]
            
            row = {'Method': method}
            
            # Add metrics
            for metric, values in stats.items():
                row[f'{metric}_mean'] = values['mean']
                row[f'{metric}_std'] = values['std']
                row[f'{metric}_min'] = values['min']
                row[f'{metric}_max'] = values['max']
            
            # Add rankings
            for metric, rankings in report['rankings'].items():
                rank = next(i for i, (name, _) in enumerate(rankings) if name == method) + 1
                row[f'{metric}_rank'] = rank
            
            summary_data.append(row)
        
        df = pd.DataFrame(summary_data)
        
        if save_path:
            df.to_csv(save_path, index=False)
        
        return df


def main():
    """
    Example usage of the results calculation module.
    """
    print("Results Calculation and Comparison Module Demo")
    print("=" * 60)
    
    # Create sample data
    np.random.seed(42)
    
    # Generate synthetic ground truth and predictions
    n_samples = 20
    image_size = (128, 128)
    
    ground_truth = []
    predictions_method1 = []
    predictions_method2 = []
    predictions_method3 = []
    
    print("Generating synthetic segmentation data...")
    
    for i in range(n_samples):
        # Create ground truth (circular tumor)
        gt = np.zeros(image_size)
        center = (64, 64)
        radius = 20 + np.random.normal(0, 5)
        
        y, x = np.ogrid[:image_size[0], :image_size[1]]
        mask = (x - center[0])**2 + (y - center[1])**2 <= radius**2
        gt[mask] = 1
        
        # Method 1: Good segmentation (high accuracy)
        pred1 = gt.copy()
        noise = np.random.normal(0, 0.1, image_size)
        pred1 = (pred1 + noise > 0.5).astype(np.uint8)
        
        # Method 2: Moderate segmentation (medium accuracy)
        pred2 = gt.copy()
        noise = np.random.normal(0, 0.3, image_size)
        pred2 = (pred2 + noise > 0.3).astype(np.uint8)
        
        # Method 3: Poor segmentation (low accuracy)
        pred3 = gt.copy()
        noise = np.random.normal(0, 0.5, image_size)
        pred3 = (pred3 + noise > 0.1).astype(np.uint8)
        
        ground_truth.append(gt)
        predictions_method1.append(pred1)
        predictions_method2.append(pred2)
        predictions_method3.append(pred3)
    
    # Initialize comparator
    comparator = MethodComparator()
    
    # Add methods
    print("Adding method results...")
    comparator.add_method_results("High Accuracy Method", ground_truth, predictions_method1)
    comparator.add_method_results("Medium Accuracy Method", ground_truth, predictions_method2)
    comparator.add_method_results("Low Accuracy Method", ground_truth, predictions_method3)
    
    # Generate comparison report
    print("Generating comparison report...")
    report = comparator.generate_comparison_report()
    
    # Print summary
    print("\nMethod Rankings (Dice Coefficient):")
    for i, (method, score) in enumerate(report['rankings']['dice_coefficient']):
        print(f"{i+1}. {method}: {score:.4f}")
    
    print(f"\nBest method overall: {report['best_method']['method']}")
    print(f"Average rank: {report['best_method']['average_rank']:.2f}")
    
    # Create visualizations
    print("\nCreating visualizations...")
    visualizer = ResultsVisualizer()
    
    # Metrics comparison
    visualizer.plot_metrics_comparison(comparator)
    
    # Method ranking
    visualizer.plot_method_ranking(comparator)
    
    # Volume comparison
    visualizer.plot_volume_comparison(comparator)
    
    # Performance summary
    visualizer.plot_performance_summary(comparator)
    
    # Results dashboard
    visualizer.create_results_dashboard(comparator)
    
    # Generate reports
    print("\nGenerating reports...")
    reporter = ResultsReporter()
    
    # Text report
    text_report = reporter.generate_text_report(comparator)
    print("\nText report generated (first 500 characters):")
    print(text_report[:500] + "...")
    
    # JSON report
    json_report = reporter.generate_json_report(comparator)
    print(f"\nJSON report generated with {len(json_report)} sections")
    
    # CSV summary
    csv_summary = reporter.generate_csv_summary(comparator)
    print(f"\nCSV summary generated with {len(csv_summary)} rows and {len(csv_summary.columns)} columns")
    
    # Statistical comparison example
    print("\nStatistical comparison example:")
    comparison = comparator.compare_methods("High Accuracy Method", "Medium Accuracy Method", "dice_coefficient")
    print(f"Comparison: {comparison['method1']} vs {comparison['method2']}")
    print(f"Mean difference: {comparison['difference']:.4f}")
    print(f"P-value (t-test): {comparison['paired_t_test']['p_value']:.4f}")
    print(f"Significant: {comparison['paired_t_test']['significant']}")
    print(f"Effect size: {comparison['effect_size']}")
    
    print("\nDemo completed successfully!")


if __name__ == "__main__":
    main()