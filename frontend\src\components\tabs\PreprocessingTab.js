import React, { useState } from 'react';
import CodeBlock from '../CodeBlock';

const PreprocessingTab = ({ analysisResults }) => {
  const [codeLanguage, setCodeLanguage] = useState('python');

  const preprocessingMethods = [
    {
      id: 'contrast',
      title: 'Contrast Enhancement',
      icon: 'fas fa-adjust',
      description: 'CLAHE and histogram equalization',
      color: '#3498db'
    },
    {
      id: 'brightness',
      title: 'Brightness Adaptation',
      icon: 'fas fa-sun',
      description: 'Gamma correction and linear adjustment',
      color: '#f39c12'
    },
    {
      id: 'filtering',
      title: 'Noise Filtering',
      icon: 'fas fa-filter',
      description: 'Gaussian, median, and bilateral filtering',
      color: '#27ae60'
    },
    {
      id: 'enhancement',
      title: 'Edge Enhancement',
      icon: 'fas fa-vector-square',
      description: 'Unsharp masking and edge detection',
      color: '#e74c3c'
    }
  ];

  const pythonCode = `import cv2
import numpy as np
from scipy import ndimage
from skimage import exposure, filters
from skimage.restoration import denoise_bilateral

class ImagePreprocessor:
    def __init__(self):
        self.processed_images = []
        self.processing_params = {}
    
    def contrast_enhancement(self, image, method='clahe'):
        """Apply contrast enhancement techniques"""
        if method == 'clahe':
            # Contrast Limited Adaptive Histogram Equalization
            image_uint8 = (image * 255).astype(np.uint8)
            clahe = cv2.createCLAHE(clipLimit=0.01, tileGridSize=(8, 8))
            enhanced = clahe.apply(image_uint8)
            return enhanced.astype(np.float32) / 255.0
        
        elif method == 'histogram_equalization':
            # Standard histogram equalization
            image_uint8 = (image * 255).astype(np.uint8)
            equalized = cv2.equalizeHist(image_uint8)
            return equalized.astype(np.float32) / 255.0
        
        elif method == 'adaptive_equalization':
            # Adaptive histogram equalization
            return exposure.equalize_adapthist(image, clip_limit=0.01)
        
        return image
    
    def brightness_adjustment(self, image, method='gamma', gamma=1.2):
        """Apply brightness adjustment"""
        if method == 'gamma':
            # Gamma correction
            return exposure.adjust_gamma(image, gamma=gamma)
        
        elif method == 'linear':
            # Linear brightness adjustment
            brightness = 0.1
            return np.clip(image + brightness, 0, 1)
        
        elif method == 'sigmoid':
            # Sigmoid adjustment
            return exposure.adjust_sigmoid(image, cutoff=0.5, gain=10)
        
        return image
    
    def noise_reduction(self, image, method='bilateral'):
        """Apply noise reduction techniques"""
        if method == 'bilateral':
            # Bilateral filtering
            image_uint8 = (image * 255).astype(np.uint8)
            filtered = denoise_bilateral(image_uint8, sigma_color=0.05, sigma_spatial=1.0)
            return filtered.astype(np.float32) / 255.0
        
        elif method == 'gaussian':
            # Gaussian filtering
            return filters.gaussian(image, sigma=1.0)
        
        elif method == 'median':
            # Median filtering
            return filters.median(image, disk(3))
        
        return image
    
    def edge_enhancement(self, image, method='unsharp_mask'):
        """Apply edge enhancement techniques"""
        if method == 'unsharp_mask':
            # Unsharp masking
            radius = 1.0
            amount = 1.5
            
            # Create Gaussian blur
            blurred = filters.gaussian(image, sigma=radius)
            
            # Unsharp mask
            mask = image - blurred
            enhanced = image + amount * mask
            
            return np.clip(enhanced, 0, 1)
        
        elif method == 'laplacian':
            # Laplacian edge enhancement
            laplacian = cv2.Laplacian(image, cv2.CV_64F)
            enhanced = image + 0.1 * laplacian
            return np.clip(enhanced, 0, 1)
        
        return image
    
    def preprocess_image(self, image, params=None):
        """Complete preprocessing pipeline"""
        if params is None:
            params = {
                'contrast_method': 'clahe',
                'brightness_method': 'gamma',
                'noise_method': 'bilateral',
                'edge_method': 'unsharp_mask',
                'gamma': 1.2
            }
        
        processed = image.copy()
        
        # Step 1: Noise reduction
        processed = self.noise_reduction(processed, params['noise_method'])
        
        # Step 2: Contrast enhancement
        processed = self.contrast_enhancement(processed, params['contrast_method'])
        
        # Step 3: Brightness adjustment
        processed = self.brightness_adjustment(processed, params['brightness_method'], 
                                             params['gamma'])
        
        # Step 4: Edge enhancement
        processed = self.edge_enhancement(processed, params['edge_method'])
        
        return processed
    
    def batch_preprocess(self, images, params=None):
        """Preprocess multiple images"""
        processed_images = []
        
        for image in images:
            processed = self.preprocess_image(image, params)
            processed_images.append(processed)
        
        return processed_images
    
    def adaptive_preprocessing(self, image):
        """Adaptive preprocessing based on image characteristics"""
        # Analyze image characteristics
        mean_intensity = np.mean(image)
        std_intensity = np.std(image)
        
        # Adaptive parameters
        if mean_intensity < 0.3:
            # Dark image - increase brightness
            gamma = 0.8
            contrast_method = 'adaptive_equalization'
        elif mean_intensity > 0.7:
            # Bright image - reduce brightness
            gamma = 1.4
            contrast_method = 'clahe'
        else:
            # Normal image
            gamma = 1.2
            contrast_method = 'clahe'
        
        # Adaptive noise reduction
        if std_intensity > 0.2:
            noise_method = 'bilateral'
        else:
            noise_method = 'gaussian'
        
        params = {
            'contrast_method': contrast_method,
            'brightness_method': 'gamma',
            'noise_method': noise_method,
            'edge_method': 'unsharp_mask',
            'gamma': gamma
        }
        
        return self.preprocess_image(image, params)

# Usage example
preprocessor = ImagePreprocessor()
processed_image = preprocessor.preprocess_image(mri_image)
adaptive_processed = preprocessor.adaptive_preprocessing(mri_image)`;

  const matlabCode = `classdef ImagePreprocessor < handle
    properties
        processedImages
        processingParams
    end
    
    methods
        function obj = ImagePreprocessor()
            obj.processedImages = [];
            obj.processingParams = struct();
        end
        
        function enhanced = contrastEnhancement(obj, image, method)
            % Apply contrast enhancement techniques
            if nargin < 3
                method = 'clahe';
            end
            
            switch method
                case 'clahe'
                    % Contrast Limited Adaptive Histogram Equalization
                    enhanced = adapthisteq(image, 'ClipLimit', 0.01, 'NumTiles', [8 8]);
                    
                case 'histogram_equalization'
                    % Standard histogram equalization
                    enhanced = histeq(image);
                    
                case 'adaptive_equalization'
                    % Adaptive histogram equalization
                    enhanced = adapthisteq(image);
                    
                otherwise
                    enhanced = image;
            end
        end
        
        function adjusted = brightnessAdjustment(obj, image, method, gamma)
            % Apply brightness adjustment
            if nargin < 3
                method = 'gamma';
            end
            if nargin < 4
                gamma = 1.2;
            end
            
            switch method
                case 'gamma'
                    % Gamma correction
                    adjusted = imadjust(image, [], [], gamma);
                    
                case 'linear'
                    % Linear brightness adjustment
                    brightness = 0.1;
                    adjusted = image + brightness;
                    adjusted = max(0, min(1, adjusted));
                    
                case 'sigmoid'
                    % Sigmoid adjustment
                    adjusted = imadjust(image, [], [], 1, 0.5);
                    
                otherwise
                    adjusted = image;
            end
        end
        
        function filtered = noiseReduction(obj, image, method)
            % Apply noise reduction techniques
            if nargin < 3
                method = 'bilateral';
            end
            
            switch method
                case 'bilateral'
                    % Bilateral filtering (approximation using guided filter)
                    filtered = imguidedfilter(image, image, 'NeighborhoodSize', [5 5]);
                    
                case 'gaussian'
                    % Gaussian filtering
                    h = fspecial('gaussian', [5 5], 1.0);
                    filtered = imfilter(image, h, 'replicate');
                    
                case 'median'
                    % Median filtering
                    filtered = medfilt2(image, [3 3]);
                    
                otherwise
                    filtered = image;
            end
        end
        
        function enhanced = edgeEnhancement(obj, image, method)
            % Apply edge enhancement techniques
            if nargin < 3
                method = 'unsharp_mask';
            end
            
            switch method
                case 'unsharp_mask'
                    % Unsharp masking
                    radius = 1.0;
                    amount = 1.5;
                    
                    % Create Gaussian blur
                    h = fspecial('gaussian', [5 5], radius);
                    blurred = imfilter(image, h, 'replicate');
                    
                    % Unsharp mask
                    mask = image - blurred;
                    enhanced = image + amount * mask;
                    enhanced = max(0, min(1, enhanced));
                    
                case 'laplacian'
                    % Laplacian edge enhancement
                    h = fspecial('laplacian', 0.2);
                    laplacian = imfilter(image, h, 'replicate');
                    enhanced = image + 0.1 * laplacian;
                    enhanced = max(0, min(1, enhanced));
                    
                otherwise
                    enhanced = image;
            end
        end
        
        function processed = preprocessImage(obj, image, params)
            % Complete preprocessing pipeline
            if nargin < 3
                params = struct();
                params.contrastMethod = 'clahe';
                params.brightnessMethod = 'gamma';
                params.noiseMethod = 'bilateral';
                params.edgeMethod = 'unsharp_mask';
                params.gamma = 1.2;
            end
            
            processed = image;
            
            % Step 1: Noise reduction
            processed = obj.noiseReduction(processed, params.noiseMethod);
            
            % Step 2: Contrast enhancement
            processed = obj.contrastEnhancement(processed, params.contrastMethod);
            
            % Step 3: Brightness adjustment
            processed = obj.brightnessAdjustment(processed, params.brightnessMethod, params.gamma);
            
            % Step 4: Edge enhancement
            processed = obj.edgeEnhancement(processed, params.edgeMethod);
        end
        
        function processedImages = batchPreprocess(obj, images, params)
            % Preprocess multiple images
            processedImages = cell(size(images));
            
            for i = 1:length(images)
                processedImages{i} = obj.preprocessImage(images{i}, params);
            end
        end
        
        function processed = adaptivePreprocessing(obj, image)
            % Adaptive preprocessing based on image characteristics
            
            % Analyze image characteristics
            meanIntensity = mean(image(:));
            stdIntensity = std(image(:));
            
            % Adaptive parameters
            if meanIntensity < 0.3
                % Dark image - increase brightness
                gamma = 0.8;
                contrastMethod = 'adaptive_equalization';
            elseif meanIntensity > 0.7
                % Bright image - reduce brightness
                gamma = 1.4;
                contrastMethod = 'clahe';
            else
                % Normal image
                gamma = 1.2;
                contrastMethod = 'clahe';
            end
            
            % Adaptive noise reduction
            if stdIntensity > 0.2
                noiseMethod = 'bilateral';
            else
                noiseMethod = 'gaussian';
            end
            
            params = struct();
            params.contrastMethod = contrastMethod;
            params.brightnessMethod = 'gamma';
            params.noiseMethod = noiseMethod;
            params.edgeMethod = 'unsharp_mask';
            params.gamma = gamma;
            
            processed = obj.preprocessImage(image, params);
        end
    end
end

% Usage example
preprocessor = ImagePreprocessor();
processedImage = preprocessor.preprocessImage(mriImage);
adaptiveProcessed = preprocessor.adaptivePreprocessing(mriImage);`;

  return (
    <div className="preprocessing-tab">
      <div className="detail-header">
        <i className="fas fa-cog detail-icon"></i>
        <div>
          <h3 className="detail-title">2. Advanced Image Preprocessing</h3>
          <p className="detail-subtitle">
            Enhance image quality through contrast adaptation, noise reduction, and edge enhancement
          </p>
        </div>
      </div>

      <div className="preprocessing-methods">
        <h4>Preprocessing Techniques</h4>
        <div className="methods-grid">
          {preprocessingMethods.map((method) => (
            <div key={method.id} className="method-card">
              <div className="method-header">
                <div className="method-icon" style={{ background: method.color }}>
                  <i className={method.icon}></i>
                </div>
                <div>
                  <h5>{method.title}</h5>
                  <p>{method.description}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="preprocessing-pipeline">
        <h4>Preprocessing Pipeline</h4>
        <div className="pipeline-flow">
          <div className="flow-step">
            <div className="flow-icon" style={{ background: '#3498db' }}>
              <i className="fas fa-image"></i>
            </div>
            <div className="flow-content">
              <h5>Original Image</h5>
              <p>Raw MRI slice input</p>
            </div>
          </div>
          <div className="flow-arrow">→</div>
          <div className="flow-step">
            <div className="flow-icon" style={{ background: '#27ae60' }}>
              <i className="fas fa-filter"></i>
            </div>
            <div className="flow-content">
              <h5>Noise Reduction</h5>
              <p>Bilateral/Gaussian filtering</p>
            </div>
          </div>
          <div className="flow-arrow">→</div>
          <div className="flow-step">
            <div className="flow-icon" style={{ background: '#3498db' }}>
              <i className="fas fa-adjust"></i>
            </div>
            <div className="flow-content">
              <h5>Contrast Enhancement</h5>
              <p>CLAHE/Histogram equalization</p>
            </div>
          </div>
          <div className="flow-arrow">→</div>
          <div className="flow-step">
            <div className="flow-icon" style={{ background: '#f39c12' }}>
              <i className="fas fa-sun"></i>
            </div>
            <div className="flow-content">
              <h5>Brightness Adjustment</h5>
              <p>Gamma correction</p>
            </div>
          </div>
          <div className="flow-arrow">→</div>
          <div className="flow-step">
            <div className="flow-icon" style={{ background: '#e74c3c' }}>
              <i className="fas fa-vector-square"></i>
            </div>
            <div className="flow-content">
              <h5>Edge Enhancement</h5>
              <p>Unsharp masking</p>
            </div>
          </div>
        </div>
      </div>

      <div className="preprocessing-details">
        <div className="technique-cards">
          <div className="technique-card">
            <div className="technique-header">
              <div className="technique-icon">
                <i className="fas fa-adjust"></i>
              </div>
              <div>
                <h4>Contrast Enhancement</h4>
                <span className="technique-badge">CLAHE</span>
              </div>
            </div>
            <div className="technique-content">
              <p><strong>Contrast Limited Adaptive Histogram Equalization</strong></p>
              <ul className="technique-features">
                <li>• Adaptive local contrast enhancement</li>
                <li>• Prevents over-amplification of noise</li>
                <li>• Preserves image details</li>
                <li>• Configurable clip limit and tile size</li>
              </ul>
              <div className="technique-params">
                <span className="param">Clip Limit: 0.01</span>
                <span className="param">Tiles: 8×8</span>
              </div>
            </div>
          </div>

          <div className="technique-card">
            <div className="technique-header">
              <div className="technique-icon">
                <i className="fas fa-sun"></i>
              </div>
              <div>
                <h4>Brightness Adaptation</h4>
                <span className="technique-badge">Gamma Correction</span>
              </div>
            </div>
            <div className="technique-content">
              <p><strong>Adaptive Gamma Correction</strong></p>
              <ul className="technique-features">
                <li>• Non-linear intensity transformation</li>
                <li>• Preserves relative contrast</li>
                <li>• Adaptive based on image statistics</li>
                <li>• Enhances visibility of structures</li>
              </ul>
              <div className="technique-params">
                <span className="param">Gamma: 1.2</span>
                <span className="param">Adaptive: Yes</span>
              </div>
            </div>
          </div>

          <div className="technique-card">
            <div className="technique-header">
              <div className="technique-icon">
                <i className="fas fa-filter"></i>
              </div>
              <div>
                <h4>Noise Reduction</h4>
                <span className="technique-badge">Bilateral Filter</span>
              </div>
            </div>
            <div className="technique-content">
              <p><strong>Edge-Preserving Noise Reduction</strong></p>
              <ul className="technique-features">
                <li>• Preserves edges while reducing noise</li>
                <li>• Spatial and intensity domain filtering</li>
                <li>• Maintains tissue boundaries</li>
                <li>• Reduces speckle and thermal noise</li>
              </ul>
              <div className="technique-params">
                <span className="param">Sigma Color: 0.05</span>
                <span className="param">Sigma Spatial: 1.0</span>
              </div>
            </div>
          </div>

          <div className="technique-card">
            <div className="technique-header">
              <div className="technique-icon">
                <i className="fas fa-vector-square"></i>
              </div>
              <div>
                <h4>Edge Enhancement</h4>
                <span className="technique-badge">Unsharp Masking</span>
              </div>
            </div>
            <div className="technique-content">
              <p><strong>Tumor Boundary Sharpening</strong></p>
              <ul className="technique-features">
                <li>• Unsharp masking technique</li>
                <li>• Laplacian edge detection</li>
                <li>• Enhances tumor-tissue contrast</li>
                <li>• Improves segmentation accuracy</li>
              </ul>
              <div className="technique-params">
                <span className="param">Radius: 1.0</span>
                <span className="param">Amount: 1.5</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="preprocessing-comparison">
        <h4>Before & After Preprocessing</h4>
        <div className="comparison-grid">
          <div className="comparison-item">
            <div className="comparison-image">
              <i className="fas fa-image"></i>
              <span>Original Image</span>
            </div>
            <div className="comparison-label">Raw MRI Slice</div>
          </div>
          <div className="comparison-item">
            <div className="comparison-image">
              <i className="fas fa-adjust"></i>
              <span>Enhanced Contrast</span>
            </div>
            <div className="comparison-label">CLAHE Applied</div>
          </div>
          <div className="comparison-item">
            <div className="comparison-image">
              <i className="fas fa-filter"></i>
              <span>Noise Reduced</span>
            </div>
            <div className="comparison-label">Bilateral Filter</div>
          </div>
          <div className="comparison-item">
            <div className="comparison-image">
              <i className="fas fa-check"></i>
              <span>Final Result</span>
            </div>
            <div className="comparison-label">Fully Processed</div>
          </div>
        </div>
      </div>

      <div className="code-section">
        <div className="code-header">
          <h4>Preprocessing Implementation</h4>
          <div className="code-tabs">
            <button 
              className={`code-tab ${codeLanguage === 'python' ? 'active' : ''}`}
              onClick={() => setCodeLanguage('python')}
            >
              Python
            </button>
            <button 
              className={`code-tab ${codeLanguage === 'matlab' ? 'active' : ''}`}
              onClick={() => setCodeLanguage('matlab')}
            >
              MATLAB
            </button>
          </div>
        </div>
        <CodeBlock 
          code={codeLanguage === 'python' ? pythonCode : matlabCode} 
          language={codeLanguage}
        />
      </div>

      <style jsx>{`
        .preprocessing-tab {
          padding: 20px 0;
        }

        .detail-header {
          display: flex;
          align-items: center;
          gap: 16px;
          margin-bottom: 40px;
          padding-bottom: 20px;
          border-bottom: 2px solid var(--border-color);
        }

        .detail-icon {
          font-size: 32px;
          color: var(--primary-color);
        }

        .detail-title {
          font-size: 24px;
          font-weight: 600;
          color: var(--text-primary);
          margin-bottom: 8px;
        }

        .detail-subtitle {
          color: var(--text-secondary);
          font-size: 16px;
        }

        .preprocessing-methods {
          margin-bottom: 40px;
        }

        .preprocessing-methods h4 {
          margin-bottom: 20px;
          color: var(--text-primary);
        }

        .methods-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 20px;
        }

        .method-card {
          background: white;
          border-radius: var(--border-radius);
          padding: 20px;
          box-shadow: var(--shadow-light);
          transition: var(--transition);
        }

        .method-card:hover {
          box-shadow: var(--shadow-medium);
          transform: translateY(-2px);
        }

        .method-header {
          display: flex;
          align-items: center;
          gap: 16px;
        }

        .method-icon {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 20px;
        }

        .method-header h5 {
          margin: 0 0 4px 0;
          color: var(--text-primary);
        }

        .method-header p {
          margin: 0;
          color: var(--text-secondary);
          font-size: 14px;
        }

        .preprocessing-pipeline {
          margin-bottom: 40px;
        }

        .preprocessing-pipeline h4 {
          margin-bottom: 20px;
          color: var(--text-primary);
        }

        .pipeline-flow {
          display: flex;
          align-items: center;
          gap: 20px;
          padding: 30px;
          background: white;
          border-radius: var(--border-radius);
          box-shadow: var(--shadow-light);
          overflow-x: auto;
        }

        .flow-step {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
          min-width: 120px;
        }

        .flow-icon {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 20px;
          margin-bottom: 12px;
        }

        .flow-content h5 {
          margin: 0 0 4px 0;
          color: var(--text-primary);
          font-size: 14px;
        }

        .flow-content p {
          margin: 0;
          color: var(--text-secondary);
          font-size: 12px;
        }

        .flow-arrow {
          font-size: 20px;
          color: var(--primary-color);
          font-weight: bold;
        }

        .preprocessing-details {
          margin-bottom: 40px;
        }

        .technique-cards {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 24px;
        }

        .technique-card {
          background: white;
          border-radius: var(--border-radius);
          padding: 24px;
          box-shadow: var(--shadow-light);
        }

        .technique-header {
          display: flex;
          align-items: center;
          gap: 16px;
          margin-bottom: 16px;
        }

        .technique-icon {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 20px;
        }

        .technique-header h4 {
          margin: 0;
          color: var(--text-primary);
        }

        .technique-badge {
          background: var(--primary-color);
          color: white;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;
        }

        .technique-content p {
          margin: 0 0 12px 0;
          color: var(--text-primary);
          font-weight: 500;
        }

        .technique-features {
          list-style: none;
          padding: 0;
          margin: 0 0 16px 0;
        }

        .technique-features li {
          padding: 4px 0;
          color: var(--text-secondary);
          font-size: 14px;
        }

        .technique-params {
          display: flex;
          gap: 12px;
          flex-wrap: wrap;
        }

        .param {
          background: var(--background-dark);
          color: var(--text-secondary);
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-family: monospace;
        }

        .preprocessing-comparison {
          margin-bottom: 40px;
        }

        .preprocessing-comparison h4 {
          margin-bottom: 20px;
          color: var(--text-primary);
        }

        .comparison-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 20px;
        }

        .comparison-item {
          text-align: center;
        }

        .comparison-image {
          width: 100%;
          height: 120px;
          background: var(--background-dark);
          border-radius: var(--border-radius);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          margin-bottom: 8px;
          border: 2px dashed var(--border-color);
        }

        .comparison-image i {
          font-size: 24px;
          color: var(--primary-color);
          margin-bottom: 8px;
        }

        .comparison-image span {
          color: var(--text-secondary);
          font-size: 14px;
        }

        .comparison-label {
          font-size: 12px;
          color: var(--text-secondary);
          font-weight: 500;
        }

        .code-section {
          background: white;
          border-radius: var(--border-radius);
          overflow: hidden;
          box-shadow: var(--shadow-light);
        }

        .code-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20px;
          border-bottom: 1px solid var(--border-color);
        }

        .code-header h4 {
          margin: 0;
          color: var(--text-primary);
        }

        .code-tabs {
          display: flex;
          gap: 8px;
        }

        .code-tab {
          padding: 8px 16px;
          border: 1px solid var(--border-color);
          background: white;
          cursor: pointer;
          border-radius: var(--border-radius);
          font-size: 14px;
          transition: var(--transition);
        }

        .code-tab.active {
          background: var(--primary-color);
          color: white;
          border-color: var(--primary-color);
        }

        @media (max-width: 768px) {
          .pipeline-flow {
            flex-direction: column;
          }

          .flow-arrow {
            transform: rotate(90deg);
          }
        }
      `}</style>
    </div>
  );
};

export default PreprocessingTab;