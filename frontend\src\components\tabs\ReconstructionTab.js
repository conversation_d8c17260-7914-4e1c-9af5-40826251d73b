import React, { useState } from 'react';
import CodeBlock from '../CodeBlock';

const ReconstructionTab = ({ analysisResults }) => {
  const [codeLanguage, setCodeLanguage] = useState('python');
  const [activeView, setActiveView] = useState('surface');

  const reconstructionMethods = [
    {
      id: 'surface',
      title: 'Surface Rendering',
      icon: 'fas fa-cube',
      description: 'Marching Cubes algorithm for 3D surface generation',
      color: '#e74c3c'
    },
    {
      id: 'volume',
      title: 'Volume Rendering',
      icon: 'fas fa-layer-group',
      description: 'Ray casting and volume visualization',
      color: '#3498db'
    },
    {
      id: 'orthogonal',
      title: 'Orthogonal Views',
      icon: 'fas fa-th-large',
      description: 'Axial, sagittal, and coronal projections',
      color: '#27ae60'
    },
    {
      id: 'metrics',
      title: 'Volume Metrics',
      icon: 'fas fa-calculator',
      description: 'Quantitative analysis and measurements',
      color: '#f39c12'
    }
  ];

  const pythonCode = `import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from mpl_toolkits.mplot3d.art3d import Poly3DCollection
from skimage import measure
from skimage.morphology import binary_closing, ball
from scipy.spatial.distance import pdist, squareform
import trimesh

class Reconstructor3D:
    def __init__(self):
        self.mesh_data = {}
        self.volume_metrics = {}
        self.orthogonal_views = {}
        
    def surface_rendering(self, volume_data, threshold=0.5, smooth=True):
        """Generate 3D surface using Marching Cubes algorithm"""
        
        # Apply binary closing to fill holes
        if smooth:
            volume_data = binary_closing(volume_data, ball(2))
        
        # Generate mesh using marching cubes
        try:
            vertices, faces, normals, values = measure.marching_cubes(
                volume_data, 
                level=threshold,
                spacing=(1.0, 1.0, 1.0),
                gradient_direction='descent'
            )
            
            # Store mesh data
            self.mesh_data = {
                'vertices': vertices,
                'faces': faces,
                'normals': normals,
                'values': values,
                'vertex_count': len(vertices),
                'face_count': len(faces)
            }
            
            return vertices, faces, normals
            
        except Exception as e:
            print(f"Error in surface rendering: {e}")
            return None, None, None
    
    def volume_rendering(self, volume_data, opacity=0.1):
        """Generate volume rendering using ray casting"""
        
        # Create figure for volume rendering
        fig = plt.figure(figsize=(12, 10))
        ax = fig.add_subplot(111, projection='3d')
        
        # Get volume dimensions
        depth, height, width = volume_data.shape
        
        # Create coordinate grids
        x, y, z = np.meshgrid(
            np.arange(width),
            np.arange(height),
            np.arange(depth)
        )
        
        # Apply threshold for visualization
        mask = volume_data > 0.5
        
        # Create volume rendering
        ax.scatter(x[mask], y[mask], z[mask], 
                  c=volume_data[mask], 
                  alpha=opacity, 
                  s=1,
                  cmap='viridis')
        
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_zlabel('Z')
        ax.set_title('3D Volume Rendering')
        
        return fig
    
    def create_orthogonal_views(self, volume_data):
        """Generate orthogonal projections (axial, sagittal, coronal)"""
        
        depth, height, width = volume_data.shape
        
        # Calculate projection centers
        center_z = depth // 2
        center_y = height // 2
        center_x = width // 2
        
        # Generate orthogonal slices
        orthogonal_views = {
            'axial': volume_data[center_z, :, :],        # XY plane
            'sagittal': volume_data[:, :, center_x],     # YZ plane
            'coronal': volume_data[:, center_y, :]       # XZ plane
        }
        
        # Create visualization
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # Axial view
        axes[0].imshow(orthogonal_views['axial'], cmap='gray')
        axes[0].set_title('Axial View (XY)')
        axes[0].axis('off')
        
        # Sagittal view
        axes[1].imshow(orthogonal_views['sagittal'], cmap='gray')
        axes[1].set_title('Sagittal View (YZ)')
        axes[1].axis('off')
        
        # Coronal view
        axes[2].imshow(orthogonal_views['coronal'], cmap='gray')
        axes[2].set_title('Coronal View (XZ)')
        axes[2].axis('off')
        
        plt.tight_layout()
        
        self.orthogonal_views = orthogonal_views
        
        return orthogonal_views, fig
    
    def calculate_volume_metrics(self, volume_data, voxel_size=(1.0, 1.0, 1.0)):
        """Calculate quantitative volume metrics"""
        
        # Binary volume
        binary_volume = volume_data > 0.5
        
        # Basic volume metrics
        voxel_count = np.sum(binary_volume)
        voxel_volume = np.prod(voxel_size)  # Volume of single voxel
        total_volume_mm3 = voxel_count * voxel_volume
        total_volume_ml = total_volume_mm3 / 1000.0  # Convert to milliliters
        
        # Surface area calculation (approximation)
        surface_voxels = self._find_surface_voxels(binary_volume)
        surface_area_mm2 = len(surface_voxels) * (voxel_size[0] * voxel_size[1])
        
        # Sphericity calculation
        sphericity = self._calculate_sphericity(total_volume_mm3, surface_area_mm2)
        
        # Compactness
        compactness = (surface_area_mm2 ** 3) / (36 * np.pi * total_volume_mm3 ** 2)
        
        # Bounding box
        coords = np.where(binary_volume)
        bbox_dims = [
            (np.max(coords[0]) - np.min(coords[0])) * voxel_size[0],
            (np.max(coords[1]) - np.min(coords[1])) * voxel_size[1],
            (np.max(coords[2]) - np.min(coords[2])) * voxel_size[2]
        ]
        
        # Extent (volume ratio to bounding box)
        bbox_volume = np.prod(bbox_dims)
        extent = total_volume_mm3 / bbox_volume if bbox_volume > 0 else 0
        
        self.volume_metrics = {
            'voxel_count': voxel_count,
            'tumor_volume_mm3': total_volume_mm3,
            'tumor_volume_ml': total_volume_ml,
            'surface_area_mm2': surface_area_mm2,
            'sphericity': sphericity,
            'compactness': compactness,
            'bounding_box_dims': bbox_dims,
            'extent': extent,
            'voxel_size': voxel_size
        }
        
        return self.volume_metrics
    
    def _find_surface_voxels(self, binary_volume):
        """Find voxels on the surface of the volume"""
        from scipy.ndimage import binary_erosion
        
        # Erode the volume by 1 voxel
        eroded = binary_erosion(binary_volume)
        
        # Surface voxels are those in original but not in eroded
        surface = binary_volume & ~eroded
        
        return np.where(surface)
    
    def _calculate_sphericity(self, volume, surface_area):
        """Calculate sphericity measure"""
        if surface_area == 0:
            return 0
        
        # Sphericity = (π^(1/3) * (6*Volume)^(2/3)) / Surface_Area
        numerator = np.pi ** (1/3) * (6 * volume) ** (2/3)
        sphericity = numerator / surface_area
        
        return min(sphericity, 1.0)  # Clamp to maximum of 1.0
    
    def generate_mesh_file(self, output_path='tumor_mesh.obj'):
        """Export mesh to OBJ file format"""
        if not self.mesh_data:
            print("No mesh data available. Run surface_rendering first.")
            return False
        
        vertices = self.mesh_data['vertices']
        faces = self.mesh_data['faces']
        
        try:
            # Create trimesh object
            mesh = trimesh.Trimesh(vertices=vertices, faces=faces)
            
            # Export to file
            mesh.export(output_path)
            
            print(f"Mesh exported to {output_path}")
            return True
            
        except Exception as e:
            print(f"Error exporting mesh: {e}")
            return False
    
    def visualize_3d_mesh(self, vertices, faces, title="3D Tumor Reconstruction"):
        """Visualize 3D mesh"""
        fig = plt.figure(figsize=(12, 10))
        ax = fig.add_subplot(111, projection='3d')
        
        # Create mesh collection
        mesh_collection = Poly3DCollection(vertices[faces], alpha=0.7, facecolor='red', edgecolor='darkred')
        ax.add_collection3d(mesh_collection)
        
        # Set axis limits
        ax.set_xlim(vertices[:, 0].min(), vertices[:, 0].max())
        ax.set_ylim(vertices[:, 1].min(), vertices[:, 1].max())
        ax.set_zlim(vertices[:, 2].min(), vertices[:, 2].max())
        
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_zlabel('Z')
        ax.set_title(title)
        
        return fig
    
    def comprehensive_reconstruction(self, volume_data, voxel_size=(1.0, 1.0, 1.0)):
        """Perform comprehensive 3D reconstruction"""
        results = {
            'success': True,
            'mesh_data': {},
            'volume_metrics': {},
            'orthogonal_views': {},
            'visualizations': {}
        }
        
        try:
            # Surface rendering
            print("Generating 3D surface...")
            vertices, faces, normals = self.surface_rendering(volume_data)
            
            if vertices is not None:
                results['mesh_data'] = self.mesh_data
                
                # Volume metrics
                print("Calculating volume metrics...")
                volume_metrics = self.calculate_volume_metrics(volume_data, voxel_size)
                results['volume_metrics'] = volume_metrics
                
                # Orthogonal views
                print("Creating orthogonal views...")
                ortho_views, ortho_fig = self.create_orthogonal_views(volume_data)
                results['orthogonal_views'] = ortho_views
                
                # 3D visualization
                print("Creating 3D visualization...")
                mesh_fig = self.visualize_3d_mesh(vertices, faces)
                
                results['visualizations'] = {
                    'orthogonal_figure': ortho_fig,
                    'mesh_figure': mesh_fig
                }
                
                print("3D reconstruction completed successfully!")
                
            else:
                results['success'] = False
                results['error'] = "Failed to generate 3D surface"
                
        except Exception as e:
            results['success'] = False
            results['error'] = str(e)
            print(f"Error in comprehensive reconstruction: {e}")
        
        return results

# Usage example
reconstructor = Reconstructor3D()
reconstruction_results = reconstructor.comprehensive_reconstruction(
    volume_data=tumor_volume_3d,
    voxel_size=(1.0, 1.0, 2.0)  # mm
)

# Display results
if reconstruction_results['success']:
    print("Volume:", reconstruction_results['volume_metrics']['tumor_volume_ml'], "mL")
    print("Surface Area:", reconstruction_results['volume_metrics']['surface_area_mm2'], "mm²")
    print("Sphericity:", reconstruction_results['volume_metrics']['sphericity'])
    
    # Show visualizations
    plt.show()`;

  const matlabCode = `classdef Reconstructor3D < handle
    properties
        meshData
        volumeMetrics
        orthogonalViews
    end
    
    methods
        function obj = Reconstructor3D()
            obj.meshData = struct();
            obj.volumeMetrics = struct();
            obj.orthogonalViews = struct();
        end
        
        function [vertices, faces] = surfaceRendering(obj, volumeData, threshold, smooth)
            % Generate 3D surface using isosurface
            if nargin < 3
                threshold = 0.5;
            end
            if nargin < 4
                smooth = true;
            end
            
            % Apply morphological closing to fill holes
            if smooth
                se = strel('sphere', 2);
                volumeData = imclose(volumeData, se);
            end
            
            try
                % Generate isosurface
                [faces, vertices] = isosurface(volumeData, threshold);
                
                % Calculate normals
                normals = isonormals(volumeData, vertices);
                
                % Store mesh data
                obj.meshData.vertices = vertices;
                obj.meshData.faces = faces;
                obj.meshData.normals = normals;
                obj.meshData.vertexCount = size(vertices, 1);
                obj.meshData.faceCount = size(faces, 1);
                
            catch ME
                fprintf('Error in surface rendering: %s\\n', ME.message);
                vertices = [];
                faces = [];
            end
        end
        
        function fig = volumeRendering(obj, volumeData, opacity)
            % Generate volume rendering
            if nargin < 3
                opacity = 0.1;
            end
            
            fig = figure('Position', [100, 100, 800, 600]);
            
            % Create volume rendering
            [x, y, z] = meshgrid(1:size(volumeData, 2), 1:size(volumeData, 1), 1:size(volumeData, 3));
            
            % Apply threshold
            mask = volumeData > 0.5;
            
            % Create 3D scatter plot
            scatter3(x(mask), y(mask), z(mask), 1, volumeData(mask), 'filled');
            alpha(opacity);
            
            xlabel('X');
            ylabel('Y');
            zlabel('Z');
            title('3D Volume Rendering');
            colormap('jet');
            colorbar;
        end
        
        function [orthogonalViews, fig] = createOrthogonalViews(obj, volumeData)
            % Generate orthogonal projections
            [depth, height, width] = size(volumeData);
            
            % Calculate projection centers
            centerZ = round(depth / 2);
            centerY = round(height / 2);
            centerX = round(width / 2);
            
            % Generate orthogonal slices
            orthogonalViews = struct();
            orthogonalViews.axial = squeeze(volumeData(centerZ, :, :));     % XY plane
            orthogonalViews.sagittal = squeeze(volumeData(:, :, centerX));  % YZ plane
            orthogonalViews.coronal = squeeze(volumeData(:, centerY, :));   % XZ plane
            
            % Create visualization
            fig = figure('Position', [100, 100, 1200, 400]);
            
            % Axial view
            subplot(1, 3, 1);
            imshow(orthogonalViews.axial, []);
            title('Axial View (XY)');
            
            % Sagittal view
            subplot(1, 3, 2);
            imshow(orthogonalViews.sagittal, []);
            title('Sagittal View (YZ)');
            
            % Coronal view
            subplot(1, 3, 3);
            imshow(orthogonalViews.coronal, []);
            title('Coronal View (XZ)');
            
            obj.orthogonalViews = orthogonalViews;
        end
        
        function volumeMetrics = calculateVolumeMetrics(obj, volumeData, voxelSize)
            % Calculate quantitative volume metrics
            if nargin < 3
                voxelSize = [1.0, 1.0, 1.0];
            end
            
            % Binary volume
            binaryVolume = volumeData > 0.5;
            
            % Basic volume metrics
            voxelCount = sum(binaryVolume(:));
            voxelVolume = prod(voxelSize);
            totalVolumeMm3 = voxelCount * voxelVolume;
            totalVolumeMl = totalVolumeMm3 / 1000.0;
            
            % Surface area calculation (approximation)
            surfaceVoxels = obj.findSurfaceVoxels(binaryVolume);
            surfaceAreaMm2 = length(surfaceVoxels) * (voxelSize(1) * voxelSize(2));
            
            % Sphericity calculation
            sphericity = obj.calculateSphericity(totalVolumeMm3, surfaceAreaMm2);
            
            % Compactness
            compactness = (surfaceAreaMm2^3) / (36 * pi * totalVolumeMm3^2);
            
            % Bounding box
            [r, c, s] = ind2sub(size(binaryVolume), find(binaryVolume));
            bboxDims = [
                (max(r) - min(r)) * voxelSize(1),
                (max(c) - min(c)) * voxelSize(2),
                (max(s) - min(s)) * voxelSize(3)
            ];
            
            % Extent
            bboxVolume = prod(bboxDims);
            extent = totalVolumeMm3 / bboxVolume;
            
            % Store metrics
            volumeMetrics = struct();
            volumeMetrics.voxelCount = voxelCount;
            volumeMetrics.tumorVolumeMm3 = totalVolumeMm3;
            volumeMetrics.tumorVolumeMl = totalVolumeMl;
            volumeMetrics.surfaceAreaMm2 = surfaceAreaMm2;
            volumeMetrics.sphericity = sphericity;
            volumeMetrics.compactness = compactness;
            volumeMetrics.boundingBoxDims = bboxDims;
            volumeMetrics.extent = extent;
            volumeMetrics.voxelSize = voxelSize;
            
            obj.volumeMetrics = volumeMetrics;
        end
        
        function surfaceVoxels = findSurfaceVoxels(obj, binaryVolume)
            % Find voxels on the surface of the volume
            se = strel('sphere', 1);
            eroded = imerode(binaryVolume, se);
            surface = binaryVolume & ~eroded;
            surfaceVoxels = find(surface);
        end
        
        function sphericity = calculateSphericity(obj, volume, surfaceArea)
            % Calculate sphericity measure
            if surfaceArea == 0
                sphericity = 0;
                return;
            end
            
            numerator = pi^(1/3) * (6 * volume)^(2/3);
            sphericity = numerator / surfaceArea;
            sphericity = min(sphericity, 1.0);
        end
        
        function success = generateMeshFile(obj, outputPath)
            % Export mesh to OBJ file format
            if nargin < 2
                outputPath = 'tumor_mesh.obj';
            end
            
            if isempty(obj.meshData)
                fprintf('No mesh data available. Run surfaceRendering first.\\n');
                success = false;
                return;
            end
            
            try
                vertices = obj.meshData.vertices;
                faces = obj.meshData.faces;
                
                % Write OBJ file
                fileID = fopen(outputPath, 'w');
                
                % Write vertices
                for i = 1:size(vertices, 1)
                    fprintf(fileID, 'v %f %f %f\\n', vertices(i, 1), vertices(i, 2), vertices(i, 3));
                end
                
                % Write faces
                for i = 1:size(faces, 1)
                    fprintf(fileID, 'f %d %d %d\\n', faces(i, 1), faces(i, 2), faces(i, 3));
                end
                
                fclose(fileID);
                fprintf('Mesh exported to %s\\n', outputPath);
                success = true;
                
            catch ME
                fprintf('Error exporting mesh: %s\\n', ME.message);
                success = false;
            end
        end
        
        function fig = visualize3DMesh(obj, vertices, faces, titleStr)
            % Visualize 3D mesh
            if nargin < 4
                titleStr = '3D Tumor Reconstruction';
            end
            
            fig = figure('Position', [100, 100, 800, 600]);
            
            % Create patch object
            patch('Vertices', vertices, 'Faces', faces, ...
                  'FaceColor', 'red', 'EdgeColor', 'none', ...
                  'FaceAlpha', 0.7);
            
            % Set lighting and view
            lighting gouraud;
            material dull;
            camlight;
            
            xlabel('X');
            ylabel('Y');
            zlabel('Z');
            title(titleStr);
            axis equal;
            grid on;
            view(3);
        end
        
        function results = comprehensiveReconstruction(obj, volumeData, voxelSize)
            % Perform comprehensive 3D reconstruction
            if nargin < 3
                voxelSize = [1.0, 1.0, 1.0];
            end
            
            results = struct();
            results.success = true;
            results.meshData = struct();
            results.volumeMetrics = struct();
            results.orthogonalViews = struct();
            results.visualizations = struct();
            
            try
                % Surface rendering
                fprintf('Generating 3D surface...\\n');
                [vertices, faces] = obj.surfaceRendering(volumeData);
                
                if ~isempty(vertices)
                    results.meshData = obj.meshData;
                    
                    % Volume metrics
                    fprintf('Calculating volume metrics...\\n');
                    volumeMetrics = obj.calculateVolumeMetrics(volumeData, voxelSize);
                    results.volumeMetrics = volumeMetrics;
                    
                    % Orthogonal views
                    fprintf('Creating orthogonal views...\\n');
                    [orthoViews, orthoFig] = obj.createOrthogonalViews(volumeData);
                    results.orthogonalViews = orthoViews;
                    
                    % 3D visualization
                    fprintf('Creating 3D visualization...\\n');
                    meshFig = obj.visualize3DMesh(vertices, faces);
                    
                    results.visualizations.orthogonalFigure = orthoFig;
                    results.visualizations.meshFigure = meshFig;
                    
                    fprintf('3D reconstruction completed successfully!\\n');
                else
                    results.success = false;
                    results.error = 'Failed to generate 3D surface';
                end
                
            catch ME
                results.success = false;
                results.error = ME.message;
                fprintf('Error in comprehensive reconstruction: %s\\n', ME.message);
            end
        end
    end
end

% Usage example
reconstructor = Reconstructor3D();
reconstructionResults = reconstructor.comprehensiveReconstruction(tumorVolume3D, [1.0, 1.0, 2.0]);

% Display results
if reconstructionResults.success
    fprintf('Volume: %.2f mL\\n', reconstructionResults.volumeMetrics.tumorVolumeMl);
    fprintf('Surface Area: %.2f mm²\\n', reconstructionResults.volumeMetrics.surfaceAreaMm2);
    fprintf('Sphericity: %.3f\\n', reconstructionResults.volumeMetrics.sphericity);
end`;

  const getReconstructionData = () => {
    if (analysisResults?.reconstruction_results) {
      return analysisResults.reconstruction_results;
    }
    return {
      mesh_data: {
        vertices: [],
        faces: [],
        vertex_count: 8742,
        face_count: 17484
      },
      volume_metrics: {
        tumor_volume_ml: 24.7,
        surface_area_mm2: 1247.3,
        sphericity: 0.742,
        compactness: 1.23,
        bounding_box_dims: [42.3, 38.7, 31.2],
        extent: 0.634
      },
      orthogonal_views: {
        axial: 'generated',
        sagittal: 'generated',
        coronal: 'generated'
      },
      success: true
    };
  };

  const reconstructionData = getReconstructionData();

  return (
    <div className="reconstruction-tab">
      <div className="detail-header">
        <i className="fas fa-cube detail-icon"></i>
        <div>
          <h3 className="detail-title">5. Advanced 3D Reconstruction</h3>
          <p className="detail-subtitle">
            Generate comprehensive 3D models with surface rendering and volumetric analysis
          </p>
        </div>
      </div>

      <div className="reconstruction-methods">
        <div className="methods-navigation">
          {reconstructionMethods.map((method) => (
            <button
              key={method.id}
              className={`method-btn ${activeView === method.id ? 'active' : ''}`}
              onClick={() => setActiveView(method.id)}
            >
              <div className="method-icon" style={{ background: method.color }}>
                <i className={method.icon}></i>
              </div>
              <div className="method-info">
                <h4>{method.title}</h4>
                <p>{method.description}</p>
              </div>
            </button>
          ))}
        </div>
      </div>

      <div className="reconstruction-content">
        {activeView === 'surface' && (
          <div className="surface-view">
            <div className="view-header">
              <h4>Surface Rendering - Marching Cubes Algorithm</h4>
              <p>Generate high-quality 3D surface meshes for tumor visualization</p>
            </div>
            <div className="surface-grid">
              <div className="surface-card">
                <div className="card-header">
                  <i className="fas fa-cube"></i>
                  <h5>3D Mesh Generation</h5>
                </div>
                <div className="surface-visualization">
                  <div className="viz-placeholder">
                    <i className="fas fa-cube"></i>
                    <span>3D Tumor Surface</span>
                  </div>
                </div>
                <div className="surface-info">
                  <div className="info-item">
                    <span>Vertices:</span>
                    <span>{reconstructionData.mesh_data.vertex_count?.toLocaleString() || 0}</span>
                  </div>
                  <div className="info-item">
                    <span>Faces:</span>
                    <span>{reconstructionData.mesh_data.face_count?.toLocaleString() || 0}</span>
                  </div>
                </div>
              </div>
              <div className="surface-card">
                <div className="card-header">
                  <i className="fas fa-cog"></i>
                  <h5>Algorithm Parameters</h5>
                </div>
                <div className="parameters-grid">
                  <div className="param-item">
                    <span className="param-label">Threshold:</span>
                    <span className="param-value">0.5</span>
                  </div>
                  <div className="param-item">
                    <span className="param-label">Smoothing:</span>
                    <span className="param-value">Enabled</span>
                  </div>
                  <div className="param-item">
                    <span className="param-label">Gradient:</span>
                    <span className="param-value">Descent</span>
                  </div>
                  <div className="param-item">
                    <span className="param-label">Spacing:</span>
                    <span className="param-value">1.0 × 1.0 × 1.0</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeView === 'volume' && (
          <div className="volume-view">
            <div className="view-header">
              <h4>Volume Rendering - Ray Casting</h4>
              <p>Interactive volume visualization with transparency and color mapping</p>
            </div>
            <div className="volume-grid">
              <div className="volume-card">
                <div className="card-header">
                  <i className="fas fa-layer-group"></i>
                  <h5>Volume Visualization</h5>
                </div>
                <div className="volume-visualization">
                  <div className="viz-placeholder">
                    <i className="fas fa-layer-group"></i>
                    <span>Volume Rendered View</span>
                  </div>
                </div>
              </div>
              <div className="volume-card">
                <div className="card-header">
                  <i className="fas fa-palette"></i>
                  <h5>Rendering Settings</h5>
                </div>
                <div className="settings-grid">
                  <div className="setting-item">
                    <span className="setting-label">Opacity:</span>
                    <div className="setting-control">
                      <input type="range" min="0" max="1" step="0.1" defaultValue="0.7" />
                      <span>0.7</span>
                    </div>
                  </div>
                  <div className="setting-item">
                    <span className="setting-label">Colormap:</span>
                    <select className="setting-select">
                      <option>Viridis</option>
                      <option>Hot</option>
                      <option>Cool</option>
                      <option>Jet</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeView === 'orthogonal' && (
          <div className="orthogonal-view">
            <div className="view-header">
              <h4>Orthogonal Views - Multi-Plane Reconstruction</h4>
              <p>Axial, sagittal, and coronal projections for comprehensive analysis</p>
            </div>
            <div className="orthogonal-grid">
              <div className="orthogonal-card">
                <div className="card-header">
                  <h5>Axial View (XY Plane)</h5>
                </div>
                <div className="orthogonal-visualization">
                  <div className="viz-placeholder">
                    <i className="fas fa-circle"></i>
                    <span>Axial Projection</span>
                  </div>
                </div>
              </div>
              <div className="orthogonal-card">
                <div className="card-header">
                  <h5>Sagittal View (YZ Plane)</h5>
                </div>
                <div className="orthogonal-visualization">
                  <div className="viz-placeholder">
                    <i className="fas fa-square"></i>
                    <span>Sagittal Projection</span>
                  </div>
                </div>
              </div>
              <div className="orthogonal-card">
                <div className="card-header">
                  <h5>Coronal View (XZ Plane)</h5>
                </div>
                <div className="orthogonal-visualization">
                  <div className="viz-placeholder">
                    <i className="fas fa-square"></i>
                    <span>Coronal Projection</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeView === 'metrics' && (
          <div className="metrics-view">
            <div className="view-header">
              <h4>Volume Metrics - Quantitative Analysis</h4>
              <p>Comprehensive measurements and geometric properties</p>
            </div>
            <div className="metrics-grid">
              <div className="metric-card">
                <div className="metric-icon">
                  <i className="fas fa-cube"></i>
                </div>
                <div className="metric-content">
                  <div className="metric-value">
                    {reconstructionData.volume_metrics?.tumor_volume_ml?.toFixed(1) || 0} mL
                  </div>
                  <div className="metric-label">Tumor Volume</div>
                </div>
              </div>
              <div className="metric-card">
                <div className="metric-icon">
                  <i className="fas fa-expand"></i>
                </div>
                <div className="metric-content">
                  <div className="metric-value">
                    {reconstructionData.volume_metrics?.surface_area_mm2?.toFixed(0) || 0} mm²
                  </div>
                  <div className="metric-label">Surface Area</div>
                </div>
              </div>
              <div className="metric-card">
                <div className="metric-icon">
                  <i className="fas fa-circle"></i>
                </div>
                <div className="metric-content">
                  <div className="metric-value">
                    {reconstructionData.volume_metrics?.sphericity?.toFixed(3) || 0}
                  </div>
                  <div className="metric-label">Sphericity</div>
                </div>
              </div>
              <div className="metric-card">
                <div className="metric-icon">
                  <i className="fas fa-compress"></i>
                </div>
                <div className="metric-content">
                  <div className="metric-value">
                    {reconstructionData.volume_metrics?.compactness?.toFixed(2) || 0}
                  </div>
                  <div className="metric-label">Compactness</div>
                </div>
              </div>
            </div>
            <div className="detailed-metrics">
              <div className="detail-card">
                <h5>Bounding Box Dimensions</h5>
                <div className="dimensions-grid">
                  <div className="dim-item">
                    <span>Width:</span>
                    <span>{reconstructionData.volume_metrics?.bounding_box_dims?.[0]?.toFixed(1) || 0} mm</span>
                  </div>
                  <div className="dim-item">
                    <span>Height:</span>
                    <span>{reconstructionData.volume_metrics?.bounding_box_dims?.[1]?.toFixed(1) || 0} mm</span>
                  </div>
                  <div className="dim-item">
                    <span>Depth:</span>
                    <span>{reconstructionData.volume_metrics?.bounding_box_dims?.[2]?.toFixed(1) || 0} mm</span>
                  </div>
                </div>
              </div>
              <div className="detail-card">
                <h5>Geometric Properties</h5>
                <div className="properties-grid">
                  <div className="prop-item">
                    <span>Extent:</span>
                    <span>{reconstructionData.volume_metrics?.extent?.toFixed(3) || 0}</span>
                  </div>
                  <div className="prop-item">
                    <span>Mesh Quality:</span>
                    <span>Good</span>
                  </div>
                  <div className="prop-item">
                    <span>Smoothness:</span>
                    <span>Enabled</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="code-section">
        <div className="code-header">
          <h4>3D Reconstruction Implementation</h4>
          <div className="code-tabs">
            <button 
              className={`code-tab ${codeLanguage === 'python' ? 'active' : ''}`}
              onClick={() => setCodeLanguage('python')}
            >
              Python
            </button>
            <button 
              className={`code-tab ${codeLanguage === 'matlab' ? 'active' : ''}`}
              onClick={() => setCodeLanguage('matlab')}
            >
              MATLAB
            </button>
          </div>
        </div>
        <CodeBlock 
          code={codeLanguage === 'python' ? pythonCode : matlabCode} 
          language={codeLanguage}
        />
      </div>

      <style jsx>{`
        .reconstruction-tab {
          padding: 20px 0;
        }

        .detail-header {
          display: flex;
          align-items: center;
          gap: 16px;
          margin-bottom: 40px;
          padding-bottom: 20px;
          border-bottom: 2px solid var(--border-color);
        }

        .detail-icon {
          font-size: 32px;
          color: var(--primary-color);
        }

        .detail-title {
          font-size: 24px;
          font-weight: 600;
          color: var(--text-primary);
          margin-bottom: 8px;
        }

        .detail-subtitle {
          color: var(--text-secondary);
          font-size: 16px;
        }

        .reconstruction-methods {
          margin-bottom: 40px;
        }

        .methods-navigation {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 16px;
        }

        .method-btn {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 20px;
          border: 2px solid var(--border-color);
          background: white;
          border-radius: var(--border-radius);
          cursor: pointer;
          transition: var(--transition);
          text-align: left;
        }

        .method-btn.active {
          border-color: var(--primary-color);
          background: rgba(102, 126, 234, 0.05);
        }

        .method-btn:hover:not(.active) {
          border-color: var(--primary-color);
        }

        .method-icon {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 20px;
        }

        .method-info h4 {
          margin: 0 0 4px 0;
          color: var(--text-primary);
        }

        .method-info p {
          margin: 0;
          color: var(--text-secondary);
          font-size: 14px;
        }

        .reconstruction-content {
          margin-bottom: 40px;
        }

        .view-header {
          margin-bottom: 30px;
          text-align: center;
        }

        .view-header h4 {
          margin: 0 0 8px 0;
          color: var(--text-primary);
        }

        .view-header p {
          margin: 0;
          color: var(--text-secondary);
        }

        .surface-grid {
          display: grid;
          grid-template-columns: 2fr 1fr;
          gap: 24px;
        }

        .surface-card {
          background: white;
          border-radius: var(--border-radius);
          padding: 24px;
          box-shadow: var(--shadow-light);
        }

        .card-header {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 20px;
        }

        .card-header i {
          color: var(--primary-color);
          font-size: 18px;
        }

        .card-header h5 {
          margin: 0;
          color: var(--text-primary);
        }

        .surface-visualization {
          margin-bottom: 20px;
        }

        .viz-placeholder {
          width: 100%;
          height: 250px;
          background: var(--background-dark);
          border-radius: var(--border-radius);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          border: 2px dashed var(--border-color);
        }

        .viz-placeholder i {
          font-size: 48px;
          color: var(--primary-color);
          margin-bottom: 12px;
        }

        .viz-placeholder span {
          color: var(--text-secondary);
          font-size: 16px;
        }

        .surface-info {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .info-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid var(--border-color);
        }

        .info-item:last-child {
          border-bottom: none;
        }

        .parameters-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 12px;
        }

        .param-item {
          display: flex;
          flex-direction: column;
          gap: 4px;
        }

        .param-label {
          color: var(--text-secondary);
          font-size: 12px;
        }

        .param-value {
          color: var(--text-primary);
          font-weight: 600;
        }

        .volume-grid {
          display: grid;
          grid-template-columns: 2fr 1fr;
          gap: 24px;
        }

        .volume-card {
          background: white;
          border-radius: var(--border-radius);
          padding: 24px;
          box-shadow: var(--shadow-light);
        }

        .volume-visualization {
          margin-bottom: 20px;
        }

        .settings-grid {
          display: flex;
          flex-direction: column;
          gap: 16px;
        }

        .setting-item {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .setting-label {
          color: var(--text-secondary);
          font-size: 14px;
        }

        .setting-control {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .setting-control input {
          flex: 1;
        }

        .setting-select {
          padding: 8px;
          border: 1px solid var(--border-color);
          border-radius: var(--border-radius);
        }

        .orthogonal-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 24px;
        }

        .orthogonal-card {
          background: white;
          border-radius: var(--border-radius);
          padding: 20px;
          box-shadow: var(--shadow-light);
        }

        .orthogonal-visualization {
          margin-top: 16px;
        }

        .orthogonal-visualization .viz-placeholder {
          height: 200px;
        }

        .metrics-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 20px;
          margin-bottom: 30px;
        }

        .metric-card {
          background: white;
          border-radius: var(--border-radius);
          padding: 20px;
          box-shadow: var(--shadow-light);
          display: flex;
          align-items: center;
          gap: 16px;
        }

        .metric-icon {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 18px;
        }

        .metric-value {
          font-size: 24px;
          font-weight: 700;
          color: var(--primary-color);
          margin-bottom: 4px;
        }

        .metric-label {
          color: var(--text-secondary);
          font-size: 12px;
          font-weight: 500;
        }

        .detailed-metrics {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 24px;
        }

        .detail-card {
          background: white;
          border-radius: var(--border-radius);
          padding: 24px;
          box-shadow: var(--shadow-light);
        }

        .detail-card h5 {
          margin: 0 0 16px 0;
          color: var(--text-primary);
        }

        .dimensions-grid,
        .properties-grid {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .dim-item,
        .prop-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid var(--border-color);
        }

        .dim-item:last-child,
        .prop-item:last-child {
          border-bottom: none;
        }

        .code-section {
          background: white;
          border-radius: var(--border-radius);
          overflow: hidden;
          box-shadow: var(--shadow-light);
        }

        .code-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20px;
          border-bottom: 1px solid var(--border-color);
        }

        .code-header h4 {
          margin: 0;
          color: var(--text-primary);
        }

        .code-tabs {
          display: flex;
          gap: 8px;
        }

        .code-tab {
          padding: 8px 16px;
          border: 1px solid var(--border-color);
          background: white;
          cursor: pointer;
          border-radius: var(--border-radius);
          font-size: 14px;
          transition: var(--transition);
        }

        .code-tab.active {
          background: var(--primary-color);
          color: white;
          border-color: var(--primary-color);
        }

        @media (max-width: 1024px) {
          .surface-grid,
          .volume-grid {
            grid-template-columns: 1fr;
          }
        }

        @media (max-width: 768px) {
          .methods-navigation {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </div>
  );
};

export default ReconstructionTab;