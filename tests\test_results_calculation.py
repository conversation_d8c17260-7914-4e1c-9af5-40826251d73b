"""
Test Suite for Results Calculation Module
========================================

Comprehensive tests for the results calculation and comparison components.

Author: Dr. <PERSON>
"""

import unittest
import numpy as np
import sys
import os
import tempfile
import shutil
from typing import List, Dict, Tuple

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from results_calculation import (
    SegmentationMetrics, VolumeCalculator, MethodComparator,
    ResultsVisualizer, ResultsReporter
)


class TestSegmentationMetrics(unittest.TestCase):
    """Test segmentation metrics calculation."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.metrics = SegmentationMetrics()
        
        # Create test data
        self.size = (100, 100)
        
        # Perfect overlap
        self.gt_perfect = np.zeros(self.size)
        self.gt_perfect[30:70, 30:70] = 1
        self.pred_perfect = self.gt_perfect.copy()
        
        # Partial overlap
        self.gt_partial = np.zeros(self.size)
        self.gt_partial[30:70, 30:70] = 1
        self.pred_partial = np.zeros(self.size)
        self.pred_partial[25:65, 35:75] = 1
        
        # No overlap
        self.gt_no_overlap = np.zeros(self.size)
        self.gt_no_overlap[20:40, 20:40] = 1
        self.pred_no_overlap = np.zeros(self.size)
        self.pred_no_overlap[60:80, 60:80] = 1
        
        # Empty masks
        self.gt_empty = np.zeros(self.size)
        self.pred_empty = np.zeros(self.size)
    
    def test_dice_coefficient_perfect(self):
        """Test Dice coefficient with perfect overlap."""
        dice = self.metrics.dice_coefficient(self.gt_perfect, self.pred_perfect)
        self.assertAlmostEqual(dice, 1.0, places=6)
    
    def test_dice_coefficient_partial(self):
        """Test Dice coefficient with partial overlap."""
        dice = self.metrics.dice_coefficient(self.gt_partial, self.pred_partial)
        self.assertGreater(dice, 0.0)
        self.assertLess(dice, 1.0)
    
    def test_dice_coefficient_no_overlap(self):
        """Test Dice coefficient with no overlap."""
        dice = self.metrics.dice_coefficient(self.gt_no_overlap, self.pred_no_overlap)
        self.assertAlmostEqual(dice, 0.0, places=6)
    
    def test_dice_coefficient_empty(self):
        """Test Dice coefficient with empty masks."""
        dice = self.metrics.dice_coefficient(self.gt_empty, self.pred_empty)
        # Should handle empty masks gracefully
        self.assertGreaterEqual(dice, 0.0)
        self.assertLessEqual(dice, 1.0)
    
    def test_jaccard_index_perfect(self):
        """Test Jaccard index with perfect overlap."""
        jaccard = self.metrics.jaccard_index(self.gt_perfect, self.pred_perfect)
        self.assertAlmostEqual(jaccard, 1.0, places=6)
    
    def test_jaccard_index_partial(self):
        """Test Jaccard index with partial overlap."""
        jaccard = self.metrics.jaccard_index(self.gt_partial, self.pred_partial)
        self.assertGreater(jaccard, 0.0)
        self.assertLess(jaccard, 1.0)
    
    def test_jaccard_index_no_overlap(self):
        """Test Jaccard index with no overlap."""
        jaccard = self.metrics.jaccard_index(self.gt_no_overlap, self.pred_no_overlap)
        self.assertAlmostEqual(jaccard, 0.0, places=6)
    
    def test_accuracy_perfect(self):
        """Test accuracy with perfect overlap."""
        accuracy = self.metrics.accuracy(self.gt_perfect, self.pred_perfect)
        self.assertAlmostEqual(accuracy, 1.0, places=6)
    
    def test_accuracy_partial(self):
        """Test accuracy with partial overlap."""
        accuracy = self.metrics.accuracy(self.gt_partial, self.pred_partial)
        self.assertGreater(accuracy, 0.5)  # Should be reasonably high
        self.assertLess(accuracy, 1.0)
    
    def test_precision_recall_perfect(self):
        """Test precision and recall with perfect overlap."""
        precision = self.metrics.precision(self.gt_perfect, self.pred_perfect)
        recall = self.metrics.recall(self.gt_perfect, self.pred_perfect)
        
        self.assertAlmostEqual(precision, 1.0, places=6)
        self.assertAlmostEqual(recall, 1.0, places=6)
    
    def test_precision_recall_partial(self):
        """Test precision and recall with partial overlap."""
        precision = self.metrics.precision(self.gt_partial, self.pred_partial)
        recall = self.metrics.recall(self.gt_partial, self.pred_partial)
        
        self.assertGreater(precision, 0.0)
        self.assertLess(precision, 1.0)
        self.assertGreater(recall, 0.0)
        self.assertLess(recall, 1.0)
    
    def test_specificity_perfect(self):
        """Test specificity with perfect overlap."""
        specificity = self.metrics.specificity(self.gt_perfect, self.pred_perfect)
        self.assertAlmostEqual(specificity, 1.0, places=6)
    
    def test_f1_score_perfect(self):
        """Test F1 score with perfect overlap."""
        f1 = self.metrics.f1_score(self.gt_perfect, self.pred_perfect)
        self.assertAlmostEqual(f1, 1.0, places=6)
    
    def test_f1_score_equals_dice(self):
        """Test that F1 score equals Dice coefficient."""
        f1 = self.metrics.f1_score(self.gt_partial, self.pred_partial)
        dice = self.metrics.dice_coefficient(self.gt_partial, self.pred_partial)
        self.assertAlmostEqual(f1, dice, places=6)
    
    def test_volume_similarity_perfect(self):
        """Test volume similarity with perfect overlap."""
        vol_sim = self.metrics.volume_similarity(self.gt_perfect, self.pred_perfect)
        self.assertAlmostEqual(vol_sim, 1.0, places=6)
    
    def test_volume_similarity_different_volumes(self):
        """Test volume similarity with different volumes."""
        # Create prediction with different volume
        pred_larger = np.zeros(self.size)
        pred_larger[20:80, 20:80] = 1  # Larger than ground truth
        
        vol_sim = self.metrics.volume_similarity(self.gt_partial, pred_larger)
        self.assertGreater(vol_sim, 0.0)
        self.assertLess(vol_sim, 1.0)
    
    def test_calculate_all_metrics(self):
        """Test calculation of all metrics."""
        metrics = self.metrics.calculate_all_metrics(
            self.gt_partial, self.pred_partial, include_distances=False)
        
        # Check that all expected metrics are present
        expected_metrics = [
            'dice_coefficient', 'jaccard_index', 'accuracy', 'precision',
            'recall', 'sensitivity', 'specificity', 'f1_score', 'volume_similarity'
        ]
        
        for metric in expected_metrics:
            self.assertIn(metric, metrics)
            self.assertIsInstance(metrics[metric], float)
            self.assertGreaterEqual(metrics[metric], 0.0)
    
    def test_metrics_consistency(self):
        """Test consistency between related metrics."""
        metrics = self.metrics.calculate_all_metrics(
            self.gt_partial, self.pred_partial, include_distances=False)
        
        # Dice should equal F1 score
        self.assertAlmostEqual(
            metrics['dice_coefficient'], metrics['f1_score'], places=6)
        
        # Recall should equal sensitivity
        self.assertAlmostEqual(
            metrics['recall'], metrics['sensitivity'], places=6)
    
    def test_invalid_input_handling(self):
        """Test handling of invalid inputs."""
        # Test with different shapes
        gt = np.zeros((50, 50))
        pred = np.zeros((100, 100))
        
        with self.assertRaises(Exception):
            self.metrics.dice_coefficient(gt, pred)
    
    def test_binary_conversion(self):
        """Test that inputs are properly converted to binary."""
        # Non-binary ground truth
        gt_float = np.random.rand(50, 50)
        pred_float = np.random.rand(50, 50)
        
        # Should not raise error and should work
        dice = self.metrics.dice_coefficient(gt_float, pred_float)
        self.assertIsInstance(dice, float)
        self.assertGreaterEqual(dice, 0.0)
        self.assertLessEqual(dice, 1.0)


class TestVolumeCalculator(unittest.TestCase):
    """Test volume calculation functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.calculator = VolumeCalculator()
        
        # Create 3D test volume
        self.volume_shape = (10, 50, 50)
        self.voxel_spacing = (2.0, 1.0, 1.0)  # z, y, x
        
        # Create spherical volume
        self.sphere_volume = self.create_sphere_volume(
            self.volume_shape, center=(5, 25, 25), radius=10)
        
        # Create cube volume
        self.cube_volume = np.zeros(self.volume_shape)
        self.cube_volume[2:8, 20:30, 20:30] = 1
    
    def create_sphere_volume(self, shape, center, radius):
        """Create a spherical volume."""
        volume = np.zeros(shape)
        z_c, y_c, x_c = center
        
        for z in range(shape[0]):
            for y in range(shape[1]):
                for x in range(shape[2]):
                    distance = np.sqrt((z - z_c)**2 + (y - y_c)**2 + (x - x_c)**2)
                    if distance <= radius:
                        volume[z, y, x] = 1
        
        return volume
    
    def test_calculate_volume_basic(self):
        """Test basic volume calculation."""
        volume_info = self.calculator.calculate_volume(
            self.cube_volume, self.voxel_spacing)
        
        # Check that volume info contains expected keys
        expected_keys = [
            'voxel_count', 'voxel_volume_mm3', 'total_volume_mm3',
            'total_volume_cm3', 'total_volume_ml', 'bounding_box_volume_mm3',
            'fill_ratio'
        ]
        
        for key in expected_keys:
            self.assertIn(key, volume_info)
        
        # Check voxel count
        expected_voxels = 6 * 10 * 10  # 6 slices * 10x10 pixels
        self.assertEqual(volume_info['voxel_count'], expected_voxels)
        
        # Check voxel volume
        expected_voxel_volume = 2.0 * 1.0 * 1.0  # 2 mm³
        self.assertEqual(volume_info['voxel_volume_mm3'], expected_voxel_volume)
        
        # Check total volume
        expected_total_volume = expected_voxels * expected_voxel_volume
        self.assertEqual(volume_info['total_volume_mm3'], expected_total_volume)
    
    def test_volume_unit_conversion(self):
        """Test volume unit conversion."""
        volume_info = self.calculator.calculate_volume(
            self.cube_volume, self.voxel_spacing)
        
        # Check unit conversions
        self.assertEqual(
            volume_info['total_volume_cm3'], 
            volume_info['total_volume_mm3'] / 1000)
        self.assertEqual(
            volume_info['total_volume_ml'], 
            volume_info['total_volume_cm3'])
    
    def test_compare_volumes(self):
        """Test volume comparison."""
        # Create slightly different volume
        modified_cube = self.cube_volume.copy()
        modified_cube[2:7, 20:30, 20:30] = 1  # Remove one slice
        
        comparison = self.calculator.compare_volumes(
            self.cube_volume, modified_cube, self.voxel_spacing)
        
        # Check that comparison contains expected keys
        expected_keys = [
            'true_volume_mm3', 'pred_volume_mm3', 'true_volume_ml',
            'pred_volume_ml', 'volume_difference_mm3', 'volume_difference_ml',
            'relative_difference', 'relative_error', 'absolute_error_mm3',
            'absolute_error_ml'
        ]
        
        for key in expected_keys:
            self.assertIn(key, comparison)
        
        # Check that predicted volume is smaller
        self.assertLess(comparison['pred_volume_mm3'], comparison['true_volume_mm3'])
        self.assertLess(comparison['volume_difference_mm3'], 0)
        self.assertGreater(comparison['relative_error'], 0)
    
    def test_volume_overlap(self):
        """Test volume overlap calculation."""
        # Create overlapping volume
        overlapping_cube = np.zeros(self.volume_shape)
        overlapping_cube[1:7, 25:35, 25:35] = 1  # Partially overlapping
        
        overlap = self.calculator.calculate_volume_overlap(
            self.cube_volume, overlapping_cube, self.voxel_spacing)
        
        # Check that overlap contains expected keys
        expected_keys = [
            'intersection_volume_mm3', 'union_volume_mm3', 'dice_volume',
            'jaccard_volume', 'overlap_fraction'
        ]
        
        for key in expected_keys:
            self.assertIn(key, overlap)
        
        # Check that metrics are in valid range
        self.assertGreaterEqual(overlap['dice_volume'], 0.0)
        self.assertLessEqual(overlap['dice_volume'], 1.0)
        self.assertGreaterEqual(overlap['jaccard_volume'], 0.0)
        self.assertLessEqual(overlap['jaccard_volume'], 1.0)
        self.assertGreaterEqual(overlap['overlap_fraction'], 0.0)
        self.assertLessEqual(overlap['overlap_fraction'], 1.0)
    
    def test_empty_volume(self):
        """Test handling of empty volumes."""
        empty_volume = np.zeros(self.volume_shape)
        
        volume_info = self.calculator.calculate_volume(
            empty_volume, self.voxel_spacing)
        
        # Should handle empty volume gracefully
        self.assertEqual(volume_info['voxel_count'], 0)
        self.assertEqual(volume_info['total_volume_mm3'], 0)
        self.assertEqual(volume_info['fill_ratio'], 0)
    
    def test_full_volume(self):
        """Test handling of full volumes."""
        full_volume = np.ones(self.volume_shape)
        
        volume_info = self.calculator.calculate_volume(
            full_volume, self.voxel_spacing)
        
        # Check that all voxels are counted
        expected_voxels = np.prod(self.volume_shape)
        self.assertEqual(volume_info['voxel_count'], expected_voxels)
        
        # Fill ratio should be 1.0
        self.assertEqual(volume_info['fill_ratio'], 1.0)
    
    def test_different_spacings(self):
        """Test volume calculation with different spacings."""
        spacing1 = (1.0, 1.0, 1.0)
        spacing2 = (2.0, 2.0, 2.0)
        
        volume1 = self.calculator.calculate_volume(self.cube_volume, spacing1)
        volume2 = self.calculator.calculate_volume(self.cube_volume, spacing2)
        
        # Volume should be 8 times larger with 2x spacing
        self.assertAlmostEqual(
            volume2['total_volume_mm3'], 
            volume1['total_volume_mm3'] * 8, 
            places=6)


class TestMethodComparator(unittest.TestCase):
    """Test method comparison functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.comparator = MethodComparator()
        
        # Create test data
        self.n_samples = 20
        self.image_size = (64, 64)
        
        # Generate synthetic data
        self.ground_truth, self.predictions = self.generate_test_data()
    
    def generate_test_data(self):
        """Generate synthetic test data."""
        ground_truth = []
        predictions = {
            'Method_A': [],
            'Method_B': [],
            'Method_C': []
        }
        
        np.random.seed(42)  # For reproducibility
        
        for i in range(self.n_samples):
            # Create ground truth
            gt = np.zeros(self.image_size)
            center = (32, 32)
            radius = 10 + np.random.normal(0, 2)
            
            y, x = np.ogrid[:self.image_size[0], :self.image_size[1]]
            mask = (x - center[0])**2 + (y - center[1])**2 <= radius**2
            gt[mask] = 1
            
            ground_truth.append(gt)
            
            # Method A: Good performance
            pred_a = gt.copy()
            noise = np.random.normal(0, 0.1, self.image_size)
            pred_a = (pred_a + noise > 0.4).astype(np.uint8)
            predictions['Method_A'].append(pred_a)
            
            # Method B: Moderate performance
            pred_b = gt.copy()
            noise = np.random.normal(0, 0.2, self.image_size)
            pred_b = (pred_b + noise > 0.3).astype(np.uint8)
            predictions['Method_B'].append(pred_b)
            
            # Method C: Poor performance
            pred_c = gt.copy()
            noise = np.random.normal(0, 0.3, self.image_size)
            pred_c = (pred_c + noise > 0.2).astype(np.uint8)
            predictions['Method_C'].append(pred_c)
        
        return ground_truth, predictions
    
    def test_add_method_results(self):
        """Test adding method results."""
        # Add method results
        self.comparator.add_method_results(
            'Method_A', self.ground_truth, self.predictions['Method_A'])
        
        # Check that results were added
        self.assertIn('Method_A', self.comparator.results)
        self.assertEqual(len(self.comparator.results['Method_A']['metrics']), self.n_samples)
        
        # Check that metrics are calculated
        metrics = self.comparator.results['Method_A']['metrics'][0]
        self.assertIn('dice_coefficient', metrics)
        self.assertIn('jaccard_index', metrics)
        self.assertIn('accuracy', metrics)
    
    def test_summary_statistics(self):
        """Test summary statistics calculation."""
        # Add method
        self.comparator.add_method_results(
            'Method_A', self.ground_truth, self.predictions['Method_A'])
        
        # Calculate summary statistics
        stats = self.comparator.calculate_summary_statistics('Method_A')
        
        # Check that statistics contain expected keys
        expected_keys = ['mean', 'std', 'min', 'max', 'median', 'q25', 'q75', 'count']
        
        for metric in ['dice_coefficient', 'jaccard_index', 'accuracy']:
            self.assertIn(metric, stats)
            for key in expected_keys:
                self.assertIn(key, stats[metric])
        
        # Check that count is correct
        self.assertEqual(stats['dice_coefficient']['count'], self.n_samples)
        
        # Check that statistics are reasonable
        self.assertGreaterEqual(stats['dice_coefficient']['mean'], 0.0)
        self.assertLessEqual(stats['dice_coefficient']['mean'], 1.0)
        self.assertGreaterEqual(stats['dice_coefficient']['std'], 0.0)
    
    def test_compare_methods(self):
        """Test method comparison."""
        # Add two methods
        self.comparator.add_method_results(
            'Method_A', self.ground_truth, self.predictions['Method_A'])
        self.comparator.add_method_results(
            'Method_B', self.ground_truth, self.predictions['Method_B'])
        
        # Compare methods
        comparison = self.comparator.compare_methods('Method_A', 'Method_B', 'dice_coefficient')
        
        # Check that comparison contains expected keys
        expected_keys = [
            'method1', 'method2', 'metric', 'method1_mean', 'method2_mean',
            'method1_std', 'method2_std', 'difference', 'paired_t_test',
            'wilcoxon_test', 'mann_whitney_test', 'cohens_d', 'effect_size'
        ]
        
        for key in expected_keys:
            self.assertIn(key, comparison)
        
        # Check that Method_A performs better than Method_B
        self.assertGreater(comparison['method1_mean'], comparison['method2_mean'])
        self.assertGreater(comparison['difference'], 0)
        
        # Check statistical test results
        self.assertIn('statistic', comparison['paired_t_test'])
        self.assertIn('p_value', comparison['paired_t_test'])
        self.assertIn('significant', comparison['paired_t_test'])
    
    def test_generate_comparison_report(self):
        """Test comprehensive comparison report generation."""
        # Add all methods
        for method_name, preds in self.predictions.items():
            self.comparator.add_method_results(method_name, self.ground_truth, preds)
        
        # Generate report
        report = self.comparator.generate_comparison_report()
        
        # Check that report contains expected sections
        expected_sections = [
            'methods', 'summary_statistics', 'pairwise_comparisons',
            'rankings', 'best_method'
        ]
        
        for section in expected_sections:
            self.assertIn(section, report)
        
        # Check methods list
        self.assertEqual(len(report['methods']), 3)
        
        # Check that rankings are provided
        self.assertIn('dice_coefficient', report['rankings'])
        rankings = report['rankings']['dice_coefficient']
        self.assertEqual(len(rankings), 3)
        
        # Check that best method is identified
        self.assertIn('method', report['best_method'])
        self.assertIn('average_rank', report['best_method'])
        
        # Method_A should be the best
        self.assertEqual(report['best_method']['method'], 'Method_A')
    
    def test_effect_size_interpretation(self):
        """Test effect size interpretation."""
        # Add methods with known differences
        self.comparator.add_method_results(
            'Method_A', self.ground_truth, self.predictions['Method_A'])
        self.comparator.add_method_results(
            'Method_C', self.ground_truth, self.predictions['Method_C'])  # Poor performance
        
        # Compare methods
        comparison = self.comparator.compare_methods('Method_A', 'Method_C', 'dice_coefficient')
        
        # Should have large effect size
        self.assertIn(comparison['effect_size'], ['small', 'medium', 'large'])
        
        # Cohen's d should be substantial
        self.assertGreater(abs(comparison['cohens_d']), 0.2)
    
    def test_empty_results(self):
        """Test behavior with empty results."""
        # Test with no methods added
        with self.assertRaises(Exception):
            self.comparator.generate_comparison_report()
        
        # Test with non-existent method
        with self.assertRaises(ValueError):
            self.comparator.calculate_summary_statistics('Non_existent')
    
    def test_single_method(self):
        """Test behavior with single method."""
        # Add only one method
        self.comparator.add_method_results(
            'Method_A', self.ground_truth, self.predictions['Method_A'])
        
        # Should be able to calculate summary statistics
        stats = self.comparator.calculate_summary_statistics('Method_A')
        self.assertIn('dice_coefficient', stats)
        
        # Should handle comparison report with single method
        report = self.comparator.generate_comparison_report()
        self.assertEqual(len(report['methods']), 1)
        self.assertEqual(report['best_method']['method'], 'Method_A')


class TestResultsReporter(unittest.TestCase):
    """Test results reporting functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.reporter = ResultsReporter()
        
        # Create test comparator with data
        self.comparator = MethodComparator()
        
        # Generate minimal test data
        n_samples = 10
        image_size = (32, 32)
        
        ground_truth = []
        predictions = {'Method_A': [], 'Method_B': []}
        
        for i in range(n_samples):
            gt = np.random.randint(0, 2, image_size)
            pred_a = np.random.randint(0, 2, image_size)
            pred_b = np.random.randint(0, 2, image_size)
            
            ground_truth.append(gt)
            predictions['Method_A'].append(pred_a)
            predictions['Method_B'].append(pred_b)
        
        # Add methods to comparator
        for method_name, preds in predictions.items():
            self.comparator.add_method_results(method_name, ground_truth, preds)
    
    def test_generate_text_report(self):
        """Test text report generation."""
        text_report = self.reporter.generate_text_report(self.comparator)
        
        # Check that report is a string
        self.assertIsInstance(text_report, str)
        
        # Check that report contains expected sections
        self.assertIn('SEGMENTATION RESULTS COMPARISON REPORT', text_report)
        self.assertIn('SUMMARY STATISTICS', text_report)
        self.assertIn('METHOD RANKINGS', text_report)
        self.assertIn('BEST METHOD OVERALL', text_report)
        
        # Check that methods are mentioned
        self.assertIn('Method_A', text_report)
        self.assertIn('Method_B', text_report)
    
    def test_generate_json_report(self):
        """Test JSON report generation."""
        json_report = self.reporter.generate_json_report(self.comparator)
        
        # Check that report is a dictionary
        self.assertIsInstance(json_report, dict)
        
        # Check that report contains expected sections
        expected_sections = [
            'methods', 'summary_statistics', 'pairwise_comparisons',
            'rankings', 'best_method', 'metadata'
        ]
        
        for section in expected_sections:
            self.assertIn(section, json_report)
        
        # Check metadata
        self.assertIn('generated_on', json_report['metadata'])
        self.assertIn('version', json_report['metadata'])
        self.assertIn('author', json_report['metadata'])
    
    def test_generate_csv_summary(self):
        """Test CSV summary generation."""
        csv_summary = self.reporter.generate_csv_summary(self.comparator)
        
        # Check that summary is a DataFrame
        import pandas as pd
        self.assertIsInstance(csv_summary, pd.DataFrame)
        
        # Check that DataFrame has expected columns
        self.assertIn('Method', csv_summary.columns)
        self.assertIn('dice_coefficient_mean', csv_summary.columns)
        self.assertIn('jaccard_index_mean', csv_summary.columns)
        
        # Check that DataFrame has correct number of rows
        self.assertEqual(len(csv_summary), 2)  # Two methods
    
    def test_save_reports(self):
        """Test saving reports to files."""
        # Create temporary directory
        temp_dir = tempfile.mkdtemp()
        
        try:
            # Test text report saving
            text_path = os.path.join(temp_dir, 'test_report.txt')
            text_report = self.reporter.generate_text_report(self.comparator, text_path)
            
            self.assertTrue(os.path.exists(text_path))
            
            # Test JSON report saving
            json_path = os.path.join(temp_dir, 'test_report.json')
            json_report = self.reporter.generate_json_report(self.comparator, json_path)
            
            self.assertTrue(os.path.exists(json_path))
            
            # Test CSV summary saving
            csv_path = os.path.join(temp_dir, 'test_summary.csv')
            csv_summary = self.reporter.generate_csv_summary(self.comparator, csv_path)
            
            self.assertTrue(os.path.exists(csv_path))
            
            # Check that files are not empty
            self.assertGreater(os.path.getsize(text_path), 0)
            self.assertGreater(os.path.getsize(json_path), 0)
            self.assertGreater(os.path.getsize(csv_path), 0)
            
        finally:
            # Clean up
            shutil.rmtree(temp_dir)


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete results calculation pipeline."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.n_samples = 15
        self.image_size = (64, 64)
        
        # Generate more realistic test data
        self.ground_truth, self.predictions = self.generate_realistic_data()
    
    def generate_realistic_data(self):
        """Generate more realistic test data."""
        ground_truth = []
        predictions = {
            'U-Net': [],
            'FCN': [],
            'Traditional': []
        }
        
        np.random.seed(42)
        
        for i in range(self.n_samples):
            # Create ground truth tumor
            gt = np.zeros(self.image_size)
            
            # Random tumor characteristics
            if i % 3 == 0:
                # Circular tumor
                center = (32 + np.random.randint(-10, 10), 32 + np.random.randint(-10, 10))
                radius = 8 + np.random.randint(-2, 5)
                
                y, x = np.ogrid[:self.image_size[0], :self.image_size[1]]
                mask = (x - center[0])**2 + (y - center[1])**2 <= radius**2
                gt[mask] = 1
            
            elif i % 3 == 1:
                # Elliptical tumor
                center = (32 + np.random.randint(-10, 10), 32 + np.random.randint(-10, 10))
                a = 12 + np.random.randint(-2, 4)
                b = 8 + np.random.randint(-2, 4)
                
                y, x = np.ogrid[:self.image_size[0], :self.image_size[1]]
                mask = ((x - center[0])**2 / a**2 + (y - center[1])**2 / b**2) <= 1
                gt[mask] = 1
            
            else:
                # Irregular tumor
                centers = [(28 + np.random.randint(-5, 5), 28 + np.random.randint(-5, 5)),
                          (36 + np.random.randint(-5, 5), 36 + np.random.randint(-5, 5))]
                
                y, x = np.ogrid[:self.image_size[0], :self.image_size[1]]
                for center in centers:
                    radius = 6 + np.random.randint(-2, 3)
                    mask = (x - center[0])**2 + (y - center[1])**2 <= radius**2
                    gt |= mask
            
            ground_truth.append(gt.astype(np.uint8))
            
            # Generate predictions with different characteristics
            
            # U-Net: High accuracy
            pred_unet = gt.copy().astype(float)
            noise = np.random.normal(0, 0.1, self.image_size)
            pred_unet = (pred_unet + noise > 0.4).astype(np.uint8)
            predictions['U-Net'].append(pred_unet)
            
            # FCN: Moderate accuracy
            pred_fcn = gt.copy().astype(float)
            noise = np.random.normal(0, 0.2, self.image_size)
            pred_fcn = (pred_fcn + noise > 0.3).astype(np.uint8)
            predictions['FCN'].append(pred_fcn)
            
            # Traditional: Lower accuracy
            pred_trad = gt.copy().astype(float)
            noise = np.random.normal(0, 0.3, self.image_size)
            pred_trad = (pred_trad + noise > 0.2).astype(np.uint8)
            predictions['Traditional'].append(pred_trad)
        
        return ground_truth, predictions
    
    def test_complete_pipeline(self):
        """Test complete evaluation pipeline."""
        # Initialize components
        comparator = MethodComparator()
        visualizer = ResultsVisualizer()
        reporter = ResultsReporter()
        
        # Add all methods
        for method_name, preds in self.predictions.items():
            comparator.add_method_results(
                method_name, self.ground_truth, preds, voxel_spacing=(1.0, 1.0, 1.0))
        
        # Generate comprehensive report
        report = comparator.generate_comparison_report()
        
        # Check that report is complete
        self.assertIn('methods', report)
        self.assertIn('summary_statistics', report)
        self.assertIn('best_method', report)
        
        # Check that U-Net is the best method
        self.assertEqual(report['best_method']['method'], 'U-Net')
        
        # Generate text report
        text_report = reporter.generate_text_report(comparator)
        self.assertIsInstance(text_report, str)
        self.assertGreater(len(text_report), 1000)  # Should be substantial
        
        # Generate JSON report
        json_report = reporter.generate_json_report(comparator)
        self.assertIsInstance(json_report, dict)
        
        # Generate CSV summary
        csv_summary = reporter.generate_csv_summary(comparator)
        self.assertEqual(len(csv_summary), 3)  # Three methods
    
    def test_statistical_significance(self):
        """Test statistical significance detection."""
        comparator = MethodComparator()
        
        # Add methods
        for method_name, preds in self.predictions.items():
            comparator.add_method_results(
                method_name, self.ground_truth, preds, voxel_spacing=(1.0, 1.0, 1.0))
        
        # Compare U-Net vs Traditional (should be significant)
        comparison = comparator.compare_methods('U-Net', 'Traditional', 'dice_coefficient')
        
        # Should show significant difference
        self.assertTrue(comparison['paired_t_test']['significant'])
        self.assertLess(comparison['paired_t_test']['p_value'], 0.05)
        
        # Should have substantial effect size
        self.assertIn(comparison['effect_size'], ['medium', 'large'])
    
    def test_volume_analysis(self):
        """Test volume analysis integration."""
        comparator = MethodComparator()
        
        # Add methods with volume analysis
        for method_name, preds in self.predictions.items():
            comparator.add_method_results(
                method_name, self.ground_truth, preds, voxel_spacing=(2.0, 1.0, 1.0))
        
        # Check that volume comparisons are calculated
        for method_name in self.predictions.keys():
            volume_comparisons = comparator.results[method_name]['volume_comparisons']
            self.assertEqual(len(volume_comparisons), self.n_samples)
            
            # Check that volume comparison contains expected metrics
            vol_comp = volume_comparisons[0]
            expected_keys = [
                'true_volume_mm3', 'pred_volume_mm3', 'volume_difference_mm3',
                'relative_error', 'absolute_error_mm3'
            ]
            
            for key in expected_keys:
                self.assertIn(key, vol_comp)
    
    def test_performance_consistency(self):
        """Test performance consistency across samples."""
        comparator = MethodComparator()
        
        # Add methods
        for method_name, preds in self.predictions.items():
            comparator.add_method_results(
                method_name, self.ground_truth, preds, voxel_spacing=(1.0, 1.0, 1.0))
        
        # Check consistency (coefficient of variation)
        for method_name in self.predictions.keys():
            stats = comparator.calculate_summary_statistics(method_name)
            dice_stats = stats['dice_coefficient']
            
            # Calculate coefficient of variation
            cv = dice_stats['std'] / dice_stats['mean'] if dice_stats['mean'] > 0 else 0
            
            # Should be reasonable (< 1.0)
            self.assertLess(cv, 1.0)
            
            # U-Net should be most consistent
            if method_name == 'U-Net':
                self.assertLess(cv, 0.5)  # Should be quite consistent


def run_all_tests():
    """Run all tests."""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestSegmentationMetrics,
        TestVolumeCalculator,
        TestMethodComparator,
        TestResultsReporter,
        TestIntegration
    ]
    
    for test_class in test_classes:
        test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(test_class))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result


if __name__ == '__main__':
    print("Running Results Calculation Module Tests")
    print("=" * 50)
    
    result = run_all_tests()
    
    print("\n" + "=" * 50)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, failure in result.failures:
            print(f"- {test}: {failure}")
    
    if result.errors:
        print("\nErrors:")
        for test, error in result.errors:
            print(f"- {test}: {error}")
    
    if result.wasSuccessful():
        print("\nAll tests passed successfully! ✅")
    else:
        print("\nSome tests failed. ❌")"""
Test Suite for Results Calculation Module
========================================

Comprehensive tests for the results calculation and comparison components.

Author: Dr. Mohammed Yagoub Esmail
"""

import unittest
import numpy as np
import sys
import os
import tempfile
import shutil
from typing import List, Dict, Tuple

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from results_calculation import (
    SegmentationMetrics, VolumeCalculator, MethodComparator,
    ResultsVisualizer, ResultsReporter
)


class TestSegmentationMetrics(unittest.TestCase):
    """Test segmentation metrics calculation."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.metrics = SegmentationMetrics()
        
        # Create test data
        self.size = (100, 100)
        
        # Perfect overlap
        self.gt_perfect = np.zeros(self.size)
        self.gt_perfect[30:70, 30:70] = 1
        self.pred_perfect = self.gt_perfect.copy()
        
        # Partial overlap
        self.gt_partial = np.zeros(self.size)
        self.gt_partial[30:70, 30:70] = 1
        self.pred_partial = np.zeros(self.size)
        self.pred_partial[25:65, 35:75] = 1
        
        # No overlap
        self.gt_no_overlap = np.zeros(self.size)
        self.gt_no_overlap[20:40, 20:40] = 1
        self.pred_no_overlap = np.zeros(self.size)
        self.pred_no_overlap[60:80, 60:80] = 1
        
        # Empty masks
        self.gt_empty = np.zeros(self.size)
        self.pred_empty = np.zeros(self.size)
    
    def test_dice_coefficient_perfect(self):
        """Test Dice coefficient with perfect overlap."""
        dice = self.metrics.dice_coefficient(self.gt_perfect, self.pred_perfect)
        self.assertAlmostEqual(dice, 1.0, places=6)
    
    def test_dice_coefficient_partial(self):
        """Test Dice coefficient with partial overlap."""
        dice = self.metrics.dice_coefficient(self.gt_partial, self.pred_partial)
        self.assertGreater(dice, 0.0)
        self.assertLess(dice, 1.0)
    
    def test_dice_coefficient_no_overlap(self):
        """Test Dice coefficient with no overlap."""
        dice = self.metrics.dice_coefficient(self.gt_no_overlap, self.pred_no_overlap)
        self.assertAlmostEqual(dice, 0.0, places=6)
    
    def test_dice_coefficient_empty(self):
        """Test Dice coefficient with empty masks."""
        dice = self.metrics.dice_coefficient(self.gt_empty, self.pred_empty)
        # Should handle empty masks gracefully
        self.assertGreaterEqual(dice, 0.0)
        self.assertLessEqual(dice, 1.0)
    
    def test_jaccard_index_perfect(self):
        """Test Jaccard index with perfect overlap."""
        jaccard = self.metrics.jaccard_index(self.gt_perfect, self.pred_perfect)
        self.assertAlmostEqual(jaccard, 1.0, places=6)
    
    def test_jaccard_index_partial(self):
        """Test Jaccard index with partial overlap."""
        jaccard = self.metrics.jaccard_index(self.gt_partial, self.pred_partial)
        self.assertGreater(jaccard, 0.0)
        self.assertLess(jaccard, 1.0)
    
    def test_jaccard_index_no_overlap(self):
        """Test Jaccard index with no overlap."""
        jaccard = self.metrics.jaccard_index(self.gt_no_overlap, self.pred_no_overlap)
        self.assertAlmostEqual(jaccard, 0.0, places=6)
    
    def test_accuracy_perfect(self):
        """Test accuracy with perfect overlap."""
        accuracy = self.metrics.accuracy(self.gt_perfect, self.pred_perfect)
        self.assertAlmostEqual(accuracy, 1.0, places=6)
    
    def test_accuracy_partial(self):
        """Test accuracy with partial overlap."""
        accuracy = self.metrics.accuracy(self.gt_partial, self.pred_partial)
        self.assertGreater(accuracy, 0.5)  # Should be reasonably high
        self.assertLess(accuracy, 1.0)
    
    def test_precision_recall_perfect(self):
        """Test precision and recall with perfect overlap."""
        precision = self.metrics.precision(self.gt_perfect, self.pred_perfect)
        recall = self.metrics.recall(self.gt_perfect, self.pred_perfect)
        
        self.assertAlmostEqual(precision, 1.0, places=6)
        self.assertAlmostEqual(recall, 1.0, places=6)
    
    def test_precision_recall_partial(self):
        """Test precision and recall with partial overlap."""
        precision = self.metrics.precision(self.gt_partial, self.pred_partial)
        recall = self.metrics.recall(self.gt_partial, self.pred_partial)
        
        self.assertGreater(precision, 0.0)
        self.assertLess(precision, 1.0)
        self.assertGreater(recall, 0.0)
        self.assertLess(recall, 1.0)
    
    def test_specificity_perfect(self):
        """Test specificity with perfect overlap."""
        specificity = self.metrics.specificity(self.gt_perfect, self.pred_perfect)
        self.assertAlmostEqual(specificity, 1.0, places=6)
    
    def test_f1_score_perfect(self):
        """Test F1 score with perfect overlap."""
        f1 = self.metrics.f1_score(self.gt_perfect, self.pred_perfect)
        self.assertAlmostEqual(f1, 1.0, places=6)
    
    def test_f1_score_equals_dice(self):
        """Test that F1 score equals Dice coefficient."""
        f1 = self.metrics.f1_score(self.gt_partial, self.pred_partial)
        dice = self.metrics.dice_coefficient(self.gt_partial, self.pred_partial)
        self.assertAlmostEqual(f1, dice, places=6)
    
    def test_volume_similarity_perfect(self):
        """Test volume similarity with perfect overlap."""
        vol_sim = self.metrics.volume_similarity(self.gt_perfect, self.pred_perfect)
        self.assertAlmostEqual(vol_sim, 1.0, places=6)
    
    def test_volume_similarity_different_volumes(self):
        """Test volume similarity with different volumes."""
        # Create prediction with different volume
        pred_larger = np.zeros(self.size)
        pred_larger[20:80, 20:80] = 1  # Larger than ground truth
        
        vol_sim = self.metrics.volume_similarity(self.gt_partial, pred_larger)
        self.assertGreater(vol_sim, 0.0)
        self.assertLess(vol_sim, 1.0)
    
    def test_calculate_all_metrics(self):
        """Test calculation of all metrics."""
        metrics = self.metrics.calculate_all_metrics(
            self.gt_partial, self.pred_partial, include_distances=False)
        
        # Check that all expected metrics are present
        expected_metrics = [
            'dice_coefficient', 'jaccard_index', 'accuracy', 'precision',
            'recall', 'sensitivity', 'specificity', 'f1_score', 'volume_similarity'
        ]
        
        for metric in expected_metrics:
            self.assertIn(metric, metrics)
            self.assertIsInstance(metrics[metric], float)
            self.assertGreaterEqual(metrics[metric], 0.0)
    
    def test_metrics_consistency(self):
        """Test consistency between related metrics."""
        metrics = self.metrics.calculate_all_metrics(
            self.gt_partial, self.pred_partial, include_distances=False)
        
        # Dice should equal F1 score
        self.assertAlmostEqual(
            metrics['dice_coefficient'], metrics['f1_score'], places=6)
        
        # Recall should equal sensitivity
        self.assertAlmostEqual(
            metrics['recall'], metrics['sensitivity'], places=6)
    
    def test_invalid_input_handling(self):
        """Test handling of invalid inputs."""
        # Test with different shapes
        gt = np.zeros((50, 50))
        pred = np.zeros((100, 100))
        
        with self.assertRaises(Exception):
            self.metrics.dice_coefficient(gt, pred)
    
    def test_binary_conversion(self):
        """Test that inputs are properly converted to binary."""
        # Non-binary ground truth
        gt_float = np.random.rand(50, 50)
        pred_float = np.random.rand(50, 50)
        
        # Should not raise error and should work
        dice = self.metrics.dice_coefficient(gt_float, pred_float)
        self.assertIsInstance(dice, float)
        self.assertGreaterEqual(dice, 0.0)
        self.assertLessEqual(dice, 1.0)


class TestVolumeCalculator(unittest.TestCase):
    """Test volume calculation functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.calculator = VolumeCalculator()
        
        # Create 3D test volume
        self.volume_shape = (10, 50, 50)
        self.voxel_spacing = (2.0, 1.0, 1.0)  # z, y, x
        
        # Create spherical volume
        self.sphere_volume = self.create_sphere_volume(
            self.volume_shape, center=(5, 25, 25), radius=10)
        
        # Create cube volume
        self.cube_volume = np.zeros(self.volume_shape)
        self.cube_volume[2:8, 20:30, 20:30] = 1
    
    def create_sphere_volume(self, shape, center, radius):
        """Create a spherical volume."""
        volume = np.zeros(shape)
        z_c, y_c, x_c = center
        
        for z in range(shape[0]):
            for y in range(shape[1]):
                for x in range(shape[2]):
                    distance = np.sqrt((z - z_c)**2 + (y - y_c)**2 + (x - x_c)**2)
                    if distance <= radius:
                        volume[z, y, x] = 1
        
        return volume
    
    def test_calculate_volume_basic(self):
        """Test basic volume calculation."""
        volume_info = self.calculator.calculate_volume(
            self.cube_volume, self.voxel_spacing)
        
        # Check that volume info contains expected keys
        expected_keys = [
            'voxel_count', 'voxel_volume_mm3', 'total_volume_mm3',
            'total_volume_cm3', 'total_volume_ml', 'bounding_box_volume_mm3',
            'fill_ratio'
        ]
        
        for key in expected_keys:
            self.assertIn(key, volume_info)
        
        # Check voxel count
        expected_voxels = 6 * 10 * 10  # 6 slices * 10x10 pixels
        self.assertEqual(volume_info['voxel_count'], expected_voxels)
        
        # Check voxel volume
        expected_voxel_volume = 2.0 * 1.0 * 1.0  # 2 mm³
        self.assertEqual(volume_info['voxel_volume_mm3'], expected_voxel_volume)
        
        # Check total volume
        expected_total_volume = expected_voxels * expected_voxel_volume
        self.assertEqual(volume_info['total_volume_mm3'], expected_total_volume)
    
    def test_volume_unit_conversion(self):
        """Test volume unit conversion."""
        volume_info = self.calculator.calculate_volume(
            self.cube_volume, self.voxel_spacing)
        
        # Check unit conversions
        self.assertEqual(
            volume_info['total_volume_cm3'], 
            volume_info['total_volume_mm3'] / 1000)
        self.assertEqual(
            volume_info['total_volume_ml'], 
            volume_info['total_volume_cm3'])
    
    def test_compare_volumes(self):
        """Test volume comparison."""
        # Create slightly different volume
        modified_cube = self.cube_volume.copy()
        modified_cube[2:7, 20:30, 20:30] = 1  # Remove one slice
        
        comparison = self.calculator.compare_volumes(
            self.cube_volume, modified_cube, self.voxel_spacing)
        
        # Check that comparison contains expected keys
        expected_keys = [
            'true_volume_mm3', 'pred_volume_mm3', 'true_volume_ml',
            'pred_volume_ml', 'volume_difference_mm3', 'volume_difference_ml',
            'relative_difference', 'relative_error', 'absolute_error_mm3',
            'absolute_error_ml'
        ]
        
        for key in expected_keys:
            self.assertIn(key, comparison)
        
        # Check that predicted volume is smaller
        self.assertLess(comparison['pred_volume_mm3'], comparison['true_volume_mm3'])
        self.assertLess(comparison['volume_difference_mm3'], 0)
        self.assertGreater(comparison['relative_error'], 0)
    
    def test_volume_overlap(self):
        """Test volume overlap calculation."""
        # Create overlapping volume
        overlapping_cube = np.zeros(self.volume_shape)
        overlapping_cube[1:7, 25:35, 25:35] = 1  # Partially overlapping
        
        overlap = self.calculator.calculate_volume_overlap(
            self.cube_volume, overlapping_cube, self.voxel_spacing)
        
        # Check that overlap contains expected keys
        expected_keys = [
            'intersection_volume_mm3', 'union_volume_mm3', 'dice_volume',
            'jaccard_volume', 'overlap_fraction'
        ]
        
        for key in expected_keys:
            self.assertIn(key, overlap)
        
        # Check that metrics are in valid range
        self.assertGreaterEqual(overlap['dice_volume'], 0.0)
        self.assertLessEqual(overlap['dice_volume'], 1.0)
        self.assertGreaterEqual(overlap['jaccard_volume'], 0.0)
        self.assertLessEqual(overlap['jaccard_volume'], 1.0)
        self.assertGreaterEqual(overlap['overlap_fraction'], 0.0)
        self.assertLessEqual(overlap['overlap_fraction'], 1.0)
    
    def test_empty_volume(self):
        """Test handling of empty volumes."""
        empty_volume = np.zeros(self.volume_shape)
        
        volume_info = self.calculator.calculate_volume(
            empty_volume, self.voxel_spacing)
        
        # Should handle empty volume gracefully
        self.assertEqual(volume_info['voxel_count'], 0)
        self.assertEqual(volume_info['total_volume_mm3'], 0)
        self.assertEqual(volume_info['fill_ratio'], 0)
    
    def test_full_volume(self):
        """Test handling of full volumes."""
        full_volume = np.ones(self.volume_shape)
        
        volume_info = self.calculator.calculate_volume(
            full_volume, self.voxel_spacing)
        
        # Check that all voxels are counted
        expected_voxels = np.prod(self.volume_shape)
        self.assertEqual(volume_info['voxel_count'], expected_voxels)
        
        # Fill ratio should be 1.0
        self.assertEqual(volume_info['fill_ratio'], 1.0)
    
    def test_different_spacings(self):
        """Test volume calculation with different spacings."""
        spacing1 = (1.0, 1.0, 1.0)
        spacing2 = (2.0, 2.0, 2.0)
        
        volume1 = self.calculator.calculate_volume(self.cube_volume, spacing1)
        volume2 = self.calculator.calculate_volume(self.cube_volume, spacing2)
        
        # Volume should be 8 times larger with 2x spacing
        self.assertAlmostEqual(
            volume2['total_volume_mm3'], 
            volume1['total_volume_mm3'] * 8, 
            places=6)


class TestMethodComparator(unittest.TestCase):
    """Test method comparison functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.comparator = MethodComparator()
        
        # Create test data
        self.n_samples = 20
        self.image_size = (64, 64)
        
        # Generate synthetic data
        self.ground_truth, self.predictions = self.generate_test_data()
    
    def generate_test_data(self):
        """Generate synthetic test data."""
        ground_truth = []
        predictions = {
            'Method_A': [],
            'Method_B': [],
            'Method_C': []
        }
        
        np.random.seed(42)  # For reproducibility
        
        for i in range(self.n_samples):
            # Create ground truth
            gt = np.zeros(self.image_size)
            center = (32, 32)
            radius = 10 + np.random.normal(0, 2)
            
            y, x = np.ogrid[:self.image_size[0], :self.image_size[1]]
            mask = (x - center[0])**2 + (y - center[1])**2 <= radius**2
            gt[mask] = 1
            
            ground_truth.append(gt)
            
            # Method A: Good performance
            pred_a = gt.copy()
            noise = np.random.normal(0, 0.1, self.image_size)
            pred_a = (pred_a + noise > 0.4).astype(np.uint8)
            predictions['Method_A'].append(pred_a)
            
            # Method B: Moderate performance
            pred_b = gt.copy()
            noise = np.random.normal(0, 0.2, self.image_size)
            pred_b = (pred_b + noise > 0.3).astype(np.uint8)
            predictions['Method_B'].append(pred_b)
            
            # Method C: Poor performance
            pred_c = gt.copy()
            noise = np.random.normal(0, 0.3, self.image_size)
            pred_c = (pred_c + noise > 0.2).astype(np.uint8)
            predictions['Method_C'].append(pred_c)
        
        return ground_truth, predictions
    
    def test_add_method_results(self):
        """Test adding method results."""
        # Add method results
        self.comparator.add_method_results(
            'Method_A', self.ground_truth, self.predictions['Method_A'])
        
        # Check that results were added
        self.assertIn('Method_A', self.comparator.results)
        self.assertEqual(len(self.comparator.results['Method_A']['metrics']), self.n_samples)
        
        # Check that metrics are calculated
        metrics = self.comparator.results['Method_A']['metrics'][0]
        self.assertIn('dice_coefficient', metrics)
        self.assertIn('jaccard_index', metrics)
        self.assertIn('accuracy', metrics)
    
    def test_summary_statistics(self):
        """Test summary statistics calculation."""
        # Add method
        self.comparator.add_method_results(
            'Method_A', self.ground_truth, self.predictions['Method_A'])
        
        # Calculate summary statistics
        stats = self.comparator.calculate_summary_statistics('Method_A')
        
        # Check that statistics contain expected keys
        expected_keys = ['mean', 'std', 'min', 'max', 'median', 'q25', 'q75', 'count']
        
        for metric in ['dice_coefficient', 'jaccard_index', 'accuracy']:
            self.assertIn(metric, stats)
            for key in expected_keys:
                self.assertIn(key, stats[metric])
        
        # Check that count is correct
        self.assertEqual(stats['dice_coefficient']['count'], self.n_samples)
        
        # Check that statistics are reasonable
        self.assertGreaterEqual(stats['dice_coefficient']['mean'], 0.0)
        self.assertLessEqual(stats['dice_coefficient']['mean'], 1.0)
        self.assertGreaterEqual(stats['dice_coefficient']['std'], 0.0)
    
    def test_compare_methods(self):
        """Test method comparison."""
        # Add two methods
        self.comparator.add_method_results(
            'Method_A', self.ground_truth, self.predictions['Method_A'])
        self.comparator.add_method_results(
            'Method_B', self.ground_truth, self.predictions['Method_B'])
        
        # Compare methods
        comparison = self.comparator.compare_methods('Method_A', 'Method_B', 'dice_coefficient')
        
        # Check that comparison contains expected keys
        expected_keys = [
            'method1', 'method2', 'metric', 'method1_mean', 'method2_mean',
            'method1_std', 'method2_std', 'difference', 'paired_t_test',
            'wilcoxon_test', 'mann_whitney_test', 'cohens_d', 'effect_size'
        ]
        
        for key in expected_keys:
            self.assertIn(key, comparison)
        
        # Check that Method_A performs better than Method_B
        self.assertGreater(comparison['method1_mean'], comparison['method2_mean'])
        self.assertGreater(comparison['difference'], 0)
        
        # Check statistical test results
        self.assertIn('statistic', comparison['paired_t_test'])
        self.assertIn('p_value', comparison['paired_t_test'])
        self.assertIn('significant', comparison['paired_t_test'])
    
    def test_generate_comparison_report(self):
        """Test comprehensive comparison report generation."""
        # Add all methods
        for method_name, preds in self.predictions.items():
            self.comparator.add_method_results(method_name, self.ground_truth, preds)
        
        # Generate report
        report = self.comparator.generate_comparison_report()
        
        # Check that report contains expected sections
        expected_sections = [
            'methods', 'summary_statistics', 'pairwise_comparisons',
            'rankings', 'best_method'
        ]
        
        for section in expected_sections:
            self.assertIn(section, report)
        
        # Check methods list
        self.assertEqual(len(report['methods']), 3)
        
        # Check that rankings are provided
        self.assertIn('dice_coefficient', report['rankings'])
        rankings = report['rankings']['dice_coefficient']
        self.assertEqual(len(rankings), 3)
        
        # Check that best method is identified
        self.assertIn('method', report['best_method'])
        self.assertIn('average_rank', report['best_method'])
        
        # Method_A should be the best
        self.assertEqual(report['best_method']['method'], 'Method_A')
    
    def test_effect_size_interpretation(self):
        """Test effect size interpretation."""
        # Add methods with known differences
        self.comparator.add_method_results(
            'Method_A', self.ground_truth, self.predictions['Method_A'])
        self.comparator.add_method_results(
            'Method_C', self.ground_truth, self.predictions['Method_C'])  # Poor performance
        
        # Compare methods
        comparison = self.comparator.compare_methods('Method_A', 'Method_C', 'dice_coefficient')
        
        # Should have large effect size
        self.assertIn(comparison['effect_size'], ['small', 'medium', 'large'])
        
        # Cohen's d should be substantial
        self.assertGreater(abs(comparison['cohens_d']), 0.2)
    
    def test_empty_results(self):
        """Test behavior with empty results."""
        # Test with no methods added
        with self.assertRaises(Exception):
            self.comparator.generate_comparison_report()
        
        # Test with non-existent method
        with self.assertRaises(ValueError):
            self.comparator.calculate_summary_statistics('Non_existent')
    
    def test_single_method(self):
        """Test behavior with single method."""
        # Add only one method
        self.comparator.add_method_results(
            'Method_A', self.ground_truth, self.predictions['Method_A'])
        
        # Should be able to calculate summary statistics
        stats = self.comparator.calculate_summary_statistics('Method_A')
        self.assertIn('dice_coefficient', stats)
        
        # Should handle comparison report with single method
        report = self.comparator.generate_comparison_report()
        self.assertEqual(len(report['methods']), 1)
        self.assertEqual(report['best_method']['method'], 'Method_A')


class TestResultsReporter(unittest.TestCase):
    """Test results reporting functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.reporter = ResultsReporter()
        
        # Create test comparator with data
        self.comparator = MethodComparator()
        
        # Generate minimal test data
        n_samples = 10
        image_size = (32, 32)
        
        ground_truth = []
        predictions = {'Method_A': [], 'Method_B': []}
        
        for i in range(n_samples):
            gt = np.random.randint(0, 2, image_size)
            pred_a = np.random.randint(0, 2, image_size)
            pred_b = np.random.randint(0, 2, image_size)
            
            ground_truth.append(gt)
            predictions['Method_A'].append(pred_a)
            predictions['Method_B'].append(pred_b)
        
        # Add methods to comparator
        for method_name, preds in predictions.items():
            self.comparator.add_method_results(method_name, ground_truth, preds)
    
    def test_generate_text_report(self):
        """Test text report generation."""
        text_report = self.reporter.generate_text_report(self.comparator)
        
        # Check that report is a string
        self.assertIsInstance(text_report, str)
        
        # Check that report contains expected sections
        self.assertIn('SEGMENTATION RESULTS COMPARISON REPORT', text_report)
        self.assertIn('SUMMARY STATISTICS', text_report)
        self.assertIn('METHOD RANKINGS', text_report)
        self.assertIn('BEST METHOD OVERALL', text_report)
        
        # Check that methods are mentioned
        self.assertIn('Method_A', text_report)
        self.assertIn('Method_B', text_report)
    
    def test_generate_json_report(self):
        """Test JSON report generation."""
        json_report = self.reporter.generate_json_report(self.comparator)
        
        # Check that report is a dictionary
        self.assertIsInstance(json_report, dict)
        
        # Check that report contains expected sections
        expected_sections = [
            'methods', 'summary_statistics', 'pairwise_comparisons',
            'rankings', 'best_method', 'metadata'
        ]
        
        for section in expected_sections:
            self.assertIn(section, json_report)
        
        # Check metadata
        self.assertIn('generated_on', json_report['metadata'])
        self.assertIn('version', json_report['metadata'])
        self.assertIn('author', json_report['metadata'])
    
    def test_generate_csv_summary(self):
        """Test CSV summary generation."""
        csv_summary = self.reporter.generate_csv_summary(self.comparator)
        
        # Check that summary is a DataFrame
        import pandas as pd
        self.assertIsInstance(csv_summary, pd.DataFrame)
        
        # Check that DataFrame has expected columns
        self.assertIn('Method', csv_summary.columns)
        self.assertIn('dice_coefficient_mean', csv_summary.columns)
        self.assertIn('jaccard_index_mean', csv_summary.columns)
        
        # Check that DataFrame has correct number of rows
        self.assertEqual(len(csv_summary), 2)  # Two methods
    
    def test_save_reports(self):
        """Test saving reports to files."""
        # Create temporary directory
        temp_dir = tempfile.mkdtemp()
        
        try:
            # Test text report saving
            text_path = os.path.join(temp_dir, 'test_report.txt')
            text_report = self.reporter.generate_text_report(self.comparator, text_path)
            
            self.assertTrue(os.path.exists(text_path))
            
            # Test JSON report saving
            json_path = os.path.join(temp_dir, 'test_report.json')
            json_report = self.reporter.generate_json_report(self.comparator, json_path)
            
            self.assertTrue(os.path.exists(json_path))
            
            # Test CSV summary saving
            csv_path = os.path.join(temp_dir, 'test_summary.csv')
            csv_summary = self.reporter.generate_csv_summary(self.comparator, csv_path)
            
            self.assertTrue(os.path.exists(csv_path))
            
            # Check that files are not empty
            self.assertGreater(os.path.getsize(text_path), 0)
            self.assertGreater(os.path.getsize(json_path), 0)
            self.assertGreater(os.path.getsize(csv_path), 0)
            
        finally:
            # Clean up
            shutil.rmtree(temp_dir)


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete results calculation pipeline."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.n_samples = 15
        self.image_size = (64, 64)
        
        # Generate more realistic test data
        self.ground_truth, self.predictions = self.generate_realistic_data()
    
    def generate_realistic_data(self):
        """Generate more realistic test data."""
        ground_truth = []
        predictions = {
            'U-Net': [],
            'FCN': [],
            'Traditional': []
        }
        
        np.random.seed(42)
        
        for i in range(self.n_samples):
            # Create ground truth tumor
            gt = np.zeros(self.image_size)
            
            # Random tumor characteristics
            if i % 3 == 0:
                # Circular tumor
                center = (32 + np.random.randint(-10, 10), 32 + np.random.randint(-10, 10))
                radius = 8 + np.random.randint(-2, 5)
                
                y, x = np.ogrid[:self.image_size[0], :self.image_size[1]]
                mask = (x - center[0])**2 + (y - center[1])**2 <= radius**2
                gt[mask] = 1
            
            elif i % 3 == 1:
                # Elliptical tumor
                center = (32 + np.random.randint(-10, 10), 32 + np.random.randint(-10, 10))
                a = 12 + np.random.randint(-2, 4)
                b = 8 + np.random.randint(-2, 4)
                
                y, x = np.ogrid[:self.image_size[0], :self.image_size[1]]
                mask = ((x - center[0])**2 / a**2 + (y - center[1])**2 / b**2) <= 1
                gt[mask] = 1
            
            else:
                # Irregular tumor
                centers = [(28 + np.random.randint(-5, 5), 28 + np.random.randint(-5, 5)),
                          (36 + np.random.randint(-5, 5), 36 + np.random.randint(-5, 5))]
                
                y, x = np.ogrid[:self.image_size[0], :self.image_size[1]]
                for center in centers:
                    radius = 6 + np.random.randint(-2, 3)
                    mask = (x - center[0])**2 + (y - center[1])**2 <= radius**2
                    gt |= mask
            
            ground_truth.append(gt.astype(np.uint8))
            
            # Generate predictions with different characteristics
            
            # U-Net: High accuracy
            pred_unet = gt.copy().astype(float)
            noise = np.random.normal(0, 0.1, self.image_size)
            pred_unet = (pred_unet + noise > 0.4).astype(np.uint8)
            predictions['U-Net'].append(pred_unet)
            
            # FCN: Moderate accuracy
            pred_fcn = gt.copy().astype(float)
            noise = np.random.normal(0, 0.2, self.image_size)
            pred_fcn = (pred_fcn + noise > 0.3).astype(np.uint8)
            predictions['FCN'].append(pred_fcn)
            
            # Traditional: Lower accuracy
            pred_trad = gt.copy().astype(float)
            noise = np.random.normal(0, 0.3, self.image_size)
            pred_trad = (pred_trad + noise > 0.2).astype(np.uint8)
            predictions['Traditional'].append(pred_trad)
        
        return ground_truth, predictions
    
    def test_complete_pipeline(self):
        """Test complete evaluation pipeline."""
        # Initialize components
        comparator = MethodComparator()
        visualizer = ResultsVisualizer()
        reporter = ResultsReporter()
        
        # Add all methods
        for method_name, preds in self.predictions.items():
            comparator.add_method_results(
                method_name, self.ground_truth, preds, voxel_spacing=(1.0, 1.0, 1.0))
        
        # Generate comprehensive report
        report = comparator.generate_comparison_report()
        
        # Check that report is complete
        self.assertIn('methods', report)
        self.assertIn('summary_statistics', report)
        self.assertIn('best_method', report)
        
        # Check that U-Net is the best method
        self.assertEqual(report['best_method']['method'], 'U-Net')
        
        # Generate text report
        text_report = reporter.generate_text_report(comparator)
        self.assertIsInstance(text_report, str)
        self.assertGreater(len(text_report), 1000)  # Should be substantial
        
        # Generate JSON report
        json_report = reporter.generate_json_report(comparator)
        self.assertIsInstance(json_report, dict)
        
        # Generate CSV summary
        csv_summary = reporter.generate_csv_summary(comparator)
        self.assertEqual(len(csv_summary), 3)  # Three methods
    
    def test_statistical_significance(self):
        """Test statistical significance detection."""
        comparator = MethodComparator()
        
        # Add methods
        for method_name, preds in self.predictions.items():
            comparator.add_method_results(
                method_name, self.ground_truth, preds, voxel_spacing=(1.0, 1.0, 1.0))
        
        # Compare U-Net vs Traditional (should be significant)
        comparison = comparator.compare_methods('U-Net', 'Traditional', 'dice_coefficient')
        
        # Should show significant difference
        self.assertTrue(comparison['paired_t_test']['significant'])
        self.assertLess(comparison['paired_t_test']['p_value'], 0.05)
        
        # Should have substantial effect size
        self.assertIn(comparison['effect_size'], ['medium', 'large'])
    
    def test_volume_analysis(self):
        """Test volume analysis integration."""
        comparator = MethodComparator()
        
        # Add methods with volume analysis
        for method_name, preds in self.predictions.items():
            comparator.add_method_results(
                method_name, self.ground_truth, preds, voxel_spacing=(2.0, 1.0, 1.0))
        
        # Check that volume comparisons are calculated
        for method_name in self.predictions.keys():
            volume_comparisons = comparator.results[method_name]['volume_comparisons']
            self.assertEqual(len(volume_comparisons), self.n_samples)
            
            # Check that volume comparison contains expected metrics
            vol_comp = volume_comparisons[0]
            expected_keys = [
                'true_volume_mm3', 'pred_volume_mm3', 'volume_difference_mm3',
                'relative_error', 'absolute_error_mm3'
            ]
            
            for key in expected_keys:
                self.assertIn(key, vol_comp)
    
    def test_performance_consistency(self):
        """Test performance consistency across samples."""
        comparator = MethodComparator()
        
        # Add methods
        for method_name, preds in self.predictions.items():
            comparator.add_method_results(
                method_name, self.ground_truth, preds, voxel_spacing=(1.0, 1.0, 1.0))
        
        # Check consistency (coefficient of variation)
        for method_name in self.predictions.keys():
            stats = comparator.calculate_summary_statistics(method_name)
            dice_stats = stats['dice_coefficient']
            
            # Calculate coefficient of variation
            cv = dice_stats['std'] / dice_stats['mean'] if dice_stats['mean'] > 0 else 0
            
            # Should be reasonable (< 1.0)
            self.assertLess(cv, 1.0)
            
            # U-Net should be most consistent
            if method_name == 'U-Net':
                self.assertLess(cv, 0.5)  # Should be quite consistent


def run_all_tests():
    """Run all tests."""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestSegmentationMetrics,
        TestVolumeCalculator,
        TestMethodComparator,
        TestResultsReporter,
        TestIntegration
    ]
    
    for test_class in test_classes:
        test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(test_class))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result


if __name__ == '__main__':
    print("Running Results Calculation Module Tests")
    print("=" * 50)
    
    result = run_all_tests()
    
    print("\n" + "=" * 50)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, failure in result.failures:
            print(f"- {test}: {failure}")
    
    if result.errors:
        print("\nErrors:")
        for test, error in result.errors:
            print(f"- {test}: {error}")
    
    if result.wasSuccessful():
        print("\nAll tests passed successfully! ✅")
    else:
        print("\nSome tests failed. ❌")