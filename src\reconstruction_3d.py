"""
3D Image Reconstruction Module
=============================

This module implements comprehensive 3D reconstruction capabilities for brain tumor
visualization from segmented 2D MRI slices. It includes volume interpolation,
isotropic voxel creation, 3D visualization, orthogonal views, and surface rendering.

Author: Dr<PERSON>il
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from mpl_toolkits.mplot3d.art3d import Poly3DCollection
import matplotlib.patches as patches
from matplotlib.colors import ListedColormap
import cv2
from scipy import ndimage
from scipy.interpolate import RegularGridInterpolator, interp1d
from scipy.spatial.distance import cdist
from skimage import measure, morphology
from skimage.filters import gaussian
from skimage.transform import resize
import warnings
from typing import List, Tuple, Optional, Dict, Any, Union
import json
import time
warnings.filterwarnings('ignore')


class VolumeInterpolator:
    """
    Handles volume interpolation between 2D slices to create smooth 3D volumes.
    """
    
    def __init__(self, method='linear'):
        """
        Initialize volume interpolator.
        
        Args:
            method: Interpolation method ('linear', 'cubic', 'nearest')
        """
        self.method = method
        self.interpolated_volume = None
        self.original_spacing = None
        self.target_spacing = None
        
    def interpolate_volume(self, slices: List[np.ndarray], 
                          slice_spacing: float = 1.0,
                          target_spacing: float = 0.5,
                          axis: int = 0) -> np.ndarray:
        """
        Interpolate between 2D slices to create a dense 3D volume.
        
        Args:
            slices: List of 2D arrays representing slices
            slice_spacing: Original spacing between slices
            target_spacing: Target spacing for interpolation
            axis: Axis along which to interpolate (0=z, 1=y, 2=x)
            
        Returns:
            3D volume with interpolated slices
        """
        if len(slices) < 2:
            raise ValueError("Need at least 2 slices for interpolation")
        
        # Stack slices into 3D array
        volume_3d = np.stack(slices, axis=axis)
        
        # Get original dimensions
        original_shape = volume_3d.shape
        
        # Calculate number of slices needed
        original_depth = original_shape[axis]
        original_positions = np.arange(original_depth) * slice_spacing
        
        # Create target positions
        target_depth = int((original_depth - 1) * slice_spacing / target_spacing) + 1
        target_positions = np.arange(target_depth) * target_spacing
        
        # Ensure target positions don't exceed original range
        target_positions = target_positions[target_positions <= original_positions[-1]]
        
        # Interpolate along the specified axis
        if self.method == 'linear':
            interpolated_volume = self._linear_interpolation(
                volume_3d, original_positions, target_positions, axis)
        elif self.method == 'cubic':
            interpolated_volume = self._cubic_interpolation(
                volume_3d, original_positions, target_positions, axis)
        elif self.method == 'nearest':
            interpolated_volume = self._nearest_interpolation(
                volume_3d, original_positions, target_positions, axis)
        else:
            raise ValueError(f"Unsupported interpolation method: {self.method}")
        
        self.interpolated_volume = interpolated_volume
        self.original_spacing = slice_spacing
        self.target_spacing = target_spacing
        
        return interpolated_volume
    
    def _linear_interpolation(self, volume: np.ndarray, 
                            original_pos: np.ndarray, 
                            target_pos: np.ndarray, 
                            axis: int) -> np.ndarray:
        """Linear interpolation along specified axis."""
        # Create interpolation function
        f = interp1d(original_pos, volume, axis=axis, kind='linear', 
                    bounds_error=False, fill_value='extrapolate')
        
        return f(target_pos)
    
    def _cubic_interpolation(self, volume: np.ndarray, 
                           original_pos: np.ndarray, 
                           target_pos: np.ndarray, 
                           axis: int) -> np.ndarray:
        """Cubic interpolation along specified axis."""
        if len(original_pos) < 4:
            # Fall back to linear for insufficient data points
            return self._linear_interpolation(volume, original_pos, target_pos, axis)
        
        f = interp1d(original_pos, volume, axis=axis, kind='cubic', 
                    bounds_error=False, fill_value='extrapolate')
        
        return f(target_pos)
    
    def _nearest_interpolation(self, volume: np.ndarray, 
                             original_pos: np.ndarray, 
                             target_pos: np.ndarray, 
                             axis: int) -> np.ndarray:
        """Nearest neighbor interpolation along specified axis."""
        f = interp1d(original_pos, volume, axis=axis, kind='nearest', 
                    bounds_error=False, fill_value='extrapolate')
        
        return f(target_pos)
    
    def apply_smoothing(self, volume: np.ndarray, 
                       sigma: float = 1.0) -> np.ndarray:
        """
        Apply Gaussian smoothing to the interpolated volume.
        
        Args:
            volume: 3D volume to smooth
            sigma: Standard deviation for Gaussian kernel
            
        Returns:
            Smoothed 3D volume
        """
        return gaussian(volume, sigma=sigma)


class IsotropicVoxelCreator:
    """
    Creates isotropic voxels from anisotropic medical imaging data.
    """
    
    def __init__(self):
        """Initialize isotropic voxel creator."""
        self.original_spacing = None
        self.target_spacing = None
        self.isotropic_volume = None
        
    def create_isotropic_volume(self, volume: np.ndarray, 
                              original_spacing: Tuple[float, float, float],
                              target_spacing: Optional[float] = None) -> np.ndarray:
        """
        Create isotropic voxels from anisotropic volume.
        
        Args:
            volume: 3D volume with anisotropic spacing
            original_spacing: Original spacing in (z, y, x) format
            target_spacing: Target isotropic spacing (if None, uses minimum spacing)
            
        Returns:
            Isotropic 3D volume
        """
        if target_spacing is None:
            target_spacing = min(original_spacing)
        
        # Calculate scaling factors
        scale_factors = np.array(original_spacing) / target_spacing
        
        # Calculate new dimensions
        new_shape = np.round(np.array(volume.shape) * scale_factors).astype(int)
        
        # Resize volume
        isotropic_volume = resize(volume, new_shape, 
                                preserve_range=True, anti_aliasing=True)
        
        self.original_spacing = original_spacing
        self.target_spacing = target_spacing
        self.isotropic_volume = isotropic_volume
        
        return isotropic_volume
    
    def resample_to_spacing(self, volume: np.ndarray, 
                          current_spacing: Tuple[float, float, float],
                          target_spacing: Tuple[float, float, float]) -> np.ndarray:
        """
        Resample volume to specific spacing.
        
        Args:
            volume: Input 3D volume
            current_spacing: Current voxel spacing
            target_spacing: Target voxel spacing
            
        Returns:
            Resampled volume
        """
        # Calculate scaling factors
        scale_factors = np.array(current_spacing) / np.array(target_spacing)
        
        # Calculate new dimensions
        new_shape = np.round(np.array(volume.shape) * scale_factors).astype(int)
        
        # Resample
        resampled_volume = resize(volume, new_shape, 
                                preserve_range=True, anti_aliasing=True)
        
        return resampled_volume
    
    def calculate_volume_metrics(self, volume: np.ndarray, 
                               spacing: Tuple[float, float, float]) -> Dict[str, float]:
        """
        Calculate volume metrics.
        
        Args:
            volume: Binary 3D volume
            spacing: Voxel spacing
            
        Returns:
            Dictionary with volume metrics
        """
        # Calculate voxel volume
        voxel_volume = np.prod(spacing)
        
        # Calculate total volume
        total_voxels = np.sum(volume > 0)
        total_volume = total_voxels * voxel_volume
        
        # Calculate surface area (approximate)
        surface_voxels = np.sum(self._find_surface_voxels(volume))
        surface_area = surface_voxels * (spacing[0] * spacing[1])  # Approximate
        
        # Calculate bounding box
        coords = np.where(volume > 0)
        if len(coords[0]) > 0:
            bbox_min = [np.min(coords[i]) * spacing[i] for i in range(3)]
            bbox_max = [np.max(coords[i]) * spacing[i] for i in range(3)]
            bbox_size = [bbox_max[i] - bbox_min[i] for i in range(3)]
        else:
            bbox_min = [0, 0, 0]
            bbox_max = [0, 0, 0]
            bbox_size = [0, 0, 0]
        
        return {
            'total_volume_mm3': total_volume,
            'total_volume_ml': total_volume / 1000,
            'surface_area_mm2': surface_area,
            'bounding_box_min': bbox_min,
            'bounding_box_max': bbox_max,
            'bounding_box_size': bbox_size,
            'voxel_count': int(total_voxels),
            'voxel_volume_mm3': voxel_volume
        }
    
    def _find_surface_voxels(self, volume: np.ndarray) -> np.ndarray:
        """Find surface voxels (voxels adjacent to background)."""
        # Create structure for 6-connectivity
        structure = np.array([[[0, 0, 0], [0, 1, 0], [0, 0, 0]],
                             [[0, 1, 0], [1, 1, 1], [0, 1, 0]],
                             [[0, 0, 0], [0, 1, 0], [0, 0, 0]]])
        
        # Dilate the volume
        dilated = ndimage.binary_dilation(volume, structure=structure)
        
        # Surface voxels are in the dilated volume but not in the original
        surface = dilated & ~volume
        
        return surface


class OrthogonalViews:
    """
    Creates orthogonal views (axial, sagittal, coronal) from 3D volumes.
    """
    
    def __init__(self):
        """Initialize orthogonal views generator."""
        self.views = {}
        
    def generate_orthogonal_views(self, volume: np.ndarray, 
                                slice_indices: Optional[Dict[str, int]] = None) -> Dict[str, np.ndarray]:
        """
        Generate orthogonal views from 3D volume.
        
        Args:
            volume: 3D volume
            slice_indices: Dictionary with slice indices for each view
            
        Returns:
            Dictionary with orthogonal views
        """
        if slice_indices is None:
            slice_indices = {
                'axial': volume.shape[0] // 2,
                'sagittal': volume.shape[2] // 2,
                'coronal': volume.shape[1] // 2
            }
        
        views = {}
        
        # Axial view (z-axis slice)
        views['axial'] = volume[slice_indices['axial'], :, :]
        
        # Sagittal view (x-axis slice)
        views['sagittal'] = volume[:, :, slice_indices['sagittal']]
        
        # Coronal view (y-axis slice)
        views['coronal'] = volume[:, slice_indices['coronal'], :]
        
        self.views = views
        return views
    
    def create_mip_views(self, volume: np.ndarray) -> Dict[str, np.ndarray]:
        """
        Create Maximum Intensity Projection (MIP) views.
        
        Args:
            volume: 3D volume
            
        Returns:
            Dictionary with MIP views
        """
        mip_views = {}
        
        # MIP along different axes
        mip_views['axial_mip'] = np.max(volume, axis=0)      # Max along z
        mip_views['sagittal_mip'] = np.max(volume, axis=2)   # Max along x
        mip_views['coronal_mip'] = np.max(volume, axis=1)    # Max along y
        
        return mip_views
    
    def create_mean_views(self, volume: np.ndarray) -> Dict[str, np.ndarray]:
        """
        Create mean intensity views.
        
        Args:
            volume: 3D volume
            
        Returns:
            Dictionary with mean views
        """
        mean_views = {}
        
        # Mean along different axes
        mean_views['axial_mean'] = np.mean(volume, axis=0)
        mean_views['sagittal_mean'] = np.mean(volume, axis=2)
        mean_views['coronal_mean'] = np.mean(volume, axis=1)
        
        return mean_views
    
    def visualize_orthogonal_views(self, volume: np.ndarray, 
                                 slice_indices: Optional[Dict[str, int]] = None,
                                 title: str = "Orthogonal Views",
                                 save_path: Optional[str] = None) -> None:
        """
        Visualize orthogonal views.
        
        Args:
            volume: 3D volume
            slice_indices: Dictionary with slice indices
            title: Plot title
            save_path: Path to save the plot
        """
        views = self.generate_orthogonal_views(volume, slice_indices)
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # Axial view
        axes[0, 0].imshow(views['axial'], cmap='gray')
        axes[0, 0].set_title('Axial View')
        axes[0, 0].axis('off')
        
        # Sagittal view
        axes[0, 1].imshow(views['sagittal'], cmap='gray')
        axes[0, 1].set_title('Sagittal View')
        axes[0, 1].axis('off')
        
        # Coronal view
        axes[1, 0].imshow(views['coronal'], cmap='gray')
        axes[1, 0].set_title('Coronal View')
        axes[1, 0].axis('off')
        
        # 3D visualization placeholder
        axes[1, 1].text(0.5, 0.5, '3D View\n(See separate plot)', 
                        ha='center', va='center', transform=axes[1, 1].transAxes)
        axes[1, 1].axis('off')
        
        plt.suptitle(title, fontsize=16)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()


class SurfaceRenderer:
    """
    Creates 3D surface renderings from volume data using marching cubes algorithm.
    """
    
    def __init__(self):
        """Initialize surface renderer."""
        self.vertices = None
        self.faces = None
        self.normals = None
        self.values = None
        
    def extract_surface(self, volume: np.ndarray, 
                       level: float = 0.5,
                       spacing: Tuple[float, float, float] = (1.0, 1.0, 1.0)) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """
        Extract surface using marching cubes algorithm.
        
        Args:
            volume: 3D volume
            level: Isosurface level
            spacing: Voxel spacing
            
        Returns:
            Tuple of (vertices, faces, normals, values)
        """
        # Apply marching cubes
        vertices, faces, normals, values = measure.marching_cubes(
            volume, level=level, spacing=spacing)
        
        self.vertices = vertices
        self.faces = faces
        self.normals = normals
        self.values = values
        
        return vertices, faces, normals, values
    
    def render_surface_3d(self, volume: np.ndarray, 
                         level: float = 0.5,
                         spacing: Tuple[float, float, float] = (1.0, 1.0, 1.0),
                         alpha: float = 0.8,
                         color: str = 'red',
                         title: str = "3D Surface Rendering",
                         save_path: Optional[str] = None) -> None:
        """
        Render 3D surface.
        
        Args:
            volume: 3D volume
            level: Isosurface level
            spacing: Voxel spacing
            alpha: Surface transparency
            color: Surface color
            title: Plot title
            save_path: Path to save the plot
        """
        # Extract surface
        vertices, faces, normals, values = self.extract_surface(volume, level, spacing)
        
        # Create 3D plot
        fig = plt.figure(figsize=(12, 10))
        ax = fig.add_subplot(111, projection='3d')
        
        # Create mesh
        mesh = Poly3DCollection(vertices[faces], alpha=alpha, facecolor=color, edgecolor='black')
        ax.add_collection3d(mesh)
        
        # Set axis limits
        ax.set_xlim(0, volume.shape[2] * spacing[2])
        ax.set_ylim(0, volume.shape[1] * spacing[1])
        ax.set_zlim(0, volume.shape[0] * spacing[0])
        
        # Labels
        ax.set_xlabel('X (mm)')
        ax.set_ylabel('Y (mm)')
        ax.set_zlabel('Z (mm)')
        ax.set_title(title)
        
        # Add lighting effect
        ax.view_init(elev=20, azim=45)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def create_wireframe(self, volume: np.ndarray, 
                        level: float = 0.5,
                        spacing: Tuple[float, float, float] = (1.0, 1.0, 1.0),
                        title: str = "3D Wireframe",
                        save_path: Optional[str] = None) -> None:
        """
        Create wireframe visualization.
        
        Args:
            volume: 3D volume
            level: Isosurface level
            spacing: Voxel spacing
            title: Plot title
            save_path: Path to save the plot
        """
        # Extract surface
        vertices, faces, normals, values = self.extract_surface(volume, level, spacing)
        
        # Create 3D plot
        fig = plt.figure(figsize=(12, 10))
        ax = fig.add_subplot(111, projection='3d')
        
        # Create wireframe
        mesh = Poly3DCollection(vertices[faces], alpha=0.1, facecolor='none', edgecolor='blue')
        ax.add_collection3d(mesh)
        
        # Set axis limits
        ax.set_xlim(0, volume.shape[2] * spacing[2])
        ax.set_ylim(0, volume.shape[1] * spacing[1])
        ax.set_zlim(0, volume.shape[0] * spacing[0])
        
        # Labels
        ax.set_xlabel('X (mm)')
        ax.set_ylabel('Y (mm)')
        ax.set_zlabel('Z (mm)')
        ax.set_title(title)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def calculate_surface_metrics(self, volume: np.ndarray, 
                                level: float = 0.5,
                                spacing: Tuple[float, float, float] = (1.0, 1.0, 1.0)) -> Dict[str, float]:
        """
        Calculate surface metrics.
        
        Args:
            volume: 3D volume
            level: Isosurface level
            spacing: Voxel spacing
            
        Returns:
            Dictionary with surface metrics
        """
        vertices, faces, normals, values = self.extract_surface(volume, level, spacing)
        
        # Calculate surface area
        surface_area = 0
        for face in faces:
            v1, v2, v3 = vertices[face]
            # Calculate triangle area using cross product
            area = 0.5 * np.linalg.norm(np.cross(v2 - v1, v3 - v1))
            surface_area += area
        
        # Calculate volume (approximate using convex hull)
        from scipy.spatial import ConvexHull
        try:
            hull = ConvexHull(vertices)
            convex_volume = hull.volume
        except:
            convex_volume = 0
        
        # Calculate bounding box
        bbox_min = np.min(vertices, axis=0)
        bbox_max = np.max(vertices, axis=0)
        bbox_size = bbox_max - bbox_min
        
        # Calculate centroid
        centroid = np.mean(vertices, axis=0)
        
        return {
            'surface_area_mm2': surface_area,
            'convex_volume_mm3': convex_volume,
            'n_vertices': len(vertices),
            'n_faces': len(faces),
            'bounding_box_min': bbox_min.tolist(),
            'bounding_box_max': bbox_max.tolist(),
            'bounding_box_size': bbox_size.tolist(),
            'centroid': centroid.tolist()
        }


class VolumeVisualizer:
    """
    Advanced volume visualization capabilities.
    """
    
    def __init__(self):
        """Initialize volume visualizer."""
        self.volume = None
        
    def create_volume_slices(self, volume: np.ndarray, 
                           n_slices: int = 9,
                           axis: int = 0,
                           title: str = "Volume Slices",
                           save_path: Optional[str] = None) -> None:
        """
        Create a grid of volume slices.
        
        Args:
            volume: 3D volume
            n_slices: Number of slices to display
            axis: Axis along which to slice
            title: Plot title
            save_path: Path to save the plot
        """
        # Calculate slice indices
        slice_indices = np.linspace(0, volume.shape[axis] - 1, n_slices, dtype=int)
        
        # Create subplot grid
        n_cols = 3
        n_rows = (n_slices + n_cols - 1) // n_cols
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(4*n_cols, 4*n_rows))
        if n_slices == 1:
            axes = [axes]
        elif n_rows == 1:
            axes = axes.reshape(1, -1)
        
        for i, slice_idx in enumerate(slice_indices):
            row = i // n_cols
            col = i % n_cols
            ax = axes[row, col] if n_rows > 1 else axes[col]
            
            # Extract slice
            if axis == 0:
                slice_img = volume[slice_idx, :, :]
            elif axis == 1:
                slice_img = volume[:, slice_idx, :]
            else:
                slice_img = volume[:, :, slice_idx]
            
            # Display slice
            ax.imshow(slice_img, cmap='gray')
            ax.set_title(f'Slice {slice_idx}')
            ax.axis('off')
        
        # Remove empty subplots
        for i in range(n_slices, n_rows * n_cols):
            row = i // n_cols
            col = i % n_cols
            ax = axes[row, col] if n_rows > 1 else axes[col]
            ax.remove()
        
        plt.suptitle(title, fontsize=16)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def create_volume_overlay(self, image_volume: np.ndarray, 
                            mask_volume: np.ndarray,
                            slice_index: int,
                            axis: int = 0,
                            alpha: float = 0.5,
                            title: str = "Volume Overlay",
                            save_path: Optional[str] = None) -> None:
        """
        Create overlay of image and mask volumes.
        
        Args:
            image_volume: Original image volume
            mask_volume: Mask volume
            slice_index: Slice index to display
            axis: Axis along which to slice
            alpha: Overlay transparency
            title: Plot title
            save_path: Path to save the plot
        """
        # Extract slices
        if axis == 0:
            image_slice = image_volume[slice_index, :, :]
            mask_slice = mask_volume[slice_index, :, :]
        elif axis == 1:
            image_slice = image_volume[:, slice_index, :]
            mask_slice = mask_volume[:, slice_index, :]
        else:
            image_slice = image_volume[:, :, slice_index]
            mask_slice = mask_volume[:, :, slice_index]
        
        # Create plot
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # Display image
        ax.imshow(image_slice, cmap='gray')
        
        # Overlay mask
        masked_overlay = np.ma.masked_where(mask_slice == 0, mask_slice)
        ax.imshow(masked_overlay, cmap='Reds', alpha=alpha)
        
        ax.set_title(f'{title} - Slice {slice_index}')
        ax.axis('off')
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def create_3d_scatter(self, volume: np.ndarray, 
                         threshold: float = 0.5,
                         sample_ratio: float = 0.1,
                         title: str = "3D Scatter Plot",
                         save_path: Optional[str] = None) -> None:
        """
        Create 3D scatter plot of volume data.
        
        Args:
            volume: 3D volume
            threshold: Threshold for including points
            sample_ratio: Ratio of points to sample
            title: Plot title
            save_path: Path to save the plot
        """
        # Find points above threshold
        coords = np.where(volume > threshold)
        
        if len(coords[0]) == 0:
            print("No points above threshold")
            return
        
        # Sample points
        n_points = len(coords[0])
        sample_size = int(n_points * sample_ratio)
        sample_indices = np.random.choice(n_points, sample_size, replace=False)
        
        x = coords[2][sample_indices]  # X coordinates
        y = coords[1][sample_indices]  # Y coordinates
        z = coords[0][sample_indices]  # Z coordinates
        colors = volume[coords[0][sample_indices], 
                       coords[1][sample_indices], 
                       coords[2][sample_indices]]
        
        # Create 3D scatter plot
        fig = plt.figure(figsize=(12, 10))
        ax = fig.add_subplot(111, projection='3d')
        
        scatter = ax.scatter(x, y, z, c=colors, cmap='viridis', alpha=0.6)
        
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_zlabel('Z')
        ax.set_title(title)
        
        # Add colorbar
        plt.colorbar(scatter, ax=ax, shrink=0.5)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()


class Reconstruction3D:
    """
    Main 3D reconstruction class that combines all reconstruction capabilities.
    """
    
    def __init__(self):
        """Initialize 3D reconstruction system."""
        self.volume_interpolator = VolumeInterpolator()
        self.isotropic_creator = IsotropicVoxelCreator()
        self.orthogonal_views = OrthogonalViews()
        self.surface_renderer = SurfaceRenderer()
        self.volume_visualizer = VolumeVisualizer()
        
        self.reconstructed_volume = None
        self.reconstruction_info = {}
        
    def reconstruct_from_slices(self, slices: List[np.ndarray],
                              slice_spacing: float = 1.0,
                              pixel_spacing: Tuple[float, float] = (1.0, 1.0),
                              target_isotropic_spacing: Optional[float] = None,
                              interpolation_method: str = 'linear',
                              smoothing_sigma: float = 0.5) -> np.ndarray:
        """
        Complete 3D reconstruction from 2D slices.
        
        Args:
            slices: List of 2D slice arrays
            slice_spacing: Spacing between slices
            pixel_spacing: In-plane pixel spacing (y, x)
            target_isotropic_spacing: Target isotropic spacing
            interpolation_method: Interpolation method
            smoothing_sigma: Smoothing parameter
            
        Returns:
            Reconstructed 3D volume
        """
        print(f"Starting 3D reconstruction from {len(slices)} slices...")
        start_time = time.time()
        
        # Step 1: Volume interpolation
        print("1. Interpolating volume...")
        self.volume_interpolator.method = interpolation_method
        
        # Determine target spacing for interpolation
        if target_isotropic_spacing is None:
            target_spacing = min(slice_spacing, min(pixel_spacing))
        else:
            target_spacing = target_isotropic_spacing
        
        interpolated_volume = self.volume_interpolator.interpolate_volume(
            slices, slice_spacing, target_spacing)
        
        # Step 2: Create isotropic voxels
        print("2. Creating isotropic voxels...")
        original_spacing = (target_spacing, pixel_spacing[0], pixel_spacing[1])
        
        if target_isotropic_spacing is not None:
            isotropic_volume = self.isotropic_creator.create_isotropic_volume(
                interpolated_volume, original_spacing, target_isotropic_spacing)
            final_spacing = (target_isotropic_spacing, target_isotropic_spacing, target_isotropic_spacing)
        else:
            isotropic_volume = interpolated_volume
            final_spacing = original_spacing
        
        # Step 3: Apply smoothing
        print("3. Applying smoothing...")
        if smoothing_sigma > 0:
            smoothed_volume = self.volume_interpolator.apply_smoothing(
                isotropic_volume, smoothing_sigma)
        else:
            smoothed_volume = isotropic_volume
        
        # Store reconstruction info
        self.reconstruction_info = {
            'original_slices': len(slices),
            'original_slice_spacing': slice_spacing,
            'original_pixel_spacing': pixel_spacing,
            'target_isotropic_spacing': target_isotropic_spacing,
            'final_spacing': final_spacing,
            'interpolation_method': interpolation_method,
            'smoothing_sigma': smoothing_sigma,
            'reconstruction_time': time.time() - start_time,
            'final_volume_shape': smoothed_volume.shape
        }
        
        self.reconstructed_volume = smoothed_volume
        
        print(f"Reconstruction complete in {self.reconstruction_info['reconstruction_time']:.2f} seconds")
        print(f"Final volume shape: {smoothed_volume.shape}")
        
        return smoothed_volume
    
    def analyze_reconstruction(self, volume: Optional[np.ndarray] = None,
                             spacing: Optional[Tuple[float, float, float]] = None) -> Dict[str, Any]:
        """
        Analyze the reconstructed volume.
        
        Args:
            volume: Volume to analyze (if None, uses reconstructed volume)
            spacing: Voxel spacing (if None, uses reconstruction info)
            
        Returns:
            Analysis results
        """
        if volume is None:
            volume = self.reconstructed_volume
        
        if spacing is None and 'final_spacing' in self.reconstruction_info:
            spacing = self.reconstruction_info['final_spacing']
        elif spacing is None:
            spacing = (1.0, 1.0, 1.0)
        
        if volume is None:
            raise ValueError("No volume available for analysis")
        
        # Calculate volume metrics
        volume_metrics = self.isotropic_creator.calculate_volume_metrics(volume, spacing)
        
        # Calculate surface metrics
        surface_metrics = self.surface_renderer.calculate_surface_metrics(volume, spacing=spacing)
        
        # Combine with reconstruction info
        analysis = {
            'reconstruction_info': self.reconstruction_info,
            'volume_metrics': volume_metrics,
            'surface_metrics': surface_metrics,
            'volume_statistics': {
                'min_value': float(np.min(volume)),
                'max_value': float(np.max(volume)),
                'mean_value': float(np.mean(volume)),
                'std_value': float(np.std(volume)),
                'non_zero_voxels': int(np.sum(volume > 0))
            }
        }
        
        return analysis
    
    def create_comprehensive_visualization(self, volume: Optional[np.ndarray] = None,
                                         spacing: Optional[Tuple[float, float, float]] = None,
                                         title: str = "3D Reconstruction Results",
                                         save_path: Optional[str] = None) -> None:
        """
        Create comprehensive visualization of reconstruction results.
        
        Args:
            volume: Volume to visualize
            spacing: Voxel spacing
            title: Plot title
            save_path: Path to save plots
        """
        if volume is None:
            volume = self.reconstructed_volume
        
        if spacing is None and 'final_spacing' in self.reconstruction_info:
            spacing = self.reconstruction_info['final_spacing']
        elif spacing is None:
            spacing = (1.0, 1.0, 1.0)
        
        if volume is None:
            raise ValueError("No volume available for visualization")
        
        # 1. Orthogonal views
        print("Creating orthogonal views...")
        self.orthogonal_views.visualize_orthogonal_views(
            volume, title=f"{title} - Orthogonal Views",
            save_path=f"{save_path}_orthogonal.png" if save_path else None)
        
        # 2. Volume slices
        print("Creating volume slices...")
        self.volume_visualizer.create_volume_slices(
            volume, title=f"{title} - Volume Slices",
            save_path=f"{save_path}_slices.png" if save_path else None)
        
        # 3. 3D surface rendering
        print("Creating 3D surface rendering...")
        self.surface_renderer.render_surface_3d(
            volume, spacing=spacing, title=f"{title} - Surface Rendering",
            save_path=f"{save_path}_surface.png" if save_path else None)
        
        # 4. Wireframe
        print("Creating wireframe visualization...")
        self.surface_renderer.create_wireframe(
            volume, spacing=spacing, title=f"{title} - Wireframe",
            save_path=f"{save_path}_wireframe.png" if save_path else None)
    
    def export_reconstruction_data(self, export_path: str,
                                 volume: Optional[np.ndarray] = None,
                                 analysis: Optional[Dict[str, Any]] = None) -> None:
        """
        Export reconstruction data and analysis.
        
        Args:
            export_path: Base path for export files
            volume: Volume to export
            analysis: Analysis results to export
        """
        if volume is None:
            volume = self.reconstructed_volume
        
        if analysis is None:
            analysis = self.analyze_reconstruction(volume)
        
        # Export volume as numpy array
        if volume is not None:
            np.save(f"{export_path}_volume.npy", volume)
        
        # Export analysis as JSON
        with open(f"{export_path}_analysis.json", 'w') as f:
            json.dump(analysis, f, indent=2)
        
        print(f"Reconstruction data exported to {export_path}")


def main():
    """
    Example usage of the 3D reconstruction module.
    """
    print("3D Reconstruction Module Demo")
    print("=" * 50)
    
    # Create sample data
    np.random.seed(42)
    
    # Generate synthetic MRI slices
    n_slices = 20
    slice_size = (64, 64)
    slices = []
    
    print(f"Generating {n_slices} synthetic MRI slices...")
    for i in range(n_slices):
        # Create base slice
        slice_img = np.random.rand(*slice_size) * 100 + 50
        
        # Add a tumor-like structure
        center = (32, 32)
        radius = 8 + np.sin(i * 0.3) * 3  # Varying radius
        
        y, x = np.ogrid[:slice_size[0], :slice_size[1]]
        mask = (x - center[0])**2 + (y - center[1])**2 <= radius**2
        
        slice_img[mask] = 200  # Bright tumor region
        slices.append(slice_img)
    
    # Initialize reconstruction system
    reconstructor = Reconstruction3D()
    
    # Perform reconstruction
    print("\nPerforming 3D reconstruction...")
    reconstructed_volume = reconstructor.reconstruct_from_slices(
        slices=slices,
        slice_spacing=2.0,
        pixel_spacing=(1.0, 1.0),
        target_isotropic_spacing=1.0,
        interpolation_method='linear',
        smoothing_sigma=0.5
    )
    
    # Analyze reconstruction
    print("\nAnalyzing reconstruction...")
    analysis = reconstructor.analyze_reconstruction()
    
    # Print analysis results
    print("\nReconstruction Analysis:")
    print(f"Original slices: {analysis['reconstruction_info']['original_slices']}")
    print(f"Final volume shape: {analysis['reconstruction_info']['final_volume_shape']}")
    print(f"Reconstruction time: {analysis['reconstruction_info']['reconstruction_time']:.2f} seconds")
    print(f"Total volume: {analysis['volume_metrics']['total_volume_ml']:.2f} ml")
    print(f"Surface area: {analysis['surface_metrics']['surface_area_mm2']:.2f} mm²")
    print(f"Number of vertices: {analysis['surface_metrics']['n_vertices']}")
    print(f"Number of faces: {analysis['surface_metrics']['n_faces']}")
    
    # Create visualizations
    print("\nCreating visualizations...")
    reconstructor.create_comprehensive_visualization(
        title="Synthetic Brain Tumor Reconstruction")
    
    # Export data
    print("\nExporting reconstruction data...")
    reconstructor.export_reconstruction_data("reconstruction_results", 
                                           analysis=analysis)
    
    print("\nDemo completed successfully!")


if __name__ == "__main__":
    main()