import React from 'react';

const OverviewTab = ({ onTabChange }) => {
  const processSteps = [
    {
      id: 'dicom',
      title: 'DICOM Loading',
      icon: 'fas fa-database',
      description: 'Load and organize DICOM series from medical imaging devices',
      badge: 'Step 1',
      color: '#3498db'
    },
    {
      id: 'preprocessing',
      title: 'Image Preprocessing',
      icon: 'fas fa-cog',
      description: 'Apply contrast adaptation, brightness adjustment, and filtering',
      badge: 'Step 2',
      color: '#9b59b6'
    },
    {
      id: 'segmentation',
      title: 'Image Segmentation',
      icon: 'fas fa-puzzle-piece',
      description: 'Segment brain regions using deformable models and watershed algorithms',
      badge: 'Step 3',
      color: '#e74c3c'
    },
    {
      id: 'features',
      title: 'Feature Extraction',
      icon: 'fas fa-chart-bar',
      description: 'Extract texture, shape, and statistical features from segmented regions',
      badge: 'Step 4',
      color: '#f39c12'
    },
    {
      id: 'reconstruction',
      title: '3D Reconstruction',
      icon: 'fas fa-cube',
      description: 'Reconstruct 3D tumor models with surface rendering and volume analysis',
      badge: 'Step 5',
      color: '#27ae60'
    },
    {
      id: 'results',
      title: 'Results & Analysis',
      icon: 'fas fa-chart-line',
      description: 'Calculate performance metrics and generate comprehensive reports',
      badge: 'Step 6',
      color: '#e67e22'
    }
  ];

  const keyFeatures = [
    {
      icon: 'fas fa-brain',
      title: 'Advanced AI Algorithms',
      description: 'State-of-the-art machine learning and computer vision techniques'
    },
    {
      icon: 'fas fa-microscope',
      title: 'Precise Segmentation',
      description: 'Multi-method approach with ROI, Active Contours, Watershed, and Deformable Models'
    },
    {
      icon: 'fas fa-cube',
      title: '3D Visualization',
      description: 'Interactive 3D models with orthogonal views and surface rendering'
    },
    {
      icon: 'fas fa-chart-line',
      title: 'Performance Metrics',
      description: 'Comprehensive evaluation with Dice coefficient, Jaccard index, and more'
    },
    {
      icon: 'fas fa-clock',
      title: 'Real-time Processing',
      description: 'Optimized algorithms for fast and efficient analysis'
    },
    {
      icon: 'fas fa-shield-alt',
      title: 'Clinical Grade',
      description: 'Designed for medical professionals with accuracy and reliability'
    }
  ];

  return (
    <div className="overview-tab">
      <div className="overview-header">
        <h2>Brain Tumor Detection Pipeline</h2>
        <p>
          Comprehensive 6-stage processing pipeline for detecting and analyzing brain tumors 
          in 3D MRI images using advanced computer vision and machine learning techniques.
        </p>
      </div>

      <div className="overview-grid">
        {processSteps.map((step) => (
          <div 
            key={step.id} 
            className="process-card"
            onClick={() => onTabChange(step.id)}
          >
            <div className="card-header">
              <div className="icon-wrapper" style={{ background: step.color }}>
                <i className={step.icon}></i>
              </div>
              <div className="card-title-section">
                <h3 className="card-title">{step.title}</h3>
                <span className="step-badge" style={{ background: step.color }}>{step.badge}</span>
              </div>
            </div>
            <div className="card-content">
              <div className="card-image">
                <div className={`placeholder-image ${step.id}-placeholder`} style={{ borderColor: step.color }}>
                  <i className={step.icon} style={{ color: step.color }}></i>
                  <span>{step.title}</span>
                </div>
              </div>
              <p className="card-description">{step.description}</p>
            </div>
          </div>
        ))}
      </div>

      <div className="features-section">
        <h3>Key Features</h3>
        <div className="features-grid">
          {keyFeatures.map((feature, index) => (
            <div key={index} className="feature-card">
              <div className="feature-icon">
                <i className={feature.icon}></i>
              </div>
              <div className="feature-content">
                <h4>{feature.title}</h4>
                <p>{feature.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="pipeline-flow">
        <h3>Processing Pipeline Flow</h3>
        <div className="flow-diagram">
          <div className="flow-steps">
            {processSteps.map((step, index) => (
              <React.Fragment key={step.id}>
                <div className="flow-step">
                  <div className="flow-icon" style={{ background: step.color }}>
                    <i className={step.icon}></i>
                  </div>
                  <div className="flow-title">{step.title}</div>
                </div>
                {index < processSteps.length - 1 && (
                  <div className="flow-arrow">
                    <i className="fas fa-arrow-right"></i>
                  </div>
                )}
              </React.Fragment>
            ))}
          </div>
        </div>
      </div>

      <div className="technical-specs">
        <h3>Technical Specifications</h3>
        <div className="specs-grid">
          <div className="spec-card">
            <h4>Supported Formats</h4>
            <ul>
              <li>DICOM (.dcm, .dicom)</li>
              <li>NIfTI (.nii, .nii.gz)</li>
              <li>Standard Images (.jpg, .png, .tiff)</li>
            </ul>
          </div>
          <div className="spec-card">
            <h4>Segmentation Methods</h4>
            <ul>
              <li>Region of Interest (ROI) Setting</li>
              <li>Active Contour Models</li>
              <li>Watershed Segmentation</li>
              <li>Deformable Models</li>
            </ul>
          </div>
          <div className="spec-card">
            <h4>Performance Metrics</h4>
            <ul>
              <li>Dice Coefficient</li>
              <li>Jaccard Index</li>
              <li>Sensitivity & Specificity</li>
              <li>Accuracy & Precision</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OverviewTab;