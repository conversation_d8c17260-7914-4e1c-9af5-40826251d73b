#!/usr/bin/env python
"""
Backend server startup script for Brain Tumor Detection System
"""

import os
import sys
import subprocess

def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import flask
        import flask_cors
        print("✓ Flask dependencies found")
        return True
    except ImportError:
        print("✗ Flask dependencies missing")
        return False

def install_dependencies():
    """Install required dependencies"""
    print("Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("✗ Failed to install dependencies")
        return False

def start_server():
    """Start the Flask server"""
    print("Starting Brain Tumor Detection Backend Server...")
    print("Server will be available at: http://localhost:8000")
    print("Health check endpoint: http://localhost:8000/health")
    print("Analysis endpoint: http://localhost:8000/analyze")
    print("\nPress Ctrl+C to stop the server")
    
    try:
        from app import app
        app.run(debug=True, port=8000, host='0.0.0.0')
    except Exception as e:
        print(f"✗ Failed to start server: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("Brain Tumor Detection Backend Server")
    print("=" * 60)
    
    if not check_dependencies():
        print("\nAttempting to install dependencies...")
        if not install_dependencies():
            print("Please install dependencies manually:")
            print("pip install -r requirements.txt")
            sys.exit(1)
    
    start_server()