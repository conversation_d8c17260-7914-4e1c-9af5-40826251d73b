import React, { useState } from 'react';
import CodeBlock from '../CodeBlock';

const FeaturesTab = ({ analysisResults }) => {
  const [activeCategory, setActiveCategory] = useState('texture');
  const [codeLanguage, setCodeLanguage] = useState('python');

  const featureCategories = [
    {
      id: 'texture',
      title: 'Texture Features',
      icon: 'fas fa-grip-horizontal',
      description: 'GLCM, LBP, and statistical texture descriptors',
      color: '#e74c3c',
      features: [
        { name: 'Contrast', description: 'Measure of local variations in GLCM', formula: 'Σ(i-j)²P(i,j)' },
        { name: 'Dissimilarity', description: 'Linear dependency of gray levels', formula: 'Σ|i-j|P(i,j)' },
        { name: 'Homogeneity', description: 'Closeness of distribution to diagonal', formula: 'Σ[P(i,j)/(1+|i-j|)]' },
        { name: 'Energy', description: 'Measure of uniformity', formula: 'Σ[P(i,j)]²' },
        { name: 'Correlation', description: 'Linear dependency of gray levels', formula: 'Σ[(i-μᵢ)(j-μⱼ)P(i,j)]/(σᵢσⱼ)' },
        { name: 'LBP Uniformity', description: 'Local binary pattern uniformity', formula: 'Σ[P(u)]² where u≤2' },
        { name: 'LBP Entropy', description: 'Randomness in LBP distribution', formula: '-Σ[P(u)log₂P(u)]' }
      ]
    },
    {
      id: 'shape',
      title: 'Shape Features',
      icon: 'fas fa-shapes',
      description: 'Morphological and geometric descriptors',
      color: '#3498db',
      features: [
        { name: 'Area', description: 'Number of pixels in region', formula: 'A = Σpixels' },
        { name: 'Perimeter', description: 'Boundary length of region', formula: 'P = boundary length' },
        { name: 'Eccentricity', description: 'Ratio of foci distance to major axis', formula: 'e = c/a' },
        { name: 'Solidity', description: 'Ratio of area to convex hull area', formula: 'S = A/A_convex' },
        { name: 'Extent', description: 'Ratio of area to bounding box', formula: 'E = A/A_bbox' },
        { name: 'Compactness', description: 'Measure of roundness', formula: 'C = P²/(4πA)' },
        { name: 'Circularity', description: 'Similarity to perfect circle', formula: 'Ci = 4πA/P²' },
        { name: 'Equivalent Diameter', description: 'Diameter of circle with same area', formula: 'd = 2√(A/π)' }
      ]
    },
    {
      id: 'statistical',
      title: 'Statistical Features',
      icon: 'fas fa-chart-bar',
      description: 'Intensity distribution and statistical moments',
      color: '#27ae60',
      features: [
        { name: 'Mean', description: 'Average intensity value', formula: 'μ = (1/N)Σxᵢ' },
        { name: 'Standard Deviation', description: 'Measure of intensity spread', formula: 'σ = √[(1/N)Σ(xᵢ-μ)²]' },
        { name: 'Variance', description: 'Squared standard deviation', formula: 'σ² = (1/N)Σ(xᵢ-μ)²' },
        { name: 'Skewness', description: 'Asymmetry of distribution', formula: 'S = E[(X-μ)³]/σ³' },
        { name: 'Kurtosis', description: 'Tail heaviness of distribution', formula: 'K = E[(X-μ)⁴]/σ⁴' },
        { name: 'Entropy', description: 'Randomness measure', formula: 'H = -Σ[P(i)log₂P(i)]' },
        { name: 'Range', description: 'Difference between max and min', formula: 'R = max - min' },
        { name: 'Median', description: 'Middle value of sorted data', formula: 'M = middle value' }
      ]
    }
  ];

  const pythonCode = `import numpy as np
import cv2
from scipy import ndimage
from skimage.feature import graycomatrix, graycoprops, local_binary_pattern
from skimage.measure import regionprops, label
from scipy.stats import skew, kurtosis

class FeatureExtractor:
    def __init__(self):
        self.features = {}
        self.feature_names = []
    
    def extract_texture_features(self, image, distances=[1], angles=[0, np.pi/4, np.pi/2, 3*np.pi/4]):
        """Extract texture features using GLCM and LBP"""
        texture_features = {}
        
        # Convert to uint8 for GLCM
        if image.dtype != np.uint8:
            image_uint8 = ((image - image.min()) / (image.max() - image.min()) * 255).astype(np.uint8)
        else:
            image_uint8 = image
        
        # Gray Level Co-occurrence Matrix (GLCM) features
        glcm = graycomatrix(image_uint8, distances=distances, angles=angles, 
                           levels=256, symmetric=True, normed=True)
        
        # GLCM properties
        glcm_features = {
            'contrast': graycoprops(glcm, 'contrast').mean(),
            'dissimilarity': graycoprops(glcm, 'dissimilarity').mean(),
            'homogeneity': graycoprops(glcm, 'homogeneity').mean(),
            'energy': graycoprops(glcm, 'energy').mean(),
            'correlation': graycoprops(glcm, 'correlation').mean(),
            'asm': graycoprops(glcm, 'ASM').mean()
        }
        
        texture_features.update(glcm_features)
        
        # Local Binary Pattern (LBP) features
        radius = 3
        n_points = 8 * radius
        lbp = local_binary_pattern(image_uint8, n_points, radius, method='uniform')
        
        # LBP histogram
        lbp_hist, _ = np.histogram(lbp.ravel(), bins=n_points + 2, 
                                  range=(0, n_points + 2), density=True)
        
        # LBP features
        lbp_features = {
            'lbp_uniformity': np.sum(lbp_hist ** 2),
            'lbp_entropy': -np.sum(lbp_hist * np.log2(lbp_hist + 1e-10))
        }
        
        texture_features.update(lbp_features)
        
        return texture_features
    
    def extract_shape_features(self, binary_mask):
        """Extract shape and morphological features"""
        shape_features = {}
        
        # Label connected components
        labeled_mask = label(binary_mask)
        props = regionprops(labeled_mask)
        
        if len(props) > 0:
            # Take the largest region
            largest_region = max(props, key=lambda x: x.area)
            
            # Basic shape features
            shape_features.update({
                'area': largest_region.area,
                'perimeter': largest_region.perimeter,
                'eccentricity': largest_region.eccentricity,
                'solidity': largest_region.solidity,
                'extent': largest_region.extent,
                'major_axis_length': largest_region.major_axis_length,
                'minor_axis_length': largest_region.minor_axis_length,
                'orientation': largest_region.orientation
            })
            
            # Calculated features
            area = largest_region.area
            perimeter = largest_region.perimeter
            
            # Compactness
            compactness = (perimeter ** 2) / (4 * np.pi * area) if area > 0 else 0
            
            # Circularity
            circularity = (4 * np.pi * area) / (perimeter ** 2) if perimeter > 0 else 0
            
            # Equivalent diameter
            equivalent_diameter = 2 * np.sqrt(area / np.pi)
            
            # Aspect ratio
            aspect_ratio = (largest_region.major_axis_length / 
                          largest_region.minor_axis_length if largest_region.minor_axis_length > 0 else 1)
            
            shape_features.update({
                'compactness': compactness,
                'circularity': circularity,
                'equivalent_diameter': equivalent_diameter,
                'aspect_ratio': aspect_ratio
            })
        
        return shape_features
    
    def extract_statistical_features(self, image, mask=None):
        """Extract statistical features from image intensity"""
        if mask is not None:
            intensities = image[mask > 0]
        else:
            intensities = image.flatten()
        
        if len(intensities) == 0:
            return {}
        
        # Basic statistical features
        statistical_features = {
            'mean': np.mean(intensities),
            'std': np.std(intensities),
            'variance': np.var(intensities),
            'min': np.min(intensities),
            'max': np.max(intensities),
            'range': np.ptp(intensities),
            'median': np.median(intensities),
            'q25': np.percentile(intensities, 25),
            'q75': np.percentile(intensities, 75),
            'iqr': np.percentile(intensities, 75) - np.percentile(intensities, 25)
        }
        
        # Higher-order moments
        statistical_features.update({
            'skewness': skew(intensities),
            'kurtosis': kurtosis(intensities)
        })
        
        # Entropy
        hist, _ = np.histogram(intensities, bins=256, density=True)
        entropy = -np.sum(hist * np.log2(hist + 1e-10))
        statistical_features['entropy'] = entropy
        
        return statistical_features
    
    def extract_comprehensive_features(self, image, binary_mask):
        """Extract all feature categories"""
        all_features = {}
        
        # Texture features
        texture_features = self.extract_texture_features(image)
        all_features.update({f'texture_{k}': v for k, v in texture_features.items()})
        
        # Shape features
        shape_features = self.extract_shape_features(binary_mask)
        all_features.update({f'shape_{k}': v for k, v in shape_features.items()})
        
        # Statistical features (within mask)
        statistical_features = self.extract_statistical_features(image, binary_mask)
        all_features.update({f'stats_{k}': v for k, v in statistical_features.items()})
        
        # Background statistical features
        background_mask = ~binary_mask.astype(bool)
        bg_features = self.extract_statistical_features(image, background_mask)
        all_features.update({f'bg_{k}': v for k, v in bg_features.items()})
        
        # Ratio features (tumor vs background)
        if all_features.get('stats_mean', 0) > 0 and all_features.get('bg_mean', 0) > 0:
            all_features['intensity_ratio'] = all_features['stats_mean'] / all_features['bg_mean']
        else:
            all_features['intensity_ratio'] = 0
        
        self.features = all_features
        self.feature_names = list(all_features.keys())
        
        return all_features
    
    def get_feature_vector(self):
        """Get feature vector as numpy array"""
        return np.array(list(self.features.values()))
    
    def get_feature_names(self):
        """Get feature names"""
        return self.feature_names
    
    def normalize_features(self, features_dict):
        """Normalize features to [0, 1] range"""
        normalized = {}
        
        for key, value in features_dict.items():
            if isinstance(value, (int, float)) and not np.isnan(value):
                # Min-max normalization (you might want to use global min/max)
                normalized[key] = value
            else:
                normalized[key] = 0
        
        return normalized

# Usage example
extractor = FeatureExtractor()
features = extractor.extract_comprehensive_features(mri_image, tumor_mask)
feature_vector = extractor.get_feature_vector()
feature_names = extractor.get_feature_names()`;

  const matlabCode = `classdef FeatureExtractor < handle
    properties
        features
        featureNames
    end
    
    methods
        function obj = FeatureExtractor()
            obj.features = containers.Map();
            obj.featureNames = {};
        end
        
        function textureFeatures = extractTextureFeatures(obj, image, distances, angles)
            % Extract texture features using GLCM and LBP
            if nargin < 3
                distances = [1];
            end
            if nargin < 4
                angles = [0, pi/4, pi/2, 3*pi/4];
            end
            
            textureFeatures = containers.Map();
            
            % Convert to uint8 for GLCM
            if ~isa(image, 'uint8')
                imageUint8 = uint8((image - min(image(:))) / (max(image(:)) - min(image(:))) * 255);
            else
                imageUint8 = image;
            end
            
            % Gray Level Co-occurrence Matrix (GLCM) features
            glcm = graycomatrix(imageUint8, 'Offset', [distances(1) 0; 0 distances(1)], ...
                              'Symmetric', true, 'NumLevels', 256);
            
            % GLCM properties
            contrast = mean(graycoprops(glcm, 'Contrast').Contrast);
            dissimilarity = mean(graycoprops(glcm, 'Dissimilarity').Dissimilarity);
            homogeneity = mean(graycoprops(glcm, 'Homogeneity').Homogeneity);
            energy = mean(graycoprops(glcm, 'Energy').Energy);
            correlation = mean(graycoprops(glcm, 'Correlation').Correlation);
            
            textureFeatures('contrast') = contrast;
            textureFeatures('dissimilarity') = dissimilarity;
            textureFeatures('homogeneity') = homogeneity;
            textureFeatures('energy') = energy;
            textureFeatures('correlation') = correlation;
            
            % Local Binary Pattern (LBP) features
            radius = 3;
            nPoints = 8 * radius;
            lbp = extractLBPFeatures(imageUint8, 'Radius', radius, 'NumNeighbors', nPoints);
            
            % LBP histogram
            lbpHist = histcounts(lbp(:), 'BinLimits', [0, nPoints+1], 'NumBins', nPoints+2);
            lbpHist = lbpHist / sum(lbpHist); % Normalize
            
            % LBP features
            lbpUniformity = sum(lbpHist.^2);
            lbpEntropy = -sum(lbpHist .* log2(lbpHist + eps));
            
            textureFeatures('lbp_uniformity') = lbpUniformity;
            textureFeatures('lbp_entropy') = lbpEntropy;
        end
        
        function shapeFeatures = extractShapeFeatures(obj, binaryMask)
            % Extract shape and morphological features
            shapeFeatures = containers.Map();
            
            % Label connected components
            labeledMask = bwlabel(binaryMask);
            props = regionprops(labeledMask, 'all');
            
            if ~isempty(props)
                % Take the largest region
                areas = [props.Area];
                [~, maxIdx] = max(areas);
                largestRegion = props(maxIdx);
                
                % Basic shape features
                shapeFeatures('area') = largestRegion.Area;
                shapeFeatures('perimeter') = largestRegion.Perimeter;
                shapeFeatures('eccentricity') = largestRegion.Eccentricity;
                shapeFeatures('solidity') = largestRegion.Solidity;
                shapeFeatures('extent') = largestRegion.Extent;
                shapeFeatures('major_axis_length') = largestRegion.MajorAxisLength;
                shapeFeatures('minor_axis_length') = largestRegion.MinorAxisLength;
                shapeFeatures('orientation') = largestRegion.Orientation;
                
                % Calculated features
                area = largestRegion.Area;
                perimeter = largestRegion.Perimeter;
                
                % Compactness
                compactness = (perimeter^2) / (4 * pi * area);
                
                % Circularity
                circularity = (4 * pi * area) / (perimeter^2);
                
                % Equivalent diameter
                equivalentDiameter = 2 * sqrt(area / pi);
                
                % Aspect ratio
                aspectRatio = largestRegion.MajorAxisLength / largestRegion.MinorAxisLength;
                
                shapeFeatures('compactness') = compactness;
                shapeFeatures('circularity') = circularity;
                shapeFeatures('equivalent_diameter') = equivalentDiameter;
                shapeFeatures('aspect_ratio') = aspectRatio;
            end
        end
        
        function statFeatures = extractStatisticalFeatures(obj, image, mask)
            % Extract statistical features from image intensity
            statFeatures = containers.Map();
            
            if nargin < 3
                intensities = image(:);
            else
                intensities = image(mask > 0);
            end
            
            if isempty(intensities)
                return;
            end
            
            % Basic statistical features
            statFeatures('mean') = mean(intensities);
            statFeatures('std') = std(intensities);
            statFeatures('variance') = var(intensities);
            statFeatures('min') = min(intensities);
            statFeatures('max') = max(intensities);
            statFeatures('range') = range(intensities);
            statFeatures('median') = median(intensities);
            statFeatures('q25') = prctile(intensities, 25);
            statFeatures('q75') = prctile(intensities, 75);
            statFeatures('iqr') = prctile(intensities, 75) - prctile(intensities, 25);
            
            % Higher-order moments
            statFeatures('skewness') = skewness(intensities);
            statFeatures('kurtosis') = kurtosis(intensities);
            
            % Entropy
            [counts, ~] = histcounts(intensities, 256);
            probs = counts / sum(counts);
            probs = probs(probs > 0); % Remove zeros
            entropy = -sum(probs .* log2(probs));
            statFeatures('entropy') = entropy;
        end
        
        function allFeatures = extractComprehensiveFeatures(obj, image, binaryMask)
            % Extract all feature categories
            allFeatures = containers.Map();
            
            % Texture features
            textureFeatures = obj.extractTextureFeatures(image);
            textureKeys = keys(textureFeatures);
            for i = 1:length(textureKeys)
                key = textureKeys{i};
                allFeatures(['texture_' key]) = textureFeatures(key);
            end
            
            % Shape features
            shapeFeatures = obj.extractShapeFeatures(binaryMask);
            shapeKeys = keys(shapeFeatures);
            for i = 1:length(shapeKeys)
                key = shapeKeys{i};
                allFeatures(['shape_' key]) = shapeFeatures(key);
            end
            
            % Statistical features (within mask)
            statFeatures = obj.extractStatisticalFeatures(image, binaryMask);
            statKeys = keys(statFeatures);
            for i = 1:length(statKeys)
                key = statKeys{i};
                allFeatures(['stats_' key]) = statFeatures(key);
            end
            
            % Background statistical features
            backgroundMask = ~binaryMask;
            bgFeatures = obj.extractStatisticalFeatures(image, backgroundMask);
            bgKeys = keys(bgFeatures);
            for i = 1:length(bgKeys)
                key = bgKeys{i};
                allFeatures(['bg_' key]) = bgFeatures(key);
            end
            
            % Ratio features
            if isKey(allFeatures, 'stats_mean') && isKey(allFeatures, 'bg_mean')
                if allFeatures('bg_mean') > 0
                    allFeatures('intensity_ratio') = allFeatures('stats_mean') / allFeatures('bg_mean');
                else
                    allFeatures('intensity_ratio') = 0;
                end
            end
            
            obj.features = allFeatures;
            obj.featureNames = keys(allFeatures);
        end
        
        function featureVector = getFeatureVector(obj)
            % Get feature vector as array
            featureVector = cell2mat(values(obj.features));
        end
        
        function featureNames = getFeatureNames(obj)
            % Get feature names
            featureNames = obj.featureNames;
        end
    end
end

% Usage example
extractor = FeatureExtractor();
allFeatures = extractor.extractComprehensiveFeatures(mriImage, tumorMask);
featureVector = extractor.getFeatureVector();
featureNames = extractor.getFeatureNames();`;

  const getFeatureData = () => {
    if (analysisResults?.features) {
      return analysisResults.features;
    }
    return {
      texture_features: {
        contrast: 0.523,
        dissimilarity: 0.421,
        homogeneity: 0.652,
        energy: 0.341,
        correlation: 0.724,
        lbp_uniformity: 0.432,
        lbp_entropy: 5.23
      },
      shape_features: {
        area: 1247,
        perimeter: 142.3,
        eccentricity: 0.634,
        solidity: 0.821,
        extent: 0.723,
        compactness: 1.35,
        circularity: 0.741,
        equivalent_diameter: 39.8
      },
      statistical_features: {
        mean: 0.523,
        std: 0.142,
        variance: 0.020,
        skewness: 0.832,
        kurtosis: 3.42,
        entropy: 6.23,
        range: 0.823,
        median: 0.501
      },
      summary_features: {
        avg_contrast: 0.523,
        avg_homogeneity: 0.652,
        avg_circularity: 0.741,
        avg_solidity: 0.821
      }
    };
  };

  const featureData = getFeatureData();
  const activeFeatures = featureCategories.find(cat => cat.id === activeCategory);

  return (
    <div className="features-tab">
      <div className="detail-header">
        <i className="fas fa-chart-bar detail-icon"></i>
        <div>
          <h3 className="detail-title">4. Advanced Feature Extraction</h3>
          <p className="detail-subtitle">
            Extract comprehensive texture, shape, and statistical features for tumor characterization
          </p>
        </div>
      </div>

      <div className="feature-categories">
        <div className="category-navigation">
          {featureCategories.map((category) => (
            <button
              key={category.id}
              className={`category-btn ${activeCategory === category.id ? 'active' : ''}`}
              onClick={() => setActiveCategory(category.id)}
            >
              <div className="category-icon" style={{ background: category.color }}>
                <i className={category.icon}></i>
              </div>
              <div className="category-info">
                <h4>{category.title}</h4>
                <p>{category.description}</p>
              </div>
            </button>
          ))}
        </div>

        <div className="feature-details">
          {activeFeatures && (
            <div className="feature-category-details">
              <div className="category-header">
                <div className="category-icon-large" style={{ background: activeFeatures.color }}>
                  <i className={activeFeatures.icon}></i>
                </div>
                <div>
                  <h3>{activeFeatures.title}</h3>
                  <p>{activeFeatures.description}</p>
                </div>
              </div>

              <div className="features-grid">
                {activeFeatures.features.map((feature, index) => (
                  <div key={index} className="feature-card">
                    <div className="feature-header">
                      <h4>{feature.name}</h4>
                      <span className="feature-formula">{feature.formula}</span>
                    </div>
                    <div className="feature-content">
                      <p>{feature.description}</p>
                      <div className="feature-value">
                        <span className="value-label">Current Value:</span>
                        <span className="value-number">
                          {featureData[`${activeCategory}_features`]?.[feature.name.toLowerCase()] || 
                           featureData[`${activeCategory}_features`]?.[feature.name.toLowerCase().replace(' ', '_')] || 
                           '0.000'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="feature-summary">
        <h4>Feature Extraction Summary</h4>
        <div className="summary-grid">
          <div className="summary-card">
            <div className="summary-icon">
              <i className="fas fa-grip-horizontal"></i>
            </div>
            <div className="summary-content">
              <div className="summary-value">
                {Object.keys(featureData.texture_features || {}).length}
              </div>
              <div className="summary-label">Texture Features</div>
            </div>
          </div>
          <div className="summary-card">
            <div className="summary-icon">
              <i className="fas fa-shapes"></i>
            </div>
            <div className="summary-content">
              <div className="summary-value">
                {Object.keys(featureData.shape_features || {}).length}
              </div>
              <div className="summary-label">Shape Features</div>
            </div>
          </div>
          <div className="summary-card">
            <div className="summary-icon">
              <i className="fas fa-chart-bar"></i>
            </div>
            <div className="summary-content">
              <div className="summary-value">
                {Object.keys(featureData.statistical_features || {}).length}
              </div>
              <div className="summary-label">Statistical Features</div>
            </div>
          </div>
          <div className="summary-card">
            <div className="summary-icon">
              <i className="fas fa-check-circle"></i>
            </div>
            <div className="summary-content">
              <div className="summary-value">
                {Object.keys(featureData.texture_features || {}).length + 
                 Object.keys(featureData.shape_features || {}).length + 
                 Object.keys(featureData.statistical_features || {}).length}
              </div>
              <div className="summary-label">Total Features</div>
            </div>
          </div>
        </div>
      </div>

      <div className="feature-visualization">
        <h4>Feature Visualization</h4>
        <div className="visualization-grid">
          <div className="viz-card">
            <div className="viz-header">
              <h5>Texture Analysis</h5>
            </div>
            <div className="viz-content">
              <div className="viz-placeholder">
                <i className="fas fa-grip-horizontal"></i>
                <span>GLCM Texture Map</span>
              </div>
            </div>
          </div>
          <div className="viz-card">
            <div className="viz-header">
              <h5>Shape Analysis</h5>
            </div>
            <div className="viz-content">
              <div className="viz-placeholder">
                <i className="fas fa-shapes"></i>
                <span>Morphological Features</span>
              </div>
            </div>
          </div>
          <div className="viz-card">
            <div className="viz-header">
              <h5>Statistical Distribution</h5>
            </div>
            <div className="viz-content">
              <div className="viz-placeholder">
                <i className="fas fa-chart-bar"></i>
                <span>Intensity Histogram</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="code-section">
        <div className="code-header">
          <h4>Feature Extraction Implementation</h4>
          <div className="code-tabs">
            <button 
              className={`code-tab ${codeLanguage === 'python' ? 'active' : ''}`}
              onClick={() => setCodeLanguage('python')}
            >
              Python
            </button>
            <button 
              className={`code-tab ${codeLanguage === 'matlab' ? 'active' : ''}`}
              onClick={() => setCodeLanguage('matlab')}
            >
              MATLAB
            </button>
          </div>
        </div>
        <CodeBlock 
          code={codeLanguage === 'python' ? pythonCode : matlabCode} 
          language={codeLanguage}
        />
      </div>

      <style jsx>{`
        .features-tab {
          padding: 20px 0;
        }

        .detail-header {
          display: flex;
          align-items: center;
          gap: 16px;
          margin-bottom: 40px;
          padding-bottom: 20px;
          border-bottom: 2px solid var(--border-color);
        }

        .detail-icon {
          font-size: 32px;
          color: var(--primary-color);
        }

        .detail-title {
          font-size: 24px;
          font-weight: 600;
          color: var(--text-primary);
          margin-bottom: 8px;
        }

        .detail-subtitle {
          color: var(--text-secondary);
          font-size: 16px;
        }

        .feature-categories {
          display: grid;
          grid-template-columns: 300px 1fr;
          gap: 40px;
          margin-bottom: 40px;
        }

        .category-navigation {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .category-btn {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 20px;
          border: 2px solid var(--border-color);
          background: white;
          border-radius: var(--border-radius);
          cursor: pointer;
          transition: var(--transition);
          text-align: left;
        }

        .category-btn.active {
          border-color: var(--primary-color);
          background: rgba(102, 126, 234, 0.05);
        }

        .category-btn:hover:not(.active) {
          border-color: var(--primary-color);
        }

        .category-icon {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 20px;
        }

        .category-info h4 {
          margin: 0 0 4px 0;
          color: var(--text-primary);
        }

        .category-info p {
          margin: 0;
          color: var(--text-secondary);
          font-size: 14px;
        }

        .category-header {
          display: flex;
          align-items: center;
          gap: 20px;
          margin-bottom: 30px;
        }

        .category-icon-large {
          width: 64px;
          height: 64px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 28px;
        }

        .category-header h3 {
          margin: 0 0 8px 0;
          color: var(--text-primary);
        }

        .category-header p {
          margin: 0;
          color: var(--text-secondary);
        }

        .features-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 20px;
        }

        .feature-card {
          background: white;
          border-radius: var(--border-radius);
          padding: 20px;
          box-shadow: var(--shadow-light);
          transition: var(--transition);
        }

        .feature-card:hover {
          box-shadow: var(--shadow-medium);
          transform: translateY(-2px);
        }

        .feature-header {
          margin-bottom: 12px;
        }

        .feature-header h4 {
          margin: 0 0 4px 0;
          color: var(--text-primary);
        }

        .feature-formula {
          font-family: monospace;
          font-size: 12px;
          color: var(--text-secondary);
          background: var(--background-dark);
          padding: 2px 6px;
          border-radius: 4px;
        }

        .feature-content p {
          margin: 0 0 12px 0;
          color: var(--text-secondary);
          font-size: 14px;
          line-height: 1.4;
        }

        .feature-value {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-top: 1px solid var(--border-color);
        }

        .value-label {
          color: var(--text-secondary);
          font-size: 12px;
        }

        .value-number {
          color: var(--primary-color);
          font-weight: 600;
          font-family: monospace;
        }

        .feature-summary {
          margin-bottom: 40px;
        }

        .feature-summary h4 {
          margin-bottom: 20px;
          color: var(--text-primary);
        }

        .summary-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 20px;
        }

        .summary-card {
          background: white;
          border-radius: var(--border-radius);
          padding: 20px;
          box-shadow: var(--shadow-light);
          display: flex;
          align-items: center;
          gap: 16px;
        }

        .summary-icon {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 18px;
        }

        .summary-value {
          font-size: 24px;
          font-weight: 700;
          color: var(--primary-color);
          margin-bottom: 4px;
        }

        .summary-label {
          color: var(--text-secondary);
          font-size: 12px;
          font-weight: 500;
        }

        .feature-visualization {
          margin-bottom: 40px;
        }

        .feature-visualization h4 {
          margin-bottom: 20px;
          color: var(--text-primary);
        }

        .visualization-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 20px;
        }

        .viz-card {
          background: white;
          border-radius: var(--border-radius);
          box-shadow: var(--shadow-light);
          overflow: hidden;
        }

        .viz-header {
          padding: 16px;
          background: var(--background-dark);
          border-bottom: 1px solid var(--border-color);
        }

        .viz-header h5 {
          margin: 0;
          color: var(--text-primary);
        }

        .viz-content {
          padding: 20px;
        }

        .viz-placeholder {
          width: 100%;
          height: 120px;
          background: var(--background-dark);
          border-radius: var(--border-radius);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          border: 2px dashed var(--border-color);
        }

        .viz-placeholder i {
          font-size: 24px;
          color: var(--primary-color);
          margin-bottom: 8px;
        }

        .viz-placeholder span {
          color: var(--text-secondary);
          font-size: 14px;
        }

        .code-section {
          background: white;
          border-radius: var(--border-radius);
          overflow: hidden;
          box-shadow: var(--shadow-light);
        }

        .code-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20px;
          border-bottom: 1px solid var(--border-color);
        }

        .code-header h4 {
          margin: 0;
          color: var(--text-primary);
        }

        .code-tabs {
          display: flex;
          gap: 8px;
        }

        .code-tab {
          padding: 8px 16px;
          border: 1px solid var(--border-color);
          background: white;
          cursor: pointer;
          border-radius: var(--border-radius);
          font-size: 14px;
          transition: var(--transition);
        }

        .code-tab.active {
          background: var(--primary-color);
          color: white;
          border-color: var(--primary-color);
        }

        @media (max-width: 1024px) {
          .feature-categories {
            grid-template-columns: 1fr;
          }

          .category-navigation {
            flex-direction: row;
            overflow-x: auto;
          }

          .category-btn {
            min-width: 280px;
          }
        }

        @media (max-width: 768px) {
          .category-navigation {
            flex-direction: column;
          }

          .category-btn {
            min-width: auto;
          }
        }
      `}</style>
    </div>
  );
};

export default FeaturesTab;