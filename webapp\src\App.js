import React, { useState } from 'react';
import axios from 'axios';
import './App.css';

// Tab components
import OverviewTab from './components/tabs/OverviewTab';
import DicomTab from './components/tabs/DicomTab';
import PreprocessingTab from './components/tabs/PreprocessingTab';
import SegmentationTab from './components/tabs/SegmentationTab';
import FeaturesTab from './components/tabs/FeaturesTab';
import ReconstructionTab from './components/tabs/ReconstructionTab';
import ResultsTab from './components/tabs/ResultsTab';

function App() {
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [analysisResults, setAnalysisResults] = useState(null);
  const [processingStatus, setProcessingStatus] = useState('idle');
  const [activeTab, setActiveTab] = useState('overview');

  const tabs = [
    { id: 'overview', title: 'Overview', icon: 'fas fa-home' },
    { id: 'dicom', title: 'DICOM Loading', icon: 'fas fa-database' },
    { id: 'preprocessing', title: 'Preprocessing', icon: 'fas fa-cog' },
    { id: 'segmentation', title: 'Segmentation', icon: 'fas fa-puzzle-piece' },
    { id: 'features', title: 'Features', icon: 'fas fa-chart-bar' },
    { id: 'reconstruction', title: '3D Reconstruction', icon: 'fas fa-cube' },
    { id: 'results', title: 'Results', icon: 'fas fa-chart-line' }
  ];

  const handleFileSelect = (event) => {
    const files = Array.from(event.target.files);
    setSelectedFiles(files);
  };

  const handleAnalyze = async () => {
    if (selectedFiles.length === 0) {
      alert('Please select DICOM files first');
      return;
    }

    setProcessingStatus('processing');
    
    const formData = new FormData();
    selectedFiles.forEach(file => {
      formData.append('files', file);
    });

    try {
      const response = await axios.post('http://localhost:8000/analyze', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      setAnalysisResults(response.data);
      setProcessingStatus('completed');
      setActiveTab('results');
    } catch (error) {
      console.error('Analysis failed:', error);
      setProcessingStatus('error');
    }
  };

  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
  };

  const renderActiveTab = () => {
    switch (activeTab) {
      case 'overview':
        return <OverviewTab onTabChange={handleTabChange} />;
      case 'dicom':
        return <DicomTab analysisResults={analysisResults} />;
      case 'preprocessing':
        return <PreprocessingTab analysisResults={analysisResults} />;
      case 'segmentation':
        return <SegmentationTab analysisResults={analysisResults} />;
      case 'features':
        return <FeaturesTab analysisResults={analysisResults} />;
      case 'reconstruction':
        return <ReconstructionTab analysisResults={analysisResults} />;
      case 'results':
        return <ResultsTab analysisResults={analysisResults} processingStatus={processingStatus} />;
      default:
        return <OverviewTab onTabChange={handleTabChange} />;
    }
  };

  return (
    <div className="App">
      <header className="App-header">
        <div className="header-content">
          <div className="header-left">
            <h1>Brain Tumor Detection in 3D MRI Images</h1>
            <p>Advanced Medical Image Analysis System</p>
          </div>
          <div className="header-right">
            <div className="upload-section">
              <input
                type="file"
                multiple
                accept=".dcm,.dicom"
                onChange={handleFileSelect}
                className="file-input"
                id="file-upload"
              />
              <label htmlFor="file-upload" className="file-label">
                <i className="fas fa-upload"></i>
                {selectedFiles.length > 0 ? `${selectedFiles.length} files selected` : 'Select DICOM Files'}
              </label>
              <button 
                onClick={handleAnalyze}
                disabled={processingStatus === 'processing' || selectedFiles.length === 0}
                className="analyze-button"
              >
                {processingStatus === 'processing' ? (
                  <>
                    <i className="fas fa-spinner fa-spin"></i>
                    Processing...
                  </>
                ) : (
                  <>
                    <i className="fas fa-play"></i>
                    Analyze
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </header>

      <nav className="tab-navigation">
        <div className="tabs-container">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
              onClick={() => handleTabChange(tab.id)}
            >
              <i className={tab.icon}></i>
              <span>{tab.title}</span>
            </button>
          ))}
        </div>
      </nav>

      <main className="main-content">
        <div className="tab-content">
          {renderActiveTab()}
        </div>
      </main>

      <footer className="App-footer">
        <p>&copy; 2024 Brain Tumor Detection System. Advanced Medical Image Analysis.</p>
      </footer>
    </div>
  );
}

export default App;
