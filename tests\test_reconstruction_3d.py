"""
Test Suite for 3D Reconstruction Module
======================================

Comprehensive tests for the 3D reconstruction components.

Author: Dr. <PERSON>smail
"""

import unittest
import numpy as np
import sys
import os
import tempfile
import shutil

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from reconstruction_3d import (
    VolumeInterpolator, IsotropicVoxelCreator, OrthogonalViews,
    SurfaceRenderer, VolumeVisualizer, Reconstruction3D
)


class TestVolumeInterpolator(unittest.TestCase):
    """Test volume interpolation functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.interpolator = VolumeInterpolator()
        
        # Create test slices
        self.slices = []
        for i in range(5):
            slice_img = np.random.rand(32, 32) * 255
            # Add some pattern for testing
            slice_img[10:20, 10:20] = 200 + i * 10
            self.slices.append(slice_img)
    
    def test_linear_interpolation(self):
        """Test linear interpolation."""
        interpolated = self.interpolator.interpolate_volume(
            self.slices, slice_spacing=2.0, target_spacing=1.0, axis=0)
        
        # Check that volume was interpolated
        self.assertGreater(interpolated.shape[0], len(self.slices))
        self.assertEqual(interpolated.shape[1:], self.slices[0].shape)
        
        # Check that interpolated volume contains reasonable values
        self.assertGreaterEqual(np.min(interpolated), 0)
        self.assertLessEqual(np.max(interpolated), 255)
    
    def test_cubic_interpolation(self):
        """Test cubic interpolation."""
        self.interpolator.method = 'cubic'
        interpolated = self.interpolator.interpolate_volume(
            self.slices, slice_spacing=2.0, target_spacing=1.0, axis=0)
        
        # Check basic properties
        self.assertGreater(interpolated.shape[0], len(self.slices))
        self.assertEqual(interpolated.shape[1:], self.slices[0].shape)
        
        # Check that values are reasonable
        self.assertFalse(np.any(np.isnan(interpolated)))
        self.assertFalse(np.any(np.isinf(interpolated)))
    
    def test_nearest_interpolation(self):
        """Test nearest neighbor interpolation."""
        self.interpolator.method = 'nearest'
        interpolated = self.interpolator.interpolate_volume(
            self.slices, slice_spacing=2.0, target_spacing=1.0, axis=0)
        
        # Check basic properties
        self.assertGreater(interpolated.shape[0], len(self.slices))
        self.assertEqual(interpolated.shape[1:], self.slices[0].shape)
        
        # Check that values are from original slices (nearest neighbor property)
        original_values = set()
        for slice_img in self.slices:
            original_values.update(slice_img.flatten())
        
        # Most values should be from original slices
        interpolated_values = set(interpolated.flatten())
        common_values = original_values.intersection(interpolated_values)
        self.assertGreater(len(common_values), 0)
    
    def test_smoothing(self):
        """Test volume smoothing."""
        # Create test volume
        volume = np.random.rand(10, 32, 32) * 255
        
        # Apply smoothing
        smoothed = self.interpolator.apply_smoothing(volume, sigma=1.0)
        
        # Check that smoothed volume has same shape
        self.assertEqual(smoothed.shape, volume.shape)
        
        # Check that smoothing reduced variance (generally true)
        original_var = np.var(volume)
        smoothed_var = np.var(smoothed)
        self.assertLess(smoothed_var, original_var)
    
    def test_insufficient_slices(self):
        """Test behavior with insufficient slices."""
        with self.assertRaises(ValueError):
            self.interpolator.interpolate_volume(
                [self.slices[0]], slice_spacing=2.0, target_spacing=1.0)
    
    def test_invalid_method(self):
        """Test invalid interpolation method."""
        self.interpolator.method = 'invalid'
        with self.assertRaises(ValueError):
            self.interpolator.interpolate_volume(
                self.slices, slice_spacing=2.0, target_spacing=1.0)


class TestIsotropicVoxelCreator(unittest.TestCase):
    """Test isotropic voxel creation."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.creator = IsotropicVoxelCreator()
        
        # Create test volume
        self.volume = np.random.rand(10, 32, 32) * 255
        
        # Create test mask
        self.mask = np.zeros((10, 32, 32))
        self.mask[2:8, 10:20, 10:20] = 1
    
    def test_create_isotropic_volume(self):
        """Test isotropic volume creation."""
        original_spacing = (2.0, 1.0, 1.0)
        target_spacing = 1.0
        
        isotropic = self.creator.create_isotropic_volume(
            self.volume, original_spacing, target_spacing)
        
        # Check that volume was resized appropriately
        expected_shape = (
            int(self.volume.shape[0] * original_spacing[0] / target_spacing),
            int(self.volume.shape[1] * original_spacing[1] / target_spacing),
            int(self.volume.shape[2] * original_spacing[2] / target_spacing)
        )
        
        # Allow for small rounding differences
        for i in range(3):
            self.assertAlmostEqual(isotropic.shape[i], expected_shape[i], delta=1)
        
        # Check that spacing was stored
        self.assertEqual(self.creator.target_spacing, target_spacing)
    
    def test_resample_to_spacing(self):
        """Test resampling to specific spacing."""
        current_spacing = (2.0, 1.0, 1.0)
        target_spacing = (1.0, 1.0, 1.0)
        
        resampled = self.creator.resample_to_spacing(
            self.volume, current_spacing, target_spacing)
        
        # Check that first dimension was doubled (2.0 -> 1.0)
        self.assertAlmostEqual(resampled.shape[0], self.volume.shape[0] * 2, delta=1)
        
        # Other dimensions should stay the same
        self.assertEqual(resampled.shape[1:], self.volume.shape[1:])
    
    def test_calculate_volume_metrics(self):
        """Test volume metrics calculation."""
        spacing = (1.0, 1.0, 1.0)
        metrics = self.creator.calculate_volume_metrics(self.mask, spacing)
        
        # Check that metrics contain expected keys
        expected_keys = ['total_volume_mm3', 'total_volume_ml', 'surface_area_mm2',
                        'bounding_box_min', 'bounding_box_max', 'bounding_box_size',
                        'voxel_count', 'voxel_volume_mm3']
        
        for key in expected_keys:
            self.assertIn(key, metrics)
        
        # Check that metrics are reasonable
        self.assertGreater(metrics['total_volume_mm3'], 0)
        self.assertGreater(metrics['voxel_count'], 0)
        self.assertEqual(metrics['voxel_volume_mm3'], 1.0)  # 1x1x1 spacing
        
        # Check volume calculation
        expected_volume = np.sum(self.mask) * 1.0  # 1 mm³ per voxel
        self.assertEqual(metrics['total_volume_mm3'], expected_volume)
    
    def test_auto_spacing(self):
        """Test automatic spacing determination."""
        original_spacing = (3.0, 1.5, 1.0)
        
        # Without target spacing (should use minimum)
        isotropic = self.creator.create_isotropic_volume(
            self.volume, original_spacing)
        
        # Should use minimum spacing (1.0)
        self.assertEqual(self.creator.target_spacing, min(original_spacing))


class TestOrthogonalViews(unittest.TestCase):
    """Test orthogonal views generation."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.viewer = OrthogonalViews()
        
        # Create test volume
        self.volume = np.random.rand(20, 32, 32) * 255
        
        # Add some structure for testing
        self.volume[8:12, 14:18, 14:18] = 200
    
    def test_generate_orthogonal_views(self):
        """Test orthogonal views generation."""
        views = self.viewer.generate_orthogonal_views(self.volume)
        
        # Check that all views are generated
        expected_views = ['axial', 'sagittal', 'coronal']
        for view in expected_views:
            self.assertIn(view, views)
        
        # Check view shapes
        self.assertEqual(views['axial'].shape, (32, 32))
        self.assertEqual(views['sagittal'].shape, (20, 32))
        self.assertEqual(views['coronal'].shape, (20, 32))
    
    def test_custom_slice_indices(self):
        """Test custom slice indices."""
        slice_indices = {
            'axial': 5,
            'sagittal': 10,
            'coronal': 15
        }
        
        views = self.viewer.generate_orthogonal_views(self.volume, slice_indices)
        
        # Check that correct slices were extracted
        np.testing.assert_array_equal(views['axial'], self.volume[5, :, :])
        np.testing.assert_array_equal(views['sagittal'], self.volume[:, :, 10])
        np.testing.assert_array_equal(views['coronal'], self.volume[:, 15, :])
    
    def test_mip_views(self):
        """Test Maximum Intensity Projection views."""
        mip_views = self.viewer.create_mip_views(self.volume)
        
        # Check that MIP views are generated
        expected_views = ['axial_mip', 'sagittal_mip', 'coronal_mip']
        for view in expected_views:
            self.assertIn(view, mip_views)
        
        # Check that MIP gives maximum values
        axial_mip = mip_views['axial_mip']
        manual_mip = np.max(self.volume, axis=0)
        np.testing.assert_array_equal(axial_mip, manual_mip)
    
    def test_mean_views(self):
        """Test mean intensity views."""
        mean_views = self.viewer.create_mean_views(self.volume)
        
        # Check that mean views are generated
        expected_views = ['axial_mean', 'sagittal_mean', 'coronal_mean']
        for view in expected_views:
            self.assertIn(view, mean_views)
        
        # Check that mean gives average values
        axial_mean = mean_views['axial_mean']
        manual_mean = np.mean(self.volume, axis=0)
        np.testing.assert_array_equal(axial_mean, manual_mean)


class TestSurfaceRenderer(unittest.TestCase):
    """Test surface rendering functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.renderer = SurfaceRenderer()
        
        # Create test volume with simple shape
        self.volume = np.zeros((20, 20, 20))
        
        # Create sphere-like structure
        center = (10, 10, 10)
        radius = 5
        
        for i in range(20):
            for j in range(20):
                for k in range(20):
                    distance = np.sqrt((i-center[0])**2 + (j-center[1])**2 + (k-center[2])**2)
                    if distance <= radius:
                        self.volume[i, j, k] = 1.0
    
    def test_extract_surface(self):
        """Test surface extraction."""
        vertices, faces, normals, values = self.renderer.extract_surface(
            self.volume, level=0.5)
        
        # Check that surface was extracted
        self.assertGreater(len(vertices), 0)
        self.assertGreater(len(faces), 0)
        self.assertGreater(len(normals), 0)
        self.assertGreater(len(values), 0)
        
        # Check that arrays have correct dimensions
        self.assertEqual(vertices.shape[1], 3)  # 3D coordinates
        self.assertEqual(faces.shape[1], 3)     # Triangular faces
        self.assertEqual(normals.shape[1], 3)   # 3D normals
        
        # Check that face indices are valid
        self.assertLess(np.max(faces), len(vertices))
        self.assertGreaterEqual(np.min(faces), 0)
    
    def test_calculate_surface_metrics(self):
        """Test surface metrics calculation."""
        metrics = self.renderer.calculate_surface_metrics(self.volume, level=0.5)
        
        # Check that metrics contain expected keys
        expected_keys = ['surface_area_mm2', 'convex_volume_mm3', 'n_vertices',
                        'n_faces', 'bounding_box_min', 'bounding_box_max',
                        'bounding_box_size', 'centroid']
        
        for key in expected_keys:
            self.assertIn(key, metrics)
        
        # Check that metrics are reasonable
        self.assertGreater(metrics['surface_area_mm2'], 0)
        self.assertGreater(metrics['n_vertices'], 0)
        self.assertGreater(metrics['n_faces'], 0)
        
        # Check that centroid is near volume center
        centroid = np.array(metrics['centroid'])
        expected_centroid = np.array([10, 10, 10])  # Center of sphere
        distance = np.linalg.norm(centroid - expected_centroid)
        self.assertLess(distance, 2.0)  # Should be close to center
    
    def test_with_spacing(self):
        """Test surface extraction with custom spacing."""
        spacing = (2.0, 1.0, 1.0)
        vertices, faces, normals, values = self.renderer.extract_surface(
            self.volume, level=0.5, spacing=spacing)
        
        # Check that vertices are scaled according to spacing
        self.assertGreater(len(vertices), 0)
        
        # Check that z-coordinates are scaled by 2.0
        max_z = np.max(vertices[:, 0])
        self.assertGreater(max_z, 20)  # Should be scaled
    
    def test_empty_volume(self):
        """Test behavior with empty volume."""
        empty_volume = np.zeros((10, 10, 10))
        
        # Should handle empty volume gracefully
        try:
            vertices, faces, normals, values = self.renderer.extract_surface(
                empty_volume, level=0.5)
            # If no exception, check that arrays are empty
            self.assertEqual(len(vertices), 0)
            self.assertEqual(len(faces), 0)
        except:
            # Exception is also acceptable for empty volume
            pass


class TestReconstruction3D(unittest.TestCase):
    """Test complete 3D reconstruction pipeline."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.reconstructor = Reconstruction3D()
        
        # Create test slices
        self.slices = []
        for i in range(10):
            slice_img = np.random.rand(32, 32) * 255
            # Add consistent structure
            slice_img[10:20, 10:20] = 200
            self.slices.append(slice_img)
    
    def test_reconstruct_from_slices(self):
        """Test complete reconstruction from slices."""
        reconstructed = self.reconstructor.reconstruct_from_slices(
            slices=self.slices,
            slice_spacing=2.0,
            pixel_spacing=(1.0, 1.0),
            target_isotropic_spacing=1.0,
            interpolation_method='linear',
            smoothing_sigma=0.5
        )
        
        # Check that reconstruction was successful
        self.assertIsNotNone(reconstructed)
        self.assertEqual(len(reconstructed.shape), 3)
        
        # Check that reconstruction info was stored
        self.assertIn('reconstruction_info', self.reconstructor.__dict__)
        
        # Check that final volume is larger than original (due to interpolation)
        self.assertGreater(reconstructed.shape[0], len(self.slices))
    
    def test_analyze_reconstruction(self):
        """Test reconstruction analysis."""
        # First perform reconstruction
        reconstructed = self.reconstructor.reconstruct_from_slices(
            slices=self.slices,
            slice_spacing=2.0,
            pixel_spacing=(1.0, 1.0),
            target_isotropic_spacing=1.0
        )
        
        # Analyze reconstruction
        analysis = self.reconstructor.analyze_reconstruction()
        
        # Check that analysis contains expected sections
        expected_sections = ['reconstruction_info', 'volume_metrics', 
                           'surface_metrics', 'volume_statistics']
        for section in expected_sections:
            self.assertIn(section, analysis)
        
        # Check reconstruction info
        recon_info = analysis['reconstruction_info']
        self.assertEqual(recon_info['original_slices'], len(self.slices))
        self.assertEqual(recon_info['original_slice_spacing'], 2.0)
        self.assertEqual(recon_info['target_isotropic_spacing'], 1.0)
        
        # Check that metrics are reasonable
        volume_metrics = analysis['volume_metrics']
        self.assertGreater(volume_metrics['total_volume_mm3'], 0)
        self.assertGreater(volume_metrics['voxel_count'], 0)
    
    def test_export_reconstruction_data(self):
        """Test data export functionality."""
        # Create temporary directory
        temp_dir = tempfile.mkdtemp()
        
        try:
            # Perform reconstruction
            reconstructed = self.reconstructor.reconstruct_from_slices(
                slices=self.slices,
                slice_spacing=2.0,
                pixel_spacing=(1.0, 1.0),
                target_isotropic_spacing=1.0
            )
            
            # Export data
            export_path = os.path.join(temp_dir, "test_reconstruction")
            self.reconstructor.export_reconstruction_data(export_path)
            
            # Check that files were created
            self.assertTrue(os.path.exists(export_path + "_volume.npy"))
            self.assertTrue(os.path.exists(export_path + "_analysis.json"))
            
            # Check that volume can be loaded
            loaded_volume = np.load(export_path + "_volume.npy")
            self.assertEqual(loaded_volume.shape, reconstructed.shape)
            
        finally:
            # Clean up temporary directory
            shutil.rmtree(temp_dir)
    
    def test_invalid_parameters(self):
        """Test behavior with invalid parameters."""
        # Test with insufficient slices
        with self.assertRaises(ValueError):
            self.reconstructor.reconstruct_from_slices(
                slices=[self.slices[0]],  # Only one slice
                slice_spacing=2.0,
                pixel_spacing=(1.0, 1.0)
            )
        
        # Test with invalid interpolation method
        with self.assertRaises(ValueError):
            self.reconstructor.reconstruct_from_slices(
                slices=self.slices,
                slice_spacing=2.0,
                pixel_spacing=(1.0, 1.0),
                interpolation_method='invalid_method'
            )
    
    def test_reconstruction_consistency(self):
        """Test that reconstruction is consistent."""
        # Perform reconstruction twice with same parameters
        reconstructed1 = self.reconstructor.reconstruct_from_slices(
            slices=self.slices,
            slice_spacing=2.0,
            pixel_spacing=(1.0, 1.0),
            target_isotropic_spacing=1.0,
            interpolation_method='linear',
            smoothing_sigma=0.5
        )
        
        reconstructed2 = self.reconstructor.reconstruct_from_slices(
            slices=self.slices,
            slice_spacing=2.0,
            pixel_spacing=(1.0, 1.0),
            target_isotropic_spacing=1.0,
            interpolation_method='linear',
            smoothing_sigma=0.5
        )
        
        # Results should be identical
        np.testing.assert_array_almost_equal(reconstructed1, reconstructed2)


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete 3D reconstruction pipeline."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create realistic test data
        self.slices = self.create_test_tumor_slices()
        self.masks = self.create_test_tumor_masks()
    
    def create_test_tumor_slices(self):
        """Create realistic tumor slices for testing."""
        slices = []
        
        for i in range(15):
            # Create base brain tissue
            slice_img = np.random.normal(100, 20, (64, 64))
            slice_img = np.clip(slice_img, 0, 255)
            
            # Add tumor (sphere-like)
            center = (32, 32)
            radius = 8 + np.sin(i * 0.3) * 2
            
            y, x = np.ogrid[:64, :64]
            mask = (x - center[0])**2 + (y - center[1])**2 <= radius**2
            
            slice_img[mask] = 180 + np.random.normal(0, 10, np.sum(mask))
            
            slices.append(slice_img)
        
        return slices
    
    def create_test_tumor_masks(self):
        """Create corresponding tumor masks."""
        masks = []
        
        for i in range(15):
            mask = np.zeros((64, 64))
            
            # Create tumor mask
            center = (32, 32)
            radius = 8 + np.sin(i * 0.3) * 2
            
            y, x = np.ogrid[:64, :64]
            tumor_mask = (x - center[0])**2 + (y - center[1])**2 <= radius**2
            
            mask[tumor_mask] = 1
            masks.append(mask)
        
        return masks
    
    def test_complete_pipeline(self):
        """Test the complete reconstruction pipeline."""
        # Initialize reconstructor
        reconstructor = Reconstruction3D()
        
        # Perform reconstruction
        reconstructed_volume = reconstructor.reconstruct_from_slices(
            slices=self.slices,
            slice_spacing=2.0,
            pixel_spacing=(1.0, 1.0),
            target_isotropic_spacing=1.0,
            interpolation_method='linear',
            smoothing_sigma=0.5
        )
        
        # Check reconstruction
        self.assertIsNotNone(reconstructed_volume)
        self.assertEqual(len(reconstructed_volume.shape), 3)
        
        # Analyze reconstruction
        analysis = reconstructor.analyze_reconstruction()
        
        # Check analysis results
        self.assertIn('reconstruction_info', analysis)
        self.assertIn('volume_metrics', analysis)
        self.assertIn('surface_metrics', analysis)
        
        # Check that metrics are reasonable
        volume_metrics = analysis['volume_metrics']
        self.assertGreater(volume_metrics['total_volume_ml'], 0)
        self.assertGreater(volume_metrics['voxel_count'], 0)
        
        surface_metrics = analysis['surface_metrics']
        self.assertGreater(surface_metrics['surface_area_mm2'], 0)
        self.assertGreater(surface_metrics['n_vertices'], 0)
    
    def test_different_interpolation_methods(self):
        """Test different interpolation methods."""
        methods = ['linear', 'cubic', 'nearest']
        results = {}
        
        for method in methods:
            reconstructor = Reconstruction3D()
            
            try:
                reconstructed = reconstructor.reconstruct_from_slices(
                    slices=self.slices,
                    slice_spacing=2.0,
                    pixel_spacing=(1.0, 1.0),
                    target_isotropic_spacing=1.0,
                    interpolation_method=method,
                    smoothing_sigma=0.5
                )
                
                analysis = reconstructor.analyze_reconstruction()
                results[method] = analysis
                
            except Exception as e:
                self.fail(f"Method {method} failed: {e}")
        
        # Check that all methods produced results
        self.assertEqual(len(results), len(methods))
        
        # Check that results are different (methods should produce different outputs)
        volumes = [results[method]['volume_metrics']['total_volume_ml'] for method in methods]
        self.assertGreater(len(set(volumes)), 1)  # Should have different volumes
    
    def test_different_spacings(self):
        """Test different spacing parameters."""
        spacings = [0.5, 1.0, 2.0]
        results = {}
        
        for spacing in spacings:
            reconstructor = Reconstruction3D()
            
            reconstructed = reconstructor.reconstruct_from_slices(
                slices=self.slices,
                slice_spacing=2.0,
                pixel_spacing=(1.0, 1.0),
                target_isotropic_spacing=spacing,
                interpolation_method='linear',
                smoothing_sigma=0.5
            )
            
            analysis = reconstructor.analyze_reconstruction()
            results[spacing] = {
                'volume_shape': reconstructed.shape,
                'volume_ml': analysis['volume_metrics']['total_volume_ml']
            }
        
        # Check that different spacings produce different volume shapes
        shapes = [results[s]['volume_shape'] for s in spacings]
        self.assertGreater(len(set(shapes)), 1)  # Should have different shapes
    
    def test_mask_reconstruction(self):
        """Test reconstruction with binary masks."""
        # Create binary mask volume
        mask_volume = np.stack(self.masks, axis=0)
        
        # Test surface extraction
        renderer = SurfaceRenderer()
        vertices, faces, normals, values = renderer.extract_surface(
            mask_volume, level=0.5)
        
        # Check that surface was extracted
        self.assertGreater(len(vertices), 0)
        self.assertGreater(len(faces), 0)
        
        # Test metrics calculation
        metrics = renderer.calculate_surface_metrics(mask_volume)
        
        # Check that metrics are reasonable
        self.assertGreater(metrics['surface_area_mm2'], 0)
        self.assertGreater(metrics['n_vertices'], 0)


def run_all_tests():
    """Run all tests."""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestVolumeInterpolator,
        TestIsotropicVoxelCreator,
        TestOrthogonalViews,
        TestSurfaceRenderer,
        TestReconstruction3D,
        TestIntegration
    ]
    
    for test_class in test_classes:
        test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(test_class))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result


if __name__ == '__main__':
    print("Running 3D Reconstruction Module Tests")
    print("=" * 50)
    
    result = run_all_tests()
    
    print("\n" + "=" * 50)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, failure in result.failures:
            print(f"- {test}: {failure}")
    
    if result.errors:
        print("\nErrors:")
        for test, error in result.errors:
            print(f"- {test}: {error}")
    
    if result.wasSuccessful():
        print("\nAll tests passed successfully! ✅")
    else:
        print("\nSome tests failed. ❌")