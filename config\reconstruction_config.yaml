# 3D Reconstruction Configuration
# Author: <PERSON><PERSON> Esmail

# General Settings
general:
  random_seed: 42
  verbose: true
  enable_gpu: false  # Enable GPU acceleration if available
  memory_limit_gb: 8  # Memory limit for large volumes

# Volume Interpolation Settings
interpolation:
  method: 'linear'  # Options: 'linear', 'cubic', 'nearest'
  target_spacing: 1.0  # Target spacing between slices (mm)
  
  # Method-specific parameters
  linear:
    bounds_error: false
    fill_value: 'extrapolate'
    
  cubic:
    bounds_error: false
    fill_value: 'extrapolate'
    min_points: 4  # Minimum points required for cubic interpolation
    
  nearest:
    bounds_error: false
    fill_value: 'extrapolate'
    
  # Smoothing parameters
  smoothing:
    enabled: true
    sigma: 0.5  # Gaussian smoothing sigma
    truncate: 4.0  # Truncate filter at this many sigmas

# Isotropic Voxel Creation Settings
isotropic:
  auto_spacing: true  # Automatically determine target spacing
  target_spacing: null  # Manual target spacing (mm), null for auto
  
  # Resampling parameters
  preserve_range: true
  anti_aliasing: true
  order: 1  # Interpolation order (0=nearest, 1=linear, 3=cubic)
  
  # Quality control
  min_spacing: 0.1  # Minimum allowed spacing (mm)
  max_spacing: 10.0  # Maximum allowed spacing (mm)

# Orthogonal Views Settings
orthogonal_views:
  # Default slice positions (fraction of volume size)
  default_positions:
    axial: 0.5
    sagittal: 0.5
    coronal: 0.5
    
  # View generation options
  generate_mip: true  # Generate Maximum Intensity Projections
  generate_mean: true  # Generate Mean Intensity Projections
  generate_median: false  # Generate Median Intensity Projections
  
  # Visualization parameters
  colormap: 'gray'
  contrast_enhancement: true
  histogram_equalization: false

# Surface Rendering Settings
surface_rendering:
  # Marching cubes parameters
  level: 0.5  # Isosurface level
  step_size: 1  # Step size for marching cubes
  allow_degenerate: true  # Allow degenerate triangles
  
  # Surface processing
  smooth_surface: true
  smoothing_iterations: 5
  smoothing_relaxation: 0.1
  
  # Mesh simplification
  simplify_mesh: false
  simplification_factor: 0.5
  
  # Visualization parameters
  surface_color: 'red'
  surface_alpha: 0.8
  wireframe_color: 'blue'
  wireframe_alpha: 0.3
  
  # Lighting
  ambient_light: 0.3
  diffuse_light: 0.7
  specular_light: 0.2
  
  # Camera settings
  elevation: 20  # degrees
  azimuth: 45    # degrees
  
  # Quality settings
  high_quality: true
  anti_aliasing: true
  
# Volume Visualization Settings
volume_visualization:
  # Slice visualization
  slice_display:
    n_slices: 9
    colormap: 'gray'
    add_labels: true
    show_grid: true
    
  # 3D scatter plot
  scatter_plot:
    threshold: 0.5
    sample_ratio: 0.1
    point_size: 20
    colormap: 'viridis'
    alpha: 0.6
    
  # Overlay visualization
  overlay:
    alpha: 0.5
    colormap: 'Reds'
    blend_mode: 'multiply'
    
  # Export settings
  export_format: 'png'
  export_dpi: 300
  export_quality: 95

# Performance Settings
performance:
  # Parallel processing
  use_multiprocessing: true
  n_jobs: -1  # -1 for all available cores
  
  # Memory management
  chunk_size: 1000000  # Process in chunks for large volumes
  use_memory_mapping: true
  
  # Optimization
  optimize_mesh: true
  cache_results: true
  
  # Progress tracking
  show_progress: true
  progress_update_interval: 10  # seconds

# Quality Control Settings
quality_control:
  # Input validation
  validate_inputs: true
  check_slice_consistency: true
  verify_spacing: true
  
  # Volume validation
  min_volume_size: [10, 10, 10]  # Minimum volume dimensions
  max_volume_size: [1000, 1000, 1000]  # Maximum volume dimensions
  
  # Surface validation
  min_surface_vertices: 100
  max_surface_vertices: 1000000
  
  # Metrics calculation
  calculate_metrics: true
  validate_metrics: true
  
  # Error handling
  ignore_warnings: false
  strict_mode: false

# Output Settings
output:
  # File formats
  volume_format: 'npy'  # Options: 'npy', 'nii', 'dcm'
  mesh_format: 'obj'    # Options: 'obj', 'ply', 'stl'
  image_format: 'png'   # Options: 'png', 'jpg', 'tiff'
  
  # Directory structure
  base_output_dir: 'results/reconstruction'
  create_subdirs: true
  
  subdirectories:
    volumes: 'volumes'
    surfaces: 'surfaces'
    visualizations: 'visualizations'
    analysis: 'analysis'
    logs: 'logs'
    
  # Naming conventions
  include_timestamp: true
  include_parameters: true
  
  # Compression
  compress_volumes: true
  compression_level: 6
  
  # Metadata
  save_metadata: true
  metadata_format: 'json'

# Visualization Presets
visualization_presets:
  clinical:
    surface_color: 'red'
    surface_alpha: 0.8
    background_color: 'white'
    lighting: 'soft'
    
  research:
    surface_color: 'blue'
    surface_alpha: 0.7
    background_color: 'black'
    lighting: 'dramatic'
    
  presentation:
    surface_color: 'gold'
    surface_alpha: 0.9
    background_color: 'white'
    lighting: 'bright'

# Algorithm-specific Settings
algorithms:
  marching_cubes:
    use_classic: false  # Use classic marching cubes
    use_lewiner: true   # Use Lewiner marching cubes (default)
    
  mesh_processing:
    remove_duplicates: true
    remove_isolated_vertices: true
    fix_normals: true
    
  interpolation_algorithms:
    rbf:  # Radial Basis Function
      function: 'multiquadric'
      epsilon: 1.0
      
    kriging:
      variogram_model: 'linear'
      weight: true
      
    shepard:
      power: 2
      radius: 10

# Medical Imaging Specific Settings
medical_imaging:
  # DICOM handling
  dicom:
    sort_by_position: true
    check_orientation: true
    apply_rescale: true
    
  # Anatomical orientations
  coordinate_system: 'RAS'  # Right-Anterior-Superior
  
  # Unit conversions
  default_units: 'mm'
  convert_to_mm: true
  
  # Hounsfield units
  apply_hu_scaling: false
  hu_window_center: 40
  hu_window_width: 80
  
  # Segmentation integration
  use_segmentation_masks: true
  mask_dilation: 0  # Dilate masks by N pixels
  mask_erosion: 0   # Erode masks by N pixels

# Advanced Features
advanced:
  # Multi-scale processing
  multi_scale:
    enabled: false
    scales: [0.5, 1.0, 2.0]
    
  # Texture analysis
  texture:
    calculate_texture: false
    glcm_distances: [1, 2, 3]
    glcm_angles: [0, 45, 90, 135]
    
  # Statistical analysis
  statistics:
    calculate_histogram: true
    calculate_moments: true
    calculate_entropy: true
    
  # Machine learning integration
  ml_integration:
    use_gpu: false
    batch_size: 32
    
# Logging and Debugging
logging:
  level: 'INFO'  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  log_to_file: true
  log_file: 'reconstruction.log'
  
  # Detailed logging
  log_parameters: true
  log_timing: true
  log_memory_usage: true
  
  # Debug options
  debug_mode: false
  save_intermediate: false
  validate_each_step: false