/* CSS Variables */
:root {
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --background-color: #f8fafc;
  --background-dark: #f1f5f9;
  --text-primary: #1a202c;
  --text-secondary: #4a5568;
  --border-color: #e2e8f0;
  --success-color: #48bb78;
  --warning-color: #ed8936;
  --error-color: #f56565;
  --border-radius: 8px;
  --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background-color);
  color: var(--text-primary);
}

/* Main Layout */
.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.App-header {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  padding: 20px 0;
  box-shadow: var(--shadow-medium);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.header-left h1 {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 8px;
}

.header-left p {
  font-size: 16px;
  opacity: 0.9;
}

.upload-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.file-input {
  display: none;
}

.file-label {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-size: 14px;
  font-weight: 500;
  min-width: 200px;
}

.file-label:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.analyze-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: white;
  color: var(--primary-color);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: 600;
  transition: var(--transition);
}

.analyze-button:hover:not(:disabled) {
  background: var(--background-dark);
}

.analyze-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Navigation */
.tab-navigation {
  background: white;
  border-bottom: 1px solid var(--border-color);
  padding: 0 20px;
  box-shadow: var(--shadow-light);
}

.tabs-container {
  display: flex;
  max-width: 1200px;
  margin: 0 auto;
  overflow-x: auto;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 24px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
  border-bottom: 3px solid transparent;
  transition: var(--transition);
  white-space: nowrap;
}

.tab-button:hover {
  color: var(--primary-color);
  background: rgba(102, 126, 234, 0.05);
}

.tab-button.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

/* Main Content */
.main-content {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  width: 100%;
}

/* Footer */
.App-footer {
  background: var(--background-dark);
  padding: 20px;
  text-align: center;
  color: var(--text-secondary);
  font-size: 14px;
  border-top: 1px solid var(--border-color);
  margin-top: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
  }

  .upload-section {
    width: 100%;
    justify-content: center;
  }
}

/* Common Styles */
.detail-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 40px;
  padding-bottom: 20px;
  border-bottom: 2px solid var(--border-color);
}

.detail-icon {
  font-size: 32px;
  color: var(--primary-color);
}

.detail-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.detail-subtitle {
  color: var(--text-secondary);
  font-size: 16px;
}

/* Overview Tab */
.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.process-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 24px;
  box-shadow: var(--shadow-light);
  transition: var(--transition);
  cursor: pointer;
}

.process-card:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.icon-wrapper {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.card-title-section {
  flex: 1;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.step-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.placeholder-image {
  width: 100%;
  height: 120px;
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  background: var(--background-dark);
}

.placeholder-image i {
  font-size: 24px;
  margin-bottom: 8px;
}

.placeholder-image span {
  color: var(--text-secondary);
  font-size: 14px;
}

.card-description {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Features Section */
.features-section {
  margin: 60px 0;
}

.features-section h3 {
  text-align: center;
  margin-bottom: 40px;
  font-size: 24px;
  color: var(--text-primary);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.feature-card {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 24px;
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  transition: var(--transition);
}

.feature-card:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

.feature-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
}

.feature-content h4 {
  margin: 0 0 8px 0;
  color: var(--text-primary);
}

.feature-content p {
  margin: 0;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Pipeline Flow */
.pipeline-flow {
  margin: 60px 0;
}

.pipeline-flow h3 {
  text-align: center;
  margin-bottom: 40px;
  font-size: 24px;
  color: var(--text-primary);
}

.flow-diagram {
  background: white;
  border-radius: var(--border-radius);
  padding: 40px;
  box-shadow: var(--shadow-light);
}

.flow-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 20px;
}

.flow-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-width: 120px;
}

.flow-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  margin-bottom: 12px;
}

.flow-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.flow-arrow {
  color: var(--primary-color);
  font-size: 20px;
  margin: 0 10px;
}

/* Technical Specs */
.technical-specs {
  margin: 60px 0;
}

.technical-specs h3 {
  text-align: center;
  margin-bottom: 40px;
  font-size: 24px;
  color: var(--text-primary);
}

.specs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.spec-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 24px;
  box-shadow: var(--shadow-light);
}

.spec-card h4 {
  margin: 0 0 16px 0;
  color: var(--text-primary);
  font-size: 18px;
}

.spec-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.spec-card li {
  padding: 8px 0;
  color: var(--text-secondary);
  border-bottom: 1px solid var(--border-color);
}

.spec-card li:last-child {
  border-bottom: none;
}

.spec-card li:before {
  content: '✓';
  color: var(--success-color);
  font-weight: bold;
  margin-right: 8px;
}

/* Info Cards */
.dicom-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.info-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 24px;
  box-shadow: var(--shadow-light);
}

.info-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.info-header i {
  color: var(--primary-color);
  font-size: 18px;
}

.info-header h4 {
  margin: 0;
  color: var(--text-primary);
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--border-color);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  color: var(--text-secondary);
  font-weight: 500;
}

.info-value {
  color: var(--text-primary);
  font-weight: 600;
}

/* Pipeline Steps */
.dicom-pipeline {
  margin: 40px 0;
}

.dicom-pipeline h4 {
  margin-bottom: 24px;
  color: var(--text-primary);
}

.pipeline-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
  padding: 30px;
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
}

.pipeline-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-width: 150px;
}

.step-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  margin-bottom: 12px;
}

.step-content h5 {
  margin: 0 0 8px 0;
  color: var(--text-primary);
  font-size: 16px;
}

.step-content p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.4;
}

.step-info h4 {
  margin: 0 0 4px 0;
  color: var(--text-primary);
  font-size: 16px;
}

.step-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.4;
}

.pipeline-arrow {
  color: var(--primary-color);
  font-size: 20px;
}

/* Visualization */
.dicom-visualization {
  margin: 40px 0;
}

.dicom-visualization h4 {
  margin-bottom: 24px;
  color: var(--text-primary);
}

.visualization-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
}

.viz-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.viz-placeholder {
  width: 100%;
  height: 150px;
  background: var(--background-dark);
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  border: 2px dashed var(--border-color);
}

.viz-placeholder i {
  font-size: 32px;
  color: var(--primary-color);
  margin-bottom: 8px;
}

.viz-placeholder span {
  color: var(--text-secondary);
  font-size: 14px;
}

.viz-label {
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
}

/* Preprocessing Tab */
.preprocessing-methods {
  margin: 40px 0;
}

.preprocessing-methods h4 {
  margin-bottom: 24px;
  color: var(--text-primary);
}

.methods-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.method-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 24px;
  box-shadow: var(--shadow-light);
}

.method-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.method-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.method-card h5 {
  margin: 0 0 8px 0;
  color: var(--text-primary);
}

.method-card p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.preprocessing-pipeline {
  margin: 40px 0;
}

.preprocessing-pipeline h4 {
  margin-bottom: 24px;
  color: var(--text-primary);
}

.preprocessing-comparison {
  margin: 40px 0;
}

.preprocessing-comparison h4 {
  margin-bottom: 24px;
  color: var(--text-primary);
}

.comparison-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
}

.comparison-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.comparison-image {
  width: 100%;
  height: 150px;
  background: var(--background-dark);
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  border: 2px dashed var(--border-color);
}

.comparison-image i {
  font-size: 32px;
  color: var(--primary-color);
  margin-bottom: 8px;
}

.comparison-image span {
  color: var(--text-secondary);
  font-size: 14px;
}

.comparison-label {
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
}

/* Segmentation Tab */
.segmentation-pipeline {
  margin: 40px 0;
}

.visualization-section {
  margin: 40px 0;
}

.visualization-section h4 {
  margin-bottom: 24px;
  color: var(--text-primary);
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 24px;
}

.result-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.result-image {
  width: 100%;
  height: 120px;
  background: var(--background-dark);
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  border: 2px dashed var(--border-color);
}

.result-image i {
  font-size: 28px;
  color: var(--primary-color);
  margin-bottom: 8px;
}

.result-image span {
  color: var(--text-secondary);
  font-size: 12px;
}

.result-label {
  color: var(--text-secondary);
  font-size: 12px;
  font-weight: 500;
}

.accuracy-metrics {
  margin: 40px 0;
}

.accuracy-metrics h4 {
  margin-bottom: 24px;
  color: var(--text-primary);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.metric-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 20px;
  box-shadow: var(--shadow-light);
  display: flex;
  align-items: center;
  gap: 16px;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 4px;
}

.metric-label {
  color: var(--text-secondary);
  font-size: 12px;
  font-weight: 500;
}

.metric-info {
  flex: 1;
}

/* Features Tab */
.feature-categories {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 40px;
  margin-bottom: 40px;
}

.category-navigation {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.category-btn {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  border: 2px solid var(--border-color);
  background: white;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  text-align: left;
}

.category-btn.active {
  border-color: var(--primary-color);
  background: rgba(102, 126, 234, 0.05);
}

.category-btn:hover:not(.active) {
  border-color: var(--primary-color);
}

.category-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.category-info h4 {
  margin: 0 0 4px 0;
  color: var(--text-primary);
}

.category-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.category-icon-large {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.category-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
}

.category-header h3 {
  margin: 0 0 8px 0;
  color: var(--text-primary);
}

.category-header p {
  margin: 0;
  color: var(--text-secondary);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.feature-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 20px;
  box-shadow: var(--shadow-light);
}

.feature-header h4 {
  margin: 0 0 12px 0;
  color: var(--text-primary);
  font-size: 16px;
}

.feature-content p {
  margin: 0 0 12px 0;
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.4;
}

.feature-value {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-top: 1px solid var(--border-color);
}

.value-label {
  color: var(--text-secondary);
  font-size: 12px;
}

.value-number {
  color: var(--primary-color);
  font-weight: 600;
}

.feature-summary {
  margin: 60px 0;
}

.feature-summary h4 {
  margin-bottom: 24px;
  color: var(--text-primary);
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
}

.summary-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 24px;
  box-shadow: var(--shadow-light);
  display: flex;
  align-items: center;
  gap: 20px;
}

.summary-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.summary-value {
  font-size: 32px;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 4px;
}

.summary-label {
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
}

/* Reconstruction Tab */
.reconstruction-methods {
  margin: 40px 0;
}

.methods-navigation {
  display: flex;
  gap: 16px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.method-btn {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  border: 2px solid var(--border-color);
  background: white;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  text-align: left;
  min-width: 250px;
}

.method-btn.active {
  border-color: var(--primary-color);
  background: rgba(102, 126, 234, 0.05);
}

.method-btn:hover:not(.active) {
  border-color: var(--primary-color);
}

.method-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.method-info h4 {
  margin: 0 0 4px 0;
  color: var(--text-primary);
}

.method-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.reconstruction-content {
  margin: 40px 0;
}

.view-header {
  margin-bottom: 24px;
}

.view-header h4 {
  margin: 0 0 8px 0;
  color: var(--text-primary);
}

.view-header p {
  margin: 0;
  color: var(--text-secondary);
}

.surface-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

.surface-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 24px;
  box-shadow: var(--shadow-light);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.card-header i {
  color: var(--primary-color);
  font-size: 18px;
}

.card-header h5 {
  margin: 0;
  color: var(--text-primary);
}

.surface-visualization {
  margin: 20px 0;
}

.surface-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.surface-info .info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--border-color);
}

.surface-info .info-item:last-child {
  border-bottom: none;
}

.volume-visualization {
  width: 100%;
  height: 300px;
  background: var(--background-dark);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed var(--border-color);
}

.orthogonal-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.orthogonal-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 24px;
  box-shadow: var(--shadow-light);
}

.orthogonal-card .card-header h5 {
  margin: 0 0 16px 0;
  color: var(--text-primary);
}

.orthogonal-visualization {
  width: 100%;
  height: 200px;
  background: var(--background-dark);
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed var(--border-color);
}

.orthogonal-visualization i {
  font-size: 32px;
  color: var(--primary-color);
  margin-bottom: 8px;
}

.orthogonal-visualization span {
  color: var(--text-secondary);
  font-size: 14px;
}

/* Results Tab */
.processing-status {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  text-align: center;
}

.processing-content {
  max-width: 500px;
}

.processing-icon {
  font-size: 48px;
  color: var(--primary-color);
  margin-bottom: 20px;
}

.processing-content h3 {
  margin: 0 0 10px 0;
  color: var(--text-primary);
}

.processing-content p {
  color: var(--text-secondary);
  margin-bottom: 30px;
}

.processing-steps {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

.step {
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  background: var(--background-dark);
  color: var(--text-secondary);
}

.step.completed {
  background: var(--success-color);
  color: white;
}

.step.active {
  background: var(--primary-color);
  color: white;
}

.results-navigation {
  display: flex;
  gap: 8px;
  margin-bottom: 40px;
  padding: 8px;
  background: var(--background-dark);
  border-radius: var(--border-radius);
  overflow-x: auto;
}

.nav-btn {
  padding: 12px 24px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: var(--transition);
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-btn.active {
  background: var(--primary-color);
  color: white;
}

.nav-btn:hover:not(.active) {
  background: rgba(102, 126, 234, 0.1);
}

.metrics-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.summary-card.primary {
  border-left: 4px solid var(--primary-color);
}

.summary-card.success {
  border-left: 4px solid var(--success-color);
}

.summary-card.info {
  border-left: 4px solid #3498db;
}

.summary-card.warning {
  border-left: 4px solid var(--warning-color);
}

.clinical-view {
  margin: 40px 0;
}

.clinical-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.clinical-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 24px;
  box-shadow: var(--shadow-light);
}

.clinical-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.clinical-header i {
  color: var(--primary-color);
  font-size: 18px;
}

.clinical-header h4 {
  margin: 0;
  color: var(--text-primary);
}

.clinical-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.clinical-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--border-color);
}

.clinical-item:last-child {
  border-bottom: none;
}

.clinical-label {
  color: var(--text-secondary);
  font-weight: 500;
}

.clinical-value {
  color: var(--text-primary);
  font-weight: 600;
}

.clinical-value.positive {
  color: var(--success-color);
}

.clinical-value.negative {
  color: var(--text-secondary);
}

.clinical-value.risk-moderate {
  color: var(--warning-color);
}

.clinical-value.risk-high {
  color: var(--error-color);
}

.clinical-value.risk-low {
  color: var(--success-color);
}

.indicators-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.indicator-badge {
  padding: 4px 8px;
  background: var(--background-dark);
  border-radius: 12px;
  font-size: 12px;
  color: var(--text-secondary);
}

.report-view {
  margin: 40px 0;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.report-header h3 {
  margin: 0;
  color: var(--text-primary);
}

.report-actions {
  display: flex;
  gap: 12px;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: 600;
  transition: var(--transition);
  text-decoration: none;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: var(--secondary-color);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-outline:hover {
  background: var(--primary-color);
  color: white;
}

.report-content {
  background: white;
  border-radius: var(--border-radius);
  padding: 40px;
  box-shadow: var(--shadow-light);
}

.report-section {
  margin-bottom: 30px;
}

.report-section h4 {
  margin: 0 0 16px 0;
  color: var(--text-primary);
}

.report-section p {
  margin: 0 0 16px 0;
  color: var(--text-secondary);
  line-height: 1.6;
}

.report-section ul {
  margin: 0;
  padding-left: 20px;
}

.report-section li {
  margin-bottom: 8px;
  color: var(--text-secondary);
}

.recommendations {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.recommendation-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: var(--background-dark);
  border-radius: var(--border-radius);
}

.recommendation-item i {
  color: var(--primary-color);
  font-size: 16px;
}

.recommendation-item span {
  color: var(--text-secondary);
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .feature-categories {
    grid-template-columns: 1fr;
  }

  .category-navigation {
    flex-direction: row;
    overflow-x: auto;
  }

  .category-btn {
    min-width: 280px;
  }

  .methods-navigation {
    flex-direction: column;
  }

  .method-btn {
    min-width: auto;
  }

  .orthogonal-grid {
    grid-template-columns: 1fr;
  }

  .clinical-grid {
    grid-template-columns: 1fr;
  }

  .report-header {
    flex-direction: column;
    gap: 20px;
  }

  .report-actions {
    width: 100%;
    justify-content: center;
  }
}

/* Animation */
@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(359deg);
  }
}

.fa-spin {
  animation: fa-spin 2s infinite linear;
}
