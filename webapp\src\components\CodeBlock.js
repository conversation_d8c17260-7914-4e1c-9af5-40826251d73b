import React from 'react';

const CodeBlock = ({ code, language }) => {
  const copyToClipboard = () => {
    navigator.clipboard.writeText(code);
    // You could add a toast notification here
  };

  return (
    <div className="code-block">
      <div className="code-header">
        <div className="code-language">{language.toUpperCase()}</div>
        <button className="copy-button" onClick={copyToClipboard}>
          <i className="fas fa-copy"></i>
          Copy
        </button>
      </div>
      <div className="code-content">
        <pre>
          <code className={`language-${language}`}>{code}</code>
        </pre>
      </div>
    </div>
  );
};

export default CodeBlock;