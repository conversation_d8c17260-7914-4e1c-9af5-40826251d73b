"""
Results Calculation and Comparison Demo
======================================

This script demonstrates comprehensive evaluation metrics for segmentation 
performance and comparison between different methods.

Author: Dr<PERSON> <PERSON>
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from typing import List, Dict, Tuple
import time
import json

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from results_calculation import (
    SegmentationMetrics, VolumeCalculator, MethodComparator,
    ResultsVisualizer, ResultsReporter
)


def create_realistic_segmentation_data(n_samples: int = 50) -> Dict[str, List[np.ndarray]]:
    """
    Create realistic segmentation data with different tumor shapes and sizes.
    
    Args:
        n_samples: Number of samples to generate
        
    Returns:
        Dictionary with ground truth and predictions
    """
    print(f"Creating {n_samples} realistic segmentation samples...")
    
    np.random.seed(42)  # For reproducibility
    
    ground_truth = []
    predictions = {
        'U-Net': [],
        'FCN': [],
        'DeepLab': [],
        'Traditional': []
    }
    
    image_size = (256, 256)
    
    for i in range(n_samples):
        # Create ground truth with varying tumor characteristics
        gt = create_tumor_mask(image_size, sample_idx=i)
        ground_truth.append(gt)
        
        # Generate predictions for different methods
        predictions['U-Net'].append(simulate_unet_prediction(gt))
        predictions['FCN'].append(simulate_fcn_prediction(gt))
        predictions['DeepLab'].append(simulate_deeplab_prediction(gt))
        predictions['Traditional'].append(simulate_traditional_prediction(gt))
    
    return {
        'ground_truth': ground_truth,
        'predictions': predictions
    }


def create_tumor_mask(image_size: Tuple[int, int], sample_idx: int) -> np.ndarray:
    """Create realistic tumor mask with varying characteristics."""
    mask = np.zeros(image_size)
    
    # Vary tumor characteristics based on sample index
    if sample_idx % 4 == 0:
        # Circular tumor
        center = (128 + np.random.randint(-30, 30), 128 + np.random.randint(-30, 30))
        radius = 25 + np.random.randint(-10, 15)
        
        y, x = np.ogrid[:image_size[0], :image_size[1]]
        mask = (x - center[0])**2 + (y - center[1])**2 <= radius**2
        
    elif sample_idx % 4 == 1:
        # Elliptical tumor
        center = (128 + np.random.randint(-40, 40), 128 + np.random.randint(-40, 40))
        a = 30 + np.random.randint(-5, 10)  # Semi-major axis
        b = 20 + np.random.randint(-5, 10)  # Semi-minor axis
        
        y, x = np.ogrid[:image_size[0], :image_size[1]]
        mask = ((x - center[0])**2 / a**2 + (y - center[1])**2 / b**2) <= 1
        
    elif sample_idx % 4 == 2:
        # Irregular tumor (multiple circles)
        centers = [(100 + np.random.randint(-20, 20), 100 + np.random.randint(-20, 20)),
                  (150 + np.random.randint(-20, 20), 120 + np.random.randint(-20, 20)),
                  (125 + np.random.randint(-20, 20), 150 + np.random.randint(-20, 20))]
        
        y, x = np.ogrid[:image_size[0], :image_size[1]]
        for center in centers:
            radius = 15 + np.random.randint(-5, 10)
            mask |= (x - center[0])**2 + (y - center[1])**2 <= radius**2
    
    else:
        # Small tumor
        center = (128 + np.random.randint(-50, 50), 128 + np.random.randint(-50, 50))
        radius = 15 + np.random.randint(-5, 8)
        
        y, x = np.ogrid[:image_size[0], :image_size[1]]
        mask = (x - center[0])**2 + (y - center[1])**2 <= radius**2
    
    return mask.astype(np.uint8)


def simulate_unet_prediction(ground_truth: np.ndarray) -> np.ndarray:
    """Simulate U-Net prediction (high accuracy)."""
    # Add slight noise and boundary variations
    noise = np.random.normal(0, 0.05, ground_truth.shape)
    pred = ground_truth.astype(float) + noise
    
    # Add some boundary smoothing
    from scipy.ndimage import gaussian_filter
    pred = gaussian_filter(pred, sigma=0.8)
    
    # Apply threshold
    pred = (pred > 0.4).astype(np.uint8)
    
    return pred


def simulate_fcn_prediction(ground_truth: np.ndarray) -> np.ndarray:
    """Simulate FCN prediction (moderate accuracy)."""
    # Add more noise and some systematic errors
    noise = np.random.normal(0, 0.15, ground_truth.shape)
    pred = ground_truth.astype(float) + noise
    
    # Add some false positives
    false_positives = np.random.random(ground_truth.shape) < 0.02
    pred[false_positives] = 1.0
    
    # Apply threshold
    pred = (pred > 0.3).astype(np.uint8)
    
    return pred


def simulate_deeplab_prediction(ground_truth: np.ndarray) -> np.ndarray:
    """Simulate DeepLab prediction (good accuracy with better boundaries)."""
    # Add moderate noise
    noise = np.random.normal(0, 0.1, ground_truth.shape)
    pred = ground_truth.astype(float) + noise
    
    # Better boundary preservation
    from scipy.ndimage import binary_erosion, binary_dilation
    pred_binary = (pred > 0.3).astype(np.uint8)
    
    # Clean up with morphological operations
    pred_binary = binary_erosion(pred_binary, iterations=1)
    pred_binary = binary_dilation(pred_binary, iterations=1)
    
    return pred_binary.astype(np.uint8)


def simulate_traditional_prediction(ground_truth: np.ndarray) -> np.ndarray:
    """Simulate traditional method prediction (lower accuracy)."""
    # Add significant noise and systematic errors
    noise = np.random.normal(0, 0.25, ground_truth.shape)
    pred = ground_truth.astype(float) + noise
    
    # Add more false positives and negatives
    false_positives = np.random.random(ground_truth.shape) < 0.05
    false_negatives = np.random.random(ground_truth.shape) < 0.08
    
    pred[false_positives] = 1.0
    pred[false_negatives & (ground_truth == 1)] = 0.0
    
    # Apply threshold
    pred = (pred > 0.2).astype(np.uint8)
    
    return pred


def demonstrate_basic_metrics():
    """Demonstrate basic segmentation metrics."""
    print("\n" + "="*60)
    print("BASIC SEGMENTATION METRICS DEMONSTRATION")
    print("="*60)
    
    # Create simple test case
    gt = np.zeros((100, 100))
    gt[30:70, 30:70] = 1  # Ground truth square
    
    pred = np.zeros((100, 100))
    pred[25:65, 35:75] = 1  # Predicted square (shifted)
    
    # Calculate metrics
    metrics = SegmentationMetrics()
    
    print("Ground Truth: 40x40 square at (30,30)")
    print("Prediction: 40x40 square at (25,35)")
    print()
    
    results = metrics.calculate_all_metrics(gt, pred, include_distances=True)
    
    print("Calculated Metrics:")
    print("-" * 30)
    for metric, value in results.items():
        if value != float('inf'):
            print(f"{metric}: {value:.4f}")
        else:
            print(f"{metric}: inf")
    
    # Visualize
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    axes[0].imshow(gt, cmap='gray')
    axes[0].set_title('Ground Truth')
    axes[0].axis('off')
    
    axes[1].imshow(pred, cmap='gray')
    axes[1].set_title('Prediction')
    axes[1].axis('off')
    
    # Overlap visualization
    overlap = np.zeros((100, 100, 3))
    overlap[gt == 1] = [1, 0, 0]  # Red for ground truth
    overlap[pred == 1] = [0, 1, 0]  # Green for prediction
    overlap[(gt == 1) & (pred == 1)] = [1, 1, 0]  # Yellow for overlap
    
    axes[2].imshow(overlap)
    axes[2].set_title('Overlap (Red=GT, Green=Pred, Yellow=Both)')
    axes[2].axis('off')
    
    plt.tight_layout()
    plt.show()


def demonstrate_volume_calculation():
    """Demonstrate volume calculation and comparison."""
    print("\n" + "="*60)
    print("VOLUME CALCULATION DEMONSTRATION")
    print("="*60)
    
    # Create 3D volume
    volume_shape = (50, 128, 128)
    voxel_spacing = (2.0, 1.0, 1.0)  # z, y, x spacing in mm
    
    # Create ground truth volume
    gt_volume = np.zeros(volume_shape)
    center = (25, 64, 64)
    radius = 15
    
    for z in range(volume_shape[0]):
        for y in range(volume_shape[1]):
            for x in range(volume_shape[2]):
                distance = np.sqrt((z - center[0])**2 + (y - center[1])**2 + (x - center[2])**2)
                if distance <= radius:
                    gt_volume[z, y, x] = 1
    
    # Create predicted volume (slightly different)
    pred_volume = gt_volume.copy()
    noise = np.random.normal(0, 0.1, volume_shape)
    pred_volume = (pred_volume + noise > 0.4).astype(np.uint8)
    
    # Calculate volumes
    volume_calc = VolumeCalculator()
    
    gt_vol = volume_calc.calculate_volume(gt_volume, voxel_spacing)
    pred_vol = volume_calc.calculate_volume(pred_volume, voxel_spacing)
    
    print("Ground Truth Volume:")
    print(f"  Total volume: {gt_vol['total_volume_ml']:.2f} ml")
    print(f"  Voxel count: {gt_vol['voxel_count']}")
    print(f"  Bounding box volume: {gt_vol['bounding_box_volume_mm3']:.2f} mm³")
    print(f"  Fill ratio: {gt_vol['fill_ratio']:.3f}")
    
    print("\nPredicted Volume:")
    print(f"  Total volume: {pred_vol['total_volume_ml']:.2f} ml")
    print(f"  Voxel count: {pred_vol['voxel_count']}")
    print(f"  Bounding box volume: {pred_vol['bounding_box_volume_mm3']:.2f} mm³")
    print(f"  Fill ratio: {pred_vol['fill_ratio']:.3f}")
    
    # Volume comparison
    comparison = volume_calc.compare_volumes(gt_volume, pred_volume, voxel_spacing)
    
    print("\nVolume Comparison:")
    print(f"  Volume difference: {comparison['volume_difference_ml']:.2f} ml")
    print(f"  Relative error: {comparison['relative_error']:.3f}")
    print(f"  Absolute error: {comparison['absolute_error_ml']:.2f} ml")
    
    # Volume overlap
    overlap = volume_calc.calculate_volume_overlap(gt_volume, pred_volume, voxel_spacing)
    
    print("\nVolume Overlap:")
    print(f"  Intersection volume: {overlap['intersection_volume_mm3']:.2f} mm³")
    print(f"  Union volume: {overlap['union_volume_mm3']:.2f} mm³")
    print(f"  Dice volume: {overlap['dice_volume']:.3f}")
    print(f"  Jaccard volume: {overlap['jaccard_volume']:.3f}")
    print(f"  Overlap fraction: {overlap['overlap_fraction']:.3f}")
    
    # Visualize slices
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    mid_slice = volume_shape[0] // 2
    
    # Ground truth slices
    axes[0, 0].imshow(gt_volume[mid_slice], cmap='gray')
    axes[0, 0].set_title('GT - Axial')
    axes[0, 0].axis('off')
    
    axes[0, 1].imshow(gt_volume[:, 64, :], cmap='gray')
    axes[0, 1].set_title('GT - Coronal')
    axes[0, 1].axis('off')
    
    axes[0, 2].imshow(gt_volume[:, :, 64], cmap='gray')
    axes[0, 2].set_title('GT - Sagittal')
    axes[0, 2].axis('off')
    
    # Predicted slices
    axes[1, 0].imshow(pred_volume[mid_slice], cmap='gray')
    axes[1, 0].set_title('Pred - Axial')
    axes[1, 0].axis('off')
    
    axes[1, 1].imshow(pred_volume[:, 64, :], cmap='gray')
    axes[1, 1].set_title('Pred - Coronal')
    axes[1, 1].axis('off')
    
    axes[1, 2].imshow(pred_volume[:, :, 64], cmap='gray')
    axes[1, 2].set_title('Pred - Sagittal')
    axes[1, 2].axis('off')
    
    plt.tight_layout()
    plt.show()


def demonstrate_method_comparison():
    """Demonstrate comprehensive method comparison."""
    print("\n" + "="*60)
    print("METHOD COMPARISON DEMONSTRATION")
    print("="*60)
    
    # Create realistic data
    data = create_realistic_segmentation_data(n_samples=30)
    
    # Initialize comparator
    comparator = MethodComparator()
    
    # Add methods
    methods = ['U-Net', 'FCN', 'DeepLab', 'Traditional']
    
    for method in methods:
        print(f"Adding results for {method}...")
        comparator.add_method_results(
            method_name=method,
            ground_truth=data['ground_truth'],
            predictions=data['predictions'][method],
            voxel_spacing=(1.0, 1.0, 1.0)
        )
    
    # Calculate summary statistics
    print("\nCalculating summary statistics...")
    
    for method in methods:
        stats = comparator.calculate_summary_statistics(method)
        print(f"\n{method} Summary:")
        print(f"  Dice Coefficient: {stats['dice_coefficient']['mean']:.3f} ± {stats['dice_coefficient']['std']:.3f}")
        print(f"  Jaccard Index: {stats['jaccard_index']['mean']:.3f} ± {stats['jaccard_index']['std']:.3f}")
        print(f"  Accuracy: {stats['accuracy']['mean']:.3f} ± {stats['accuracy']['std']:.3f}")
        print(f"  Precision: {stats['precision']['mean']:.3f} ± {stats['precision']['std']:.3f}")
        print(f"  Recall: {stats['recall']['mean']:.3f} ± {stats['recall']['std']:.3f}")
    
    # Pairwise comparisons
    print("\nPairwise Statistical Comparisons:")
    print("-" * 40)
    
    comparison = comparator.compare_methods('U-Net', 'Traditional', 'dice_coefficient')
    print(f"U-Net vs Traditional (Dice Coefficient):")
    print(f"  U-Net mean: {comparison['method1_mean']:.3f}")
    print(f"  Traditional mean: {comparison['method2_mean']:.3f}")
    print(f"  Difference: {comparison['difference']:.3f}")
    print(f"  P-value: {comparison['paired_t_test']['p_value']:.4f}")
    print(f"  Significant: {comparison['paired_t_test']['significant']}")
    print(f"  Effect size: {comparison['effect_size']}")
    
    comparison = comparator.compare_methods('U-Net', 'DeepLab', 'dice_coefficient')
    print(f"\nU-Net vs DeepLab (Dice Coefficient):")
    print(f"  U-Net mean: {comparison['method1_mean']:.3f}")
    print(f"  DeepLab mean: {comparison['method2_mean']:.3f}")
    print(f"  Difference: {comparison['difference']:.3f}")
    print(f"  P-value: {comparison['paired_t_test']['p_value']:.4f}")
    print(f"  Significant: {comparison['paired_t_test']['significant']}")
    print(f"  Effect size: {comparison['effect_size']}")
    
    # Generate comprehensive report
    print("\nGenerating comprehensive report...")
    report = comparator.generate_comparison_report()
    
    print("\nMethod Rankings (Dice Coefficient):")
    for i, (method, score) in enumerate(report['rankings']['dice_coefficient']):
        print(f"  {i+1}. {method}: {score:.3f}")
    
    print(f"\nOverall Best Method: {report['best_method']['method']}")
    print(f"Average Rank: {report['best_method']['average_rank']:.2f}")
    
    return comparator


def demonstrate_advanced_visualization():
    """Demonstrate advanced visualization capabilities."""
    print("\n" + "="*60)
    print("ADVANCED VISUALIZATION DEMONSTRATION")
    print("="*60)
    
    # Create data and comparator
    data = create_realistic_segmentation_data(n_samples=25)
    comparator = MethodComparator()
    
    methods = ['U-Net', 'FCN', 'DeepLab', 'Traditional']
    
    for method in methods:
        comparator.add_method_results(
            method_name=method,
            ground_truth=data['ground_truth'],
            predictions=data['predictions'][method],
            voxel_spacing=(1.0, 1.0, 1.0)
        )
    
    # Create visualizer
    visualizer = ResultsVisualizer()
    
    print("Creating metrics comparison plot...")
    visualizer.plot_metrics_comparison(comparator)
    
    print("Creating method ranking plot...")
    visualizer.plot_method_ranking(comparator)
    
    print("Creating volume comparison plot...")
    visualizer.plot_volume_comparison(comparator)
    
    print("Creating performance summary...")
    visualizer.plot_performance_summary(comparator)
    
    print("Creating comprehensive results dashboard...")
    visualizer.create_results_dashboard(comparator)
    
    return comparator


def demonstrate_detailed_analysis():
    """Demonstrate detailed analysis capabilities."""
    print("\n" + "="*60)
    print("DETAILED ANALYSIS DEMONSTRATION")
    print("="*60)
    
    # Create comparator with data
    comparator = demonstrate_method_comparison()
    
    # Generate detailed reports
    reporter = ResultsReporter()
    
    print("Generating text report...")
    text_report = reporter.generate_text_report(comparator)
    
    print("Text Report Sample:")
    print("-" * 30)
    print(text_report[:1000] + "...")
    
    print("\nGenerating JSON report...")
    json_report = reporter.generate_json_report(comparator)
    
    print(f"JSON report contains {len(json_report)} main sections:")
    for key in json_report.keys():
        print(f"  - {key}")
    
    print("\nGenerating CSV summary...")
    csv_summary = reporter.generate_csv_summary(comparator)
    
    print("CSV Summary:")
    print("-" * 30)
    print(csv_summary.head())
    
    # Detailed statistical analysis
    print("\nDetailed Statistical Analysis:")
    print("-" * 40)
    
    methods = list(comparator.results.keys())
    
    # Calculate confidence intervals
    for method in methods:
        dice_values = [m['dice_coefficient'] for m in comparator.results[method]['metrics']]
        mean_dice = np.mean(dice_values)
        std_dice = np.std(dice_values)
        n = len(dice_values)
        
        # 95% confidence interval
        confidence_interval = 1.96 * std_dice / np.sqrt(n)
        
        print(f"{method}:")
        print(f"  Dice Coefficient: {mean_dice:.3f} ± {std_dice:.3f}")
        print(f"  95% CI: [{mean_dice - confidence_interval:.3f}, {mean_dice + confidence_interval:.3f}]")
        print(f"  Sample size: {n}")
    
    # Performance consistency analysis
    print("\nPerformance Consistency Analysis:")
    print("-" * 40)
    
    for method in methods:
        dice_values = [m['dice_coefficient'] for m in comparator.results[method]['metrics']]
        cv = np.std(dice_values) / np.mean(dice_values)  # Coefficient of variation
        
        print(f"{method}:")
        print(f"  Coefficient of Variation: {cv:.3f}")
        print(f"  Consistency: {'High' if cv < 0.1 else 'Medium' if cv < 0.2 else 'Low'}")


def demonstrate_error_analysis():
    """Demonstrate error analysis capabilities."""
    print("\n" + "="*60)
    print("ERROR ANALYSIS DEMONSTRATION")
    print("="*60)
    
    # Create specific test cases for error analysis
    test_cases = create_error_analysis_cases()
    
    metrics = SegmentationMetrics()
    
    print("Analyzing different types of segmentation errors...")
    
    for case_name, (gt, pred) in test_cases.items():
        print(f"\n{case_name}:")
        print("-" * 30)
        
        results = metrics.calculate_all_metrics(gt, pred, include_distances=False)
        
        print(f"  Dice Coefficient: {results['dice_coefficient']:.3f}")
        print(f"  Jaccard Index: {results['jaccard_index']:.3f}")
        print(f"  Precision: {results['precision']:.3f}")
        print(f"  Recall: {results['recall']:.3f}")
        print(f"  Specificity: {results['specificity']:.3f}")
        
        # Analyze error type
        precision = results['precision']
        recall = results['recall']
        
        if precision > 0.8 and recall > 0.8:
            error_type = "Good segmentation"
        elif precision > recall:
            error_type = "Under-segmentation (missed regions)"
        elif recall > precision:
            error_type = "Over-segmentation (false positives)"
        else:
            error_type = "Mixed errors"
        
        print(f"  Error Type: {error_type}")
    
    # Visualize error cases
    visualize_error_cases(test_cases)


def create_error_analysis_cases() -> Dict[str, Tuple[np.ndarray, np.ndarray]]:
    """Create specific test cases for error analysis."""
    size = (100, 100)
    
    # Ground truth: circle in center
    gt = np.zeros(size)
    y, x = np.ogrid[:size[0], :size[1]]
    gt[(x - 50)**2 + (y - 50)**2 <= 20**2] = 1
    
    cases = {}
    
    # Case 1: Perfect segmentation
    cases['Perfect Segmentation'] = (gt, gt.copy())
    
    # Case 2: Under-segmentation
    under_seg = gt.copy()
    under_seg[(x - 50)**2 + (y - 50)**2 <= 15**2] = 0  # Remove center
    cases['Under-segmentation'] = (gt, under_seg)
    
    # Case 3: Over-segmentation
    over_seg = gt.copy()
    over_seg[(x - 50)**2 + (y - 50)**2 <= 25**2] = 1  # Larger circle
    cases['Over-segmentation'] = (gt, over_seg)
    
    # Case 4: Boundary shift
    boundary_shift = np.zeros(size)
    boundary_shift[(x - 45)**2 + (y - 45)**2 <= 20**2] = 1  # Shifted circle
    cases['Boundary Shift'] = (gt, boundary_shift)
    
    # Case 5: Fragmentation
    fragmented = gt.copy()
    fragmented[45:55, 45:55] = 0  # Remove center part
    cases['Fragmentation'] = (gt, fragmented)
    
    # Case 6: False positives
    false_pos = gt.copy()
    false_pos[20:30, 20:30] = 1  # Add false positive region
    cases['False Positives'] = (gt, false_pos)
    
    return cases


def visualize_error_cases(test_cases: Dict[str, Tuple[np.ndarray, np.ndarray]]):
    """Visualize different error cases."""
    n_cases = len(test_cases)
    fig, axes = plt.subplots(2, n_cases, figsize=(4 * n_cases, 8))
    
    for i, (case_name, (gt, pred)) in enumerate(test_cases.items()):
        # Ground truth
        axes[0, i].imshow(gt, cmap='gray')
        axes[0, i].set_title(f'{case_name}\nGround Truth')
        axes[0, i].axis('off')
        
        # Prediction with error visualization
        error_vis = np.zeros((*gt.shape, 3))
        error_vis[gt == 1] = [0, 1, 0]  # Green for true positives
        error_vis[(gt == 0) & (pred == 1)] = [1, 0, 0]  # Red for false positives
        error_vis[(gt == 1) & (pred == 0)] = [0, 0, 1]  # Blue for false negatives
        
        axes[1, i].imshow(error_vis)
        axes[1, i].set_title(f'{case_name}\nErrors (Red=FP, Blue=FN)')
        axes[1, i].axis('off')
    
    plt.tight_layout()
    plt.show()


def demonstrate_performance_benchmarking():
    """Demonstrate performance benchmarking."""
    print("\n" + "="*60)
    print("PERFORMANCE BENCHMARKING DEMONSTRATION")
    print("="*60)
    
    # Test different image sizes
    sizes = [(64, 64), (128, 128), (256, 256), (512, 512)]
    metrics = SegmentationMetrics()
    
    print("Benchmarking computation times for different image sizes...")
    
    benchmark_results = []
    
    for size in sizes:
        print(f"\nTesting size {size}...")
        
        # Create test data
        gt = np.random.randint(0, 2, size)
        pred = np.random.randint(0, 2, size)
        
        # Time basic metrics
        start_time = time.time()
        basic_metrics = {
            'dice': metrics.dice_coefficient(gt, pred),
            'jaccard': metrics.jaccard_index(gt, pred),
            'accuracy': metrics.accuracy(gt, pred),
            'precision': metrics.precision(gt, pred),
            'recall': metrics.recall(gt, pred)
        }
        basic_time = time.time() - start_time
        
        # Time all metrics
        start_time = time.time()
        all_metrics = metrics.calculate_all_metrics(gt, pred, include_distances=False)
        all_time = time.time() - start_time
        
        # Time with distances
        start_time = time.time()
        distance_metrics = metrics.calculate_all_metrics(gt, pred, include_distances=True)
        distance_time = time.time() - start_time
        
        benchmark_results.append({
            'size': size,
            'pixels': size[0] * size[1],
            'basic_time': basic_time,
            'all_time': all_time,
            'distance_time': distance_time
        })
        
        print(f"  Basic metrics: {basic_time:.4f}s")
        print(f"  All metrics: {all_time:.4f}s")
        print(f"  With distances: {distance_time:.4f}s")
    
    # Plot benchmarking results
    plt.figure(figsize=(12, 8))
    
    pixels = [r['pixels'] for r in benchmark_results]
    basic_times = [r['basic_time'] for r in benchmark_results]
    all_times = [r['all_time'] for r in benchmark_results]
    distance_times = [r['distance_time'] for r in benchmark_results]
    
    plt.subplot(2, 2, 1)
    plt.plot(pixels, basic_times, 'o-', label='Basic Metrics')
    plt.plot(pixels, all_times, 's-', label='All Metrics')
    plt.plot(pixels, distance_times, '^-', label='With Distances')
    plt.xlabel('Number of Pixels')
    plt.ylabel('Time (seconds)')
    plt.title('Computation Time vs Image Size')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(2, 2, 2)
    plt.loglog(pixels, basic_times, 'o-', label='Basic Metrics')
    plt.loglog(pixels, all_times, 's-', label='All Metrics')
    plt.loglog(pixels, distance_times, '^-', label='With Distances')
    plt.xlabel('Number of Pixels')
    plt.ylabel('Time (seconds)')
    plt.title('Computation Time (Log Scale)')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(2, 2, 3)
    throughput_basic = [r['pixels'] / r['basic_time'] for r in benchmark_results]
    throughput_all = [r['pixels'] / r['all_time'] for r in benchmark_results]
    throughput_distance = [r['pixels'] / r['distance_time'] for r in benchmark_results]
    
    plt.plot(pixels, throughput_basic, 'o-', label='Basic Metrics')
    plt.plot(pixels, throughput_all, 's-', label='All Metrics')
    plt.plot(pixels, throughput_distance, '^-', label='With Distances')
    plt.xlabel('Number of Pixels')
    plt.ylabel('Throughput (pixels/second)')
    plt.title('Throughput vs Image Size')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(2, 2, 4)
    efficiency_all = [basic_times[i] / all_times[i] for i in range(len(basic_times))]
    efficiency_distance = [basic_times[i] / distance_times[i] for i in range(len(basic_times))]
    
    plt.plot(pixels, efficiency_all, 's-', label='All Metrics')
    plt.plot(pixels, efficiency_distance, '^-', label='With Distances')
    plt.xlabel('Number of Pixels')
    plt.ylabel('Efficiency (relative to basic)')
    plt.title('Computational Efficiency')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.show()


def main():
    """Main demonstration function."""
    print("Results Calculation and Comparison Comprehensive Demo")
    print("Author: Dr. Mohammed Yagoub Esmail")
    print("=" * 80)
    
    print("\nThis demo will showcase all results calculation capabilities:")
    print("1. Basic Segmentation Metrics")
    print("2. Volume Calculation and Comparison")
    print("3. Method Comparison")
    print("4. Advanced Visualization")
    print("5. Detailed Analysis and Reporting")
    print("6. Error Analysis")
    print("7. Performance Benchmarking")
    
    input("\nPress Enter to start the demonstration...")
    
    # Run demonstrations
    demonstrate_basic_metrics()
    demonstrate_volume_calculation()
    demonstrate_method_comparison()
    demonstrate_advanced_visualization()
    demonstrate_detailed_analysis()
    demonstrate_error_analysis()
    demonstrate_performance_benchmarking()
    
    print("\n" + "=" * 80)
    print("DEMONSTRATION COMPLETE")
    print("=" * 80)
    print("\nKey Takeaways:")
    print("1. Comprehensive metrics for segmentation evaluation")
    print("2. Statistical comparison between methods")
    print("3. Volume calculation and analysis")
    print("4. Professional visualization capabilities")
    print("5. Detailed reporting and analysis")
    print("6. Error analysis and interpretation")
    print("7. Performance benchmarking and optimization")
    print("\nThe system provides complete evaluation capabilities for medical image segmentation!")


if __name__ == "__main__":
    main()"""
Results Calculation and Comparison Demo
======================================

This script demonstrates comprehensive evaluation metrics for segmentation 
performance and comparison between different methods.

Author: Dr. Mohammed Yagoub Esmail
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from typing import List, Dict, Tuple
import time
import json

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from results_calculation import (
    SegmentationMetrics, VolumeCalculator, MethodComparator,
    ResultsVisualizer, ResultsReporter
)


def create_realistic_segmentation_data(n_samples: int = 50) -> Dict[str, List[np.ndarray]]:
    """
    Create realistic segmentation data with different tumor shapes and sizes.
    
    Args:
        n_samples: Number of samples to generate
        
    Returns:
        Dictionary with ground truth and predictions
    """
    print(f"Creating {n_samples} realistic segmentation samples...")
    
    np.random.seed(42)  # For reproducibility
    
    ground_truth = []
    predictions = {
        'U-Net': [],
        'FCN': [],
        'DeepLab': [],
        'Traditional': []
    }
    
    image_size = (256, 256)
    
    for i in range(n_samples):
        # Create ground truth with varying tumor characteristics
        gt = create_tumor_mask(image_size, sample_idx=i)
        ground_truth.append(gt)
        
        # Generate predictions for different methods
        predictions['U-Net'].append(simulate_unet_prediction(gt))
        predictions['FCN'].append(simulate_fcn_prediction(gt))
        predictions['DeepLab'].append(simulate_deeplab_prediction(gt))
        predictions['Traditional'].append(simulate_traditional_prediction(gt))
    
    return {
        'ground_truth': ground_truth,
        'predictions': predictions
    }


def create_tumor_mask(image_size: Tuple[int, int], sample_idx: int) -> np.ndarray:
    """Create realistic tumor mask with varying characteristics."""
    mask = np.zeros(image_size)
    
    # Vary tumor characteristics based on sample index
    if sample_idx % 4 == 0:
        # Circular tumor
        center = (128 + np.random.randint(-30, 30), 128 + np.random.randint(-30, 30))
        radius = 25 + np.random.randint(-10, 15)
        
        y, x = np.ogrid[:image_size[0], :image_size[1]]
        mask = (x - center[0])**2 + (y - center[1])**2 <= radius**2
        
    elif sample_idx % 4 == 1:
        # Elliptical tumor
        center = (128 + np.random.randint(-40, 40), 128 + np.random.randint(-40, 40))
        a = 30 + np.random.randint(-5, 10)  # Semi-major axis
        b = 20 + np.random.randint(-5, 10)  # Semi-minor axis
        
        y, x = np.ogrid[:image_size[0], :image_size[1]]
        mask = ((x - center[0])**2 / a**2 + (y - center[1])**2 / b**2) <= 1
        
    elif sample_idx % 4 == 2:
        # Irregular tumor (multiple circles)
        centers = [(100 + np.random.randint(-20, 20), 100 + np.random.randint(-20, 20)),
                  (150 + np.random.randint(-20, 20), 120 + np.random.randint(-20, 20)),
                  (125 + np.random.randint(-20, 20), 150 + np.random.randint(-20, 20))]
        
        y, x = np.ogrid[:image_size[0], :image_size[1]]
        for center in centers:
            radius = 15 + np.random.randint(-5, 10)
            mask |= (x - center[0])**2 + (y - center[1])**2 <= radius**2
    
    else:
        # Small tumor
        center = (128 + np.random.randint(-50, 50), 128 + np.random.randint(-50, 50))
        radius = 15 + np.random.randint(-5, 8)
        
        y, x = np.ogrid[:image_size[0], :image_size[1]]
        mask = (x - center[0])**2 + (y - center[1])**2 <= radius**2
    
    return mask.astype(np.uint8)


def simulate_unet_prediction(ground_truth: np.ndarray) -> np.ndarray:
    """Simulate U-Net prediction (high accuracy)."""
    # Add slight noise and boundary variations
    noise = np.random.normal(0, 0.05, ground_truth.shape)
    pred = ground_truth.astype(float) + noise
    
    # Add some boundary smoothing
    from scipy.ndimage import gaussian_filter
    pred = gaussian_filter(pred, sigma=0.8)
    
    # Apply threshold
    pred = (pred > 0.4).astype(np.uint8)
    
    return pred


def simulate_fcn_prediction(ground_truth: np.ndarray) -> np.ndarray:
    """Simulate FCN prediction (moderate accuracy)."""
    # Add more noise and some systematic errors
    noise = np.random.normal(0, 0.15, ground_truth.shape)
    pred = ground_truth.astype(float) + noise
    
    # Add some false positives
    false_positives = np.random.random(ground_truth.shape) < 0.02
    pred[false_positives] = 1.0
    
    # Apply threshold
    pred = (pred > 0.3).astype(np.uint8)
    
    return pred


def simulate_deeplab_prediction(ground_truth: np.ndarray) -> np.ndarray:
    """Simulate DeepLab prediction (good accuracy with better boundaries)."""
    # Add moderate noise
    noise = np.random.normal(0, 0.1, ground_truth.shape)
    pred = ground_truth.astype(float) + noise
    
    # Better boundary preservation
    from scipy.ndimage import binary_erosion, binary_dilation
    pred_binary = (pred > 0.3).astype(np.uint8)
    
    # Clean up with morphological operations
    pred_binary = binary_erosion(pred_binary, iterations=1)
    pred_binary = binary_dilation(pred_binary, iterations=1)
    
    return pred_binary.astype(np.uint8)


def simulate_traditional_prediction(ground_truth: np.ndarray) -> np.ndarray:
    """Simulate traditional method prediction (lower accuracy)."""
    # Add significant noise and systematic errors
    noise = np.random.normal(0, 0.25, ground_truth.shape)
    pred = ground_truth.astype(float) + noise
    
    # Add more false positives and negatives
    false_positives = np.random.random(ground_truth.shape) < 0.05
    false_negatives = np.random.random(ground_truth.shape) < 0.08
    
    pred[false_positives] = 1.0
    pred[false_negatives & (ground_truth == 1)] = 0.0
    
    # Apply threshold
    pred = (pred > 0.2).astype(np.uint8)
    
    return pred


def demonstrate_basic_metrics():
    """Demonstrate basic segmentation metrics."""
    print("\n" + "="*60)
    print("BASIC SEGMENTATION METRICS DEMONSTRATION")
    print("="*60)
    
    # Create simple test case
    gt = np.zeros((100, 100))
    gt[30:70, 30:70] = 1  # Ground truth square
    
    pred = np.zeros((100, 100))
    pred[25:65, 35:75] = 1  # Predicted square (shifted)
    
    # Calculate metrics
    metrics = SegmentationMetrics()
    
    print("Ground Truth: 40x40 square at (30,30)")
    print("Prediction: 40x40 square at (25,35)")
    print()
    
    results = metrics.calculate_all_metrics(gt, pred, include_distances=True)
    
    print("Calculated Metrics:")
    print("-" * 30)
    for metric, value in results.items():
        if value != float('inf'):
            print(f"{metric}: {value:.4f}")
        else:
            print(f"{metric}: inf")
    
    # Visualize
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    axes[0].imshow(gt, cmap='gray')
    axes[0].set_title('Ground Truth')
    axes[0].axis('off')
    
    axes[1].imshow(pred, cmap='gray')
    axes[1].set_title('Prediction')
    axes[1].axis('off')
    
    # Overlap visualization
    overlap = np.zeros((100, 100, 3))
    overlap[gt == 1] = [1, 0, 0]  # Red for ground truth
    overlap[pred == 1] = [0, 1, 0]  # Green for prediction
    overlap[(gt == 1) & (pred == 1)] = [1, 1, 0]  # Yellow for overlap
    
    axes[2].imshow(overlap)
    axes[2].set_title('Overlap (Red=GT, Green=Pred, Yellow=Both)')
    axes[2].axis('off')
    
    plt.tight_layout()
    plt.show()


def demonstrate_volume_calculation():
    """Demonstrate volume calculation and comparison."""
    print("\n" + "="*60)
    print("VOLUME CALCULATION DEMONSTRATION")
    print("="*60)
    
    # Create 3D volume
    volume_shape = (50, 128, 128)
    voxel_spacing = (2.0, 1.0, 1.0)  # z, y, x spacing in mm
    
    # Create ground truth volume
    gt_volume = np.zeros(volume_shape)
    center = (25, 64, 64)
    radius = 15
    
    for z in range(volume_shape[0]):
        for y in range(volume_shape[1]):
            for x in range(volume_shape[2]):
                distance = np.sqrt((z - center[0])**2 + (y - center[1])**2 + (x - center[2])**2)
                if distance <= radius:
                    gt_volume[z, y, x] = 1
    
    # Create predicted volume (slightly different)
    pred_volume = gt_volume.copy()
    noise = np.random.normal(0, 0.1, volume_shape)
    pred_volume = (pred_volume + noise > 0.4).astype(np.uint8)
    
    # Calculate volumes
    volume_calc = VolumeCalculator()
    
    gt_vol = volume_calc.calculate_volume(gt_volume, voxel_spacing)
    pred_vol = volume_calc.calculate_volume(pred_volume, voxel_spacing)
    
    print("Ground Truth Volume:")
    print(f"  Total volume: {gt_vol['total_volume_ml']:.2f} ml")
    print(f"  Voxel count: {gt_vol['voxel_count']}")
    print(f"  Bounding box volume: {gt_vol['bounding_box_volume_mm3']:.2f} mm³")
    print(f"  Fill ratio: {gt_vol['fill_ratio']:.3f}")
    
    print("\nPredicted Volume:")
    print(f"  Total volume: {pred_vol['total_volume_ml']:.2f} ml")
    print(f"  Voxel count: {pred_vol['voxel_count']}")
    print(f"  Bounding box volume: {pred_vol['bounding_box_volume_mm3']:.2f} mm³")
    print(f"  Fill ratio: {pred_vol['fill_ratio']:.3f}")
    
    # Volume comparison
    comparison = volume_calc.compare_volumes(gt_volume, pred_volume, voxel_spacing)
    
    print("\nVolume Comparison:")
    print(f"  Volume difference: {comparison['volume_difference_ml']:.2f} ml")
    print(f"  Relative error: {comparison['relative_error']:.3f}")
    print(f"  Absolute error: {comparison['absolute_error_ml']:.2f} ml")
    
    # Volume overlap
    overlap = volume_calc.calculate_volume_overlap(gt_volume, pred_volume, voxel_spacing)
    
    print("\nVolume Overlap:")
    print(f"  Intersection volume: {overlap['intersection_volume_mm3']:.2f} mm³")
    print(f"  Union volume: {overlap['union_volume_mm3']:.2f} mm³")
    print(f"  Dice volume: {overlap['dice_volume']:.3f}")
    print(f"  Jaccard volume: {overlap['jaccard_volume']:.3f}")
    print(f"  Overlap fraction: {overlap['overlap_fraction']:.3f}")
    
    # Visualize slices
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    mid_slice = volume_shape[0] // 2
    
    # Ground truth slices
    axes[0, 0].imshow(gt_volume[mid_slice], cmap='gray')
    axes[0, 0].set_title('GT - Axial')
    axes[0, 0].axis('off')
    
    axes[0, 1].imshow(gt_volume[:, 64, :], cmap='gray')
    axes[0, 1].set_title('GT - Coronal')
    axes[0, 1].axis('off')
    
    axes[0, 2].imshow(gt_volume[:, :, 64], cmap='gray')
    axes[0, 2].set_title('GT - Sagittal')
    axes[0, 2].axis('off')
    
    # Predicted slices
    axes[1, 0].imshow(pred_volume[mid_slice], cmap='gray')
    axes[1, 0].set_title('Pred - Axial')
    axes[1, 0].axis('off')
    
    axes[1, 1].imshow(pred_volume[:, 64, :], cmap='gray')
    axes[1, 1].set_title('Pred - Coronal')
    axes[1, 1].axis('off')
    
    axes[1, 2].imshow(pred_volume[:, :, 64], cmap='gray')
    axes[1, 2].set_title('Pred - Sagittal')
    axes[1, 2].axis('off')
    
    plt.tight_layout()
    plt.show()


def demonstrate_method_comparison():
    """Demonstrate comprehensive method comparison."""
    print("\n" + "="*60)
    print("METHOD COMPARISON DEMONSTRATION")
    print("="*60)
    
    # Create realistic data
    data = create_realistic_segmentation_data(n_samples=30)
    
    # Initialize comparator
    comparator = MethodComparator()
    
    # Add methods
    methods = ['U-Net', 'FCN', 'DeepLab', 'Traditional']
    
    for method in methods:
        print(f"Adding results for {method}...")
        comparator.add_method_results(
            method_name=method,
            ground_truth=data['ground_truth'],
            predictions=data['predictions'][method],
            voxel_spacing=(1.0, 1.0, 1.0)
        )
    
    # Calculate summary statistics
    print("\nCalculating summary statistics...")
    
    for method in methods:
        stats = comparator.calculate_summary_statistics(method)
        print(f"\n{method} Summary:")
        print(f"  Dice Coefficient: {stats['dice_coefficient']['mean']:.3f} ± {stats['dice_coefficient']['std']:.3f}")
        print(f"  Jaccard Index: {stats['jaccard_index']['mean']:.3f} ± {stats['jaccard_index']['std']:.3f}")
        print(f"  Accuracy: {stats['accuracy']['mean']:.3f} ± {stats['accuracy']['std']:.3f}")
        print(f"  Precision: {stats['precision']['mean']:.3f} ± {stats['precision']['std']:.3f}")
        print(f"  Recall: {stats['recall']['mean']:.3f} ± {stats['recall']['std']:.3f}")
    
    # Pairwise comparisons
    print("\nPairwise Statistical Comparisons:")
    print("-" * 40)
    
    comparison = comparator.compare_methods('U-Net', 'Traditional', 'dice_coefficient')
    print(f"U-Net vs Traditional (Dice Coefficient):")
    print(f"  U-Net mean: {comparison['method1_mean']:.3f}")
    print(f"  Traditional mean: {comparison['method2_mean']:.3f}")
    print(f"  Difference: {comparison['difference']:.3f}")
    print(f"  P-value: {comparison['paired_t_test']['p_value']:.4f}")
    print(f"  Significant: {comparison['paired_t_test']['significant']}")
    print(f"  Effect size: {comparison['effect_size']}")
    
    comparison = comparator.compare_methods('U-Net', 'DeepLab', 'dice_coefficient')
    print(f"\nU-Net vs DeepLab (Dice Coefficient):")
    print(f"  U-Net mean: {comparison['method1_mean']:.3f}")
    print(f"  DeepLab mean: {comparison['method2_mean']:.3f}")
    print(f"  Difference: {comparison['difference']:.3f}")
    print(f"  P-value: {comparison['paired_t_test']['p_value']:.4f}")
    print(f"  Significant: {comparison['paired_t_test']['significant']}")
    print(f"  Effect size: {comparison['effect_size']}")
    
    # Generate comprehensive report
    print("\nGenerating comprehensive report...")
    report = comparator.generate_comparison_report()
    
    print("\nMethod Rankings (Dice Coefficient):")
    for i, (method, score) in enumerate(report['rankings']['dice_coefficient']):
        print(f"  {i+1}. {method}: {score:.3f}")
    
    print(f"\nOverall Best Method: {report['best_method']['method']}")
    print(f"Average Rank: {report['best_method']['average_rank']:.2f}")
    
    return comparator


def demonstrate_advanced_visualization():
    """Demonstrate advanced visualization capabilities."""
    print("\n" + "="*60)
    print("ADVANCED VISUALIZATION DEMONSTRATION")
    print("="*60)
    
    # Create data and comparator
    data = create_realistic_segmentation_data(n_samples=25)
    comparator = MethodComparator()
    
    methods = ['U-Net', 'FCN', 'DeepLab', 'Traditional']
    
    for method in methods:
        comparator.add_method_results(
            method_name=method,
            ground_truth=data['ground_truth'],
            predictions=data['predictions'][method],
            voxel_spacing=(1.0, 1.0, 1.0)
        )
    
    # Create visualizer
    visualizer = ResultsVisualizer()
    
    print("Creating metrics comparison plot...")
    visualizer.plot_metrics_comparison(comparator)
    
    print("Creating method ranking plot...")
    visualizer.plot_method_ranking(comparator)
    
    print("Creating volume comparison plot...")
    visualizer.plot_volume_comparison(comparator)
    
    print("Creating performance summary...")
    visualizer.plot_performance_summary(comparator)
    
    print("Creating comprehensive results dashboard...")
    visualizer.create_results_dashboard(comparator)
    
    return comparator


def demonstrate_detailed_analysis():
    """Demonstrate detailed analysis capabilities."""
    print("\n" + "="*60)
    print("DETAILED ANALYSIS DEMONSTRATION")
    print("="*60)
    
    # Create comparator with data
    comparator = demonstrate_method_comparison()
    
    # Generate detailed reports
    reporter = ResultsReporter()
    
    print("Generating text report...")
    text_report = reporter.generate_text_report(comparator)
    
    print("Text Report Sample:")
    print("-" * 30)
    print(text_report[:1000] + "...")
    
    print("\nGenerating JSON report...")
    json_report = reporter.generate_json_report(comparator)
    
    print(f"JSON report contains {len(json_report)} main sections:")
    for key in json_report.keys():
        print(f"  - {key}")
    
    print("\nGenerating CSV summary...")
    csv_summary = reporter.generate_csv_summary(comparator)
    
    print("CSV Summary:")
    print("-" * 30)
    print(csv_summary.head())
    
    # Detailed statistical analysis
    print("\nDetailed Statistical Analysis:")
    print("-" * 40)
    
    methods = list(comparator.results.keys())
    
    # Calculate confidence intervals
    for method in methods:
        dice_values = [m['dice_coefficient'] for m in comparator.results[method]['metrics']]
        mean_dice = np.mean(dice_values)
        std_dice = np.std(dice_values)
        n = len(dice_values)
        
        # 95% confidence interval
        confidence_interval = 1.96 * std_dice / np.sqrt(n)
        
        print(f"{method}:")
        print(f"  Dice Coefficient: {mean_dice:.3f} ± {std_dice:.3f}")
        print(f"  95% CI: [{mean_dice - confidence_interval:.3f}, {mean_dice + confidence_interval:.3f}]")
        print(f"  Sample size: {n}")
    
    # Performance consistency analysis
    print("\nPerformance Consistency Analysis:")
    print("-" * 40)
    
    for method in methods:
        dice_values = [m['dice_coefficient'] for m in comparator.results[method]['metrics']]
        cv = np.std(dice_values) / np.mean(dice_values)  # Coefficient of variation
        
        print(f"{method}:")
        print(f"  Coefficient of Variation: {cv:.3f}")
        print(f"  Consistency: {'High' if cv < 0.1 else 'Medium' if cv < 0.2 else 'Low'}")


def demonstrate_error_analysis():
    """Demonstrate error analysis capabilities."""
    print("\n" + "="*60)
    print("ERROR ANALYSIS DEMONSTRATION")
    print("="*60)
    
    # Create specific test cases for error analysis
    test_cases = create_error_analysis_cases()
    
    metrics = SegmentationMetrics()
    
    print("Analyzing different types of segmentation errors...")
    
    for case_name, (gt, pred) in test_cases.items():
        print(f"\n{case_name}:")
        print("-" * 30)
        
        results = metrics.calculate_all_metrics(gt, pred, include_distances=False)
        
        print(f"  Dice Coefficient: {results['dice_coefficient']:.3f}")
        print(f"  Jaccard Index: {results['jaccard_index']:.3f}")
        print(f"  Precision: {results['precision']:.3f}")
        print(f"  Recall: {results['recall']:.3f}")
        print(f"  Specificity: {results['specificity']:.3f}")
        
        # Analyze error type
        precision = results['precision']
        recall = results['recall']
        
        if precision > 0.8 and recall > 0.8:
            error_type = "Good segmentation"
        elif precision > recall:
            error_type = "Under-segmentation (missed regions)"
        elif recall > precision:
            error_type = "Over-segmentation (false positives)"
        else:
            error_type = "Mixed errors"
        
        print(f"  Error Type: {error_type}")
    
    # Visualize error cases
    visualize_error_cases(test_cases)


def create_error_analysis_cases() -> Dict[str, Tuple[np.ndarray, np.ndarray]]:
    """Create specific test cases for error analysis."""
    size = (100, 100)
    
    # Ground truth: circle in center
    gt = np.zeros(size)
    y, x = np.ogrid[:size[0], :size[1]]
    gt[(x - 50)**2 + (y - 50)**2 <= 20**2] = 1
    
    cases = {}
    
    # Case 1: Perfect segmentation
    cases['Perfect Segmentation'] = (gt, gt.copy())
    
    # Case 2: Under-segmentation
    under_seg = gt.copy()
    under_seg[(x - 50)**2 + (y - 50)**2 <= 15**2] = 0  # Remove center
    cases['Under-segmentation'] = (gt, under_seg)
    
    # Case 3: Over-segmentation
    over_seg = gt.copy()
    over_seg[(x - 50)**2 + (y - 50)**2 <= 25**2] = 1  # Larger circle
    cases['Over-segmentation'] = (gt, over_seg)
    
    # Case 4: Boundary shift
    boundary_shift = np.zeros(size)
    boundary_shift[(x - 45)**2 + (y - 45)**2 <= 20**2] = 1  # Shifted circle
    cases['Boundary Shift'] = (gt, boundary_shift)
    
    # Case 5: Fragmentation
    fragmented = gt.copy()
    fragmented[45:55, 45:55] = 0  # Remove center part
    cases['Fragmentation'] = (gt, fragmented)
    
    # Case 6: False positives
    false_pos = gt.copy()
    false_pos[20:30, 20:30] = 1  # Add false positive region
    cases['False Positives'] = (gt, false_pos)
    
    return cases


def visualize_error_cases(test_cases: Dict[str, Tuple[np.ndarray, np.ndarray]]):
    """Visualize different error cases."""
    n_cases = len(test_cases)
    fig, axes = plt.subplots(2, n_cases, figsize=(4 * n_cases, 8))
    
    for i, (case_name, (gt, pred)) in enumerate(test_cases.items()):
        # Ground truth
        axes[0, i].imshow(gt, cmap='gray')
        axes[0, i].set_title(f'{case_name}\nGround Truth')
        axes[0, i].axis('off')
        
        # Prediction with error visualization
        error_vis = np.zeros((*gt.shape, 3))
        error_vis[gt == 1] = [0, 1, 0]  # Green for true positives
        error_vis[(gt == 0) & (pred == 1)] = [1, 0, 0]  # Red for false positives
        error_vis[(gt == 1) & (pred == 0)] = [0, 0, 1]  # Blue for false negatives
        
        axes[1, i].imshow(error_vis)
        axes[1, i].set_title(f'{case_name}\nErrors (Red=FP, Blue=FN)')
        axes[1, i].axis('off')
    
    plt.tight_layout()
    plt.show()


def demonstrate_performance_benchmarking():
    """Demonstrate performance benchmarking."""
    print("\n" + "="*60)
    print("PERFORMANCE BENCHMARKING DEMONSTRATION")
    print("="*60)
    
    # Test different image sizes
    sizes = [(64, 64), (128, 128), (256, 256), (512, 512)]
    metrics = SegmentationMetrics()
    
    print("Benchmarking computation times for different image sizes...")
    
    benchmark_results = []
    
    for size in sizes:
        print(f"\nTesting size {size}...")
        
        # Create test data
        gt = np.random.randint(0, 2, size)
        pred = np.random.randint(0, 2, size)
        
        # Time basic metrics
        start_time = time.time()
        basic_metrics = {
            'dice': metrics.dice_coefficient(gt, pred),
            'jaccard': metrics.jaccard_index(gt, pred),
            'accuracy': metrics.accuracy(gt, pred),
            'precision': metrics.precision(gt, pred),
            'recall': metrics.recall(gt, pred)
        }
        basic_time = time.time() - start_time
        
        # Time all metrics
        start_time = time.time()
        all_metrics = metrics.calculate_all_metrics(gt, pred, include_distances=False)
        all_time = time.time() - start_time
        
        # Time with distances
        start_time = time.time()
        distance_metrics = metrics.calculate_all_metrics(gt, pred, include_distances=True)
        distance_time = time.time() - start_time
        
        benchmark_results.append({
            'size': size,
            'pixels': size[0] * size[1],
            'basic_time': basic_time,
            'all_time': all_time,
            'distance_time': distance_time
        })
        
        print(f"  Basic metrics: {basic_time:.4f}s")
        print(f"  All metrics: {all_time:.4f}s")
        print(f"  With distances: {distance_time:.4f}s")
    
    # Plot benchmarking results
    plt.figure(figsize=(12, 8))
    
    pixels = [r['pixels'] for r in benchmark_results]
    basic_times = [r['basic_time'] for r in benchmark_results]
    all_times = [r['all_time'] for r in benchmark_results]
    distance_times = [r['distance_time'] for r in benchmark_results]
    
    plt.subplot(2, 2, 1)
    plt.plot(pixels, basic_times, 'o-', label='Basic Metrics')
    plt.plot(pixels, all_times, 's-', label='All Metrics')
    plt.plot(pixels, distance_times, '^-', label='With Distances')
    plt.xlabel('Number of Pixels')
    plt.ylabel('Time (seconds)')
    plt.title('Computation Time vs Image Size')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(2, 2, 2)
    plt.loglog(pixels, basic_times, 'o-', label='Basic Metrics')
    plt.loglog(pixels, all_times, 's-', label='All Metrics')
    plt.loglog(pixels, distance_times, '^-', label='With Distances')
    plt.xlabel('Number of Pixels')
    plt.ylabel('Time (seconds)')
    plt.title('Computation Time (Log Scale)')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(2, 2, 3)
    throughput_basic = [r['pixels'] / r['basic_time'] for r in benchmark_results]
    throughput_all = [r['pixels'] / r['all_time'] for r in benchmark_results]
    throughput_distance = [r['pixels'] / r['distance_time'] for r in benchmark_results]
    
    plt.plot(pixels, throughput_basic, 'o-', label='Basic Metrics')
    plt.plot(pixels, throughput_all, 's-', label='All Metrics')
    plt.plot(pixels, throughput_distance, '^-', label='With Distances')
    plt.xlabel('Number of Pixels')
    plt.ylabel('Throughput (pixels/second)')
    plt.title('Throughput vs Image Size')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(2, 2, 4)
    efficiency_all = [basic_times[i] / all_times[i] for i in range(len(basic_times))]
    efficiency_distance = [basic_times[i] / distance_times[i] for i in range(len(basic_times))]
    
    plt.plot(pixels, efficiency_all, 's-', label='All Metrics')
    plt.plot(pixels, efficiency_distance, '^-', label='With Distances')
    plt.xlabel('Number of Pixels')
    plt.ylabel('Efficiency (relative to basic)')
    plt.title('Computational Efficiency')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.show()


def main():
    """Main demonstration function."""
    print("Results Calculation and Comparison Comprehensive Demo")
    print("Author: Dr. Mohammed Yagoub Esmail")
    print("=" * 80)
    
    print("\nThis demo will showcase all results calculation capabilities:")
    print("1. Basic Segmentation Metrics")
    print("2. Volume Calculation and Comparison")
    print("3. Method Comparison")
    print("4. Advanced Visualization")
    print("5. Detailed Analysis and Reporting")
    print("6. Error Analysis")
    print("7. Performance Benchmarking")
    
    input("\nPress Enter to start the demonstration...")
    
    # Run demonstrations
    demonstrate_basic_metrics()
    demonstrate_volume_calculation()
    demonstrate_method_comparison()
    demonstrate_advanced_visualization()
    demonstrate_detailed_analysis()
    demonstrate_error_analysis()
    demonstrate_performance_benchmarking()
    
    print("\n" + "=" * 80)
    print("DEMONSTRATION COMPLETE")
    print("=" * 80)
    print("\nKey Takeaways:")
    print("1. Comprehensive metrics for segmentation evaluation")
    print("2. Statistical comparison between methods")
    print("3. Volume calculation and analysis")
    print("4. Professional visualization capabilities")
    print("5. Detailed reporting and analysis")
    print("6. Error analysis and interpretation")
    print("7. Performance benchmarking and optimization")
    print("\nThe system provides complete evaluation capabilities for medical image segmentation!")


if __name__ == "__main__":
    main()