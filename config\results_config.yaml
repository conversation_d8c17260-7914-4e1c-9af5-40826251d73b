# Results Calculation and Comparison Configuration
# Author: Dr. <PERSON>

# General Settings
general:
  random_seed: 42
  verbose: true
  precision: 4  # Number of decimal places for results
  save_intermediate_results: true
  
# Segmentation Metrics Settings
segmentation_metrics:
  # Basic metrics
  dice_coefficient:
    enabled: true
    smooth: 1e-6  # Smoothing factor for numerical stability
    
  jaccard_index:
    enabled: true
    smooth: 1e-6
    
  accuracy:
    enabled: true
    
  precision:
    enabled: true
    smooth: 1e-6
    
  recall:
    enabled: true
    smooth: 1e-6
    
  specificity:
    enabled: true
    smooth: 1e-6
    
  f1_score:
    enabled: true
    smooth: 1e-6
    
  # Advanced metrics
  hausdorff_distance:
    enabled: true
    compute_by_default: false  # Computationally expensive
    
  average_surface_distance:
    enabled: true
    compute_by_default: false
    
  volume_similarity:
    enabled: true
    
  # Performance settings
  parallel_processing: true
  cache_results: true
  
# Volume Calculation Settings
volume_calculation:
  # Default voxel spacing (mm)
  default_spacing: [1.0, 1.0, 1.0]  # z, y, x
  
  # Units
  primary_unit: 'mm'  # Primary unit for calculations
  report_units: ['mm', 'cm', 'ml']  # Units to include in reports
  
  # Volume metrics
  calculate_bounding_box: true
  calculate_fill_ratio: true
  calculate_overlap_metrics: true
  
  # Quality control
  validate_spacing: true
  min_spacing: 0.01  # Minimum allowed spacing
  max_spacing: 100.0  # Maximum allowed spacing
  
  # Morphological operations
  apply_morphological_ops: false
  erosion_iterations: 1
  dilation_iterations: 1

# Method Comparison Settings
method_comparison:
  # Statistical tests
  statistical_tests:
    paired_t_test: true
    wilcoxon_signed_rank: true
    mann_whitney_u: true
    
  # Effect size calculation
  calculate_effect_size: true
  effect_size_interpretation:
    negligible: 0.2
    small: 0.5
    medium: 0.8
    # large: > 0.8
    
  # Confidence intervals
  confidence_level: 0.95
  bootstrap_samples: 1000
  
  # Multiple comparisons correction
  multiple_comparisons:
    enabled: true
    method: 'bonferroni'  # 'bonferroni', 'holm', 'fdr'
    
  # Ranking methods
  ranking_metrics: ['dice_coefficient', 'jaccard_index', 'accuracy', 'precision', 'recall']
  ranking_weights: [0.3, 0.2, 0.2, 0.15, 0.15]  # Weights for overall ranking
  
# Visualization Settings
visualization:
  # General settings
  style: 'seaborn-v0_8'  # matplotlib style
  color_palette: 'Set3'  # Color palette
  figure_size: [12, 8]  # Default figure size
  dpi: 300  # Resolution for saved figures
  
  # Plot types
  box_plots:
    enabled: true
    show_outliers: true
    patch_artist: true
    alpha: 0.7
    
  bar_plots:
    enabled: true
    show_values: true
    error_bars: true
    
  scatter_plots:
    enabled: true
    alpha: 0.7
    point_size: 50
    
  heatmaps:
    enabled: true
    annotate: true
    colormap: 'RdYlBu_r'
    
  radar_charts:
    enabled: true
    fill_alpha: 0.25
    
  # Dashboard settings
  dashboard:
    create_by_default: true
    include_all_metrics: true
    subplot_layout: 'auto'  # 'auto' or custom grid
    
  # Export settings
  export_format: 'png'  # 'png', 'svg', 'pdf'
  export_dpi: 300
  save_plots: true
  
# Reporting Settings
reporting:
  # Report types
  text_report:
    enabled: true
    include_statistics: true
    include_comparisons: true
    include_rankings: true
    
  json_report:
    enabled: true
    pretty_print: true
    include_metadata: true
    
  csv_report:
    enabled: true
    include_all_metrics: true
    
  html_report:
    enabled: false
    include_plots: true
    template: 'default'
    
  pdf_report:
    enabled: false
    include_plots: true
    
  # Report content
  include_method_details: true
  include_sample_information: true
  include_processing_times: true
  include_confidence_intervals: true
  
  # Metadata
  author: 'Dr. Mohammed Yagoub Esmail'
  institution: 'SUST - BME'
  version: '1.0'
  
# Performance Settings
performance:
  # Parallel processing
  use_multiprocessing: true
  max_workers: -1  # -1 for all available cores
  
  # Memory management
  memory_limit: 8  # GB
  chunk_size: 1000  # Process in chunks
  
  # Caching
  enable_cache: true
  cache_size: 1000  # Number of cached results
  
  # Progress tracking
  show_progress: true
  progress_bar: true
  
  # Optimization
  optimize_calculations: true
  use_vectorized_operations: true
  
# Quality Control Settings
quality_control:
  # Input validation
  validate_inputs: true
  check_binary_masks: true
  check_dimensions: true
  
  # Data quality checks
  min_positive_pixels: 10  # Minimum number of positive pixels
  max_image_size: [2048, 2048]  # Maximum image dimensions
  
  # Metric validation
  validate_metrics: true
  metric_ranges:
    dice_coefficient: [0.0, 1.0]
    jaccard_index: [0.0, 1.0]
    accuracy: [0.0, 1.0]
    precision: [0.0, 1.0]
    recall: [0.0, 1.0]
    specificity: [0.0, 1.0]
    
  # Outlier detection
  detect_outliers: true
  outlier_method: 'iqr'  # 'iqr', 'zscore', 'isolation_forest'
  outlier_threshold: 3.0
  
  # Error handling
  handle_empty_masks: true
  handle_invalid_values: true
  strict_mode: false
  
# Advanced Features Settings
advanced:
  # Bootstrapping
  bootstrap:
    enabled: false
    n_samples: 1000
    confidence_level: 0.95
    
  # Cross-validation
  cross_validation:
    enabled: false
    n_folds: 5
    stratified: true
    
  # Bayesian analysis
  bayesian:
    enabled: false
    prior_distribution: 'uniform'
    n_samples: 10000
    
  # Robust statistics
  robust_statistics:
    enabled: false
    use_median: true
    use_mad: true  # Median Absolute Deviation
    
  # Time series analysis
  time_series:
    enabled: false
    trend_analysis: true
    seasonal_analysis: false
    
# Clinical Settings
clinical:
  # Clinical metrics
  clinical_significance:
    enabled: false
    volume_threshold: 0.5  # ml
    dice_threshold: 0.7
    
  # Diagnostic metrics
  diagnostic:
    sensitivity_threshold: 0.8
    specificity_threshold: 0.9
    ppv_threshold: 0.8  # Positive Predictive Value
    npv_threshold: 0.9  # Negative Predictive Value
    
  # Follow-up analysis
  followup:
    enabled: false
    track_changes: true
    change_threshold: 0.1  # 10% change
    
  # Multi-class support
  multi_class:
    enabled: false
    classes: ['background', 'tumor', 'edema', 'necrosis']
    class_weights: [0.1, 0.4, 0.3, 0.2]
    
# Experimental Features
experimental:
  # Deep learning metrics
  deep_learning:
    enabled: false
    perceptual_loss: false
    adversarial_loss: false
    
  # Uncertainty quantification
  uncertainty:
    enabled: false
    monte_carlo_dropout: false
    ensemble_methods: false
    
  # Explainable AI
  explainable_ai:
    enabled: false
    grad_cam: false
    lime: false
    
# Integration Settings
integration:
  # External tools
  external_tools:
    itk_snap: false
    slicer_3d: false
    imagej: false
    
  # Database integration
  database:
    enabled: false
    type: 'sqlite'  # 'sqlite', 'postgresql', 'mysql'
    connection_string: ''
    
  # Cloud integration
  cloud:
    enabled: false
    provider: 'aws'  # 'aws', 'gcp', 'azure'
    
  # API integration
  api:
    enabled: false
    rest_api: false
    graphql: false
    
# Logging and Debugging
logging:
  level: 'INFO'  # 'DEBUG', 'INFO', 'WARNING', 'ERROR'
  log_file: 'results_calculation.log'
  
  # Detailed logging
  log_metrics: true
  log_comparisons: true
  log_timings: true
  
  # Debug features
  debug_mode: false
  save_debug_info: false
  profile_performance: false
  
# File I/O Settings
file_io:
  # Supported formats
  input_formats: ['npy', 'nii', 'dcm', 'png', 'jpg', 'tiff']
  output_formats: ['npy', 'json', 'csv', 'html', 'pdf']
  
  # Compression
  compression:
    enabled: true
    level: 6  # Compression level
    
  # Backup
  backup:
    enabled: true
    max_backups: 5
    
  # Paths
  default_input_path: 'data/input'
  default_output_path: 'results/output'
  temp_path: 'temp'
  
# Security Settings
security:
  # Data protection
  anonymize_data: false
  encrypt_outputs: false
  
  # Access control
  require_authentication: false
  allowed_users: []
  
  # Audit trail
  audit_trail: false
  log_user_actions: false# Results Calculation and Comparison Configuration
# Author: Dr. Mohammed Yagoub Esmail

# General Settings
general:
  random_seed: 42
  verbose: true
  precision: 4  # Number of decimal places for results
  save_intermediate_results: true
  
# Segmentation Metrics Settings
segmentation_metrics:
  # Basic metrics
  dice_coefficient:
    enabled: true
    smooth: 1e-6  # Smoothing factor for numerical stability
    
  jaccard_index:
    enabled: true
    smooth: 1e-6
    
  accuracy:
    enabled: true
    
  precision:
    enabled: true
    smooth: 1e-6
    
  recall:
    enabled: true
    smooth: 1e-6
    
  specificity:
    enabled: true
    smooth: 1e-6
    
  f1_score:
    enabled: true
    smooth: 1e-6
    
  # Advanced metrics
  hausdorff_distance:
    enabled: true
    compute_by_default: false  # Computationally expensive
    
  average_surface_distance:
    enabled: true
    compute_by_default: false
    
  volume_similarity:
    enabled: true
    
  # Performance settings
  parallel_processing: true
  cache_results: true
  
# Volume Calculation Settings
volume_calculation:
  # Default voxel spacing (mm)
  default_spacing: [1.0, 1.0, 1.0]  # z, y, x
  
  # Units
  primary_unit: 'mm'  # Primary unit for calculations
  report_units: ['mm', 'cm', 'ml']  # Units to include in reports
  
  # Volume metrics
  calculate_bounding_box: true
  calculate_fill_ratio: true
  calculate_overlap_metrics: true
  
  # Quality control
  validate_spacing: true
  min_spacing: 0.01  # Minimum allowed spacing
  max_spacing: 100.0  # Maximum allowed spacing
  
  # Morphological operations
  apply_morphological_ops: false
  erosion_iterations: 1
  dilation_iterations: 1

# Method Comparison Settings
method_comparison:
  # Statistical tests
  statistical_tests:
    paired_t_test: true
    wilcoxon_signed_rank: true
    mann_whitney_u: true
    
  # Effect size calculation
  calculate_effect_size: true
  effect_size_interpretation:
    negligible: 0.2
    small: 0.5
    medium: 0.8
    # large: > 0.8
    
  # Confidence intervals
  confidence_level: 0.95
  bootstrap_samples: 1000
  
  # Multiple comparisons correction
  multiple_comparisons:
    enabled: true
    method: 'bonferroni'  # 'bonferroni', 'holm', 'fdr'
    
  # Ranking methods
  ranking_metrics: ['dice_coefficient', 'jaccard_index', 'accuracy', 'precision', 'recall']
  ranking_weights: [0.3, 0.2, 0.2, 0.15, 0.15]  # Weights for overall ranking
  
# Visualization Settings
visualization:
  # General settings
  style: 'seaborn-v0_8'  # matplotlib style
  color_palette: 'Set3'  # Color palette
  figure_size: [12, 8]  # Default figure size
  dpi: 300  # Resolution for saved figures
  
  # Plot types
  box_plots:
    enabled: true
    show_outliers: true
    patch_artist: true
    alpha: 0.7
    
  bar_plots:
    enabled: true
    show_values: true
    error_bars: true
    
  scatter_plots:
    enabled: true
    alpha: 0.7
    point_size: 50
    
  heatmaps:
    enabled: true
    annotate: true
    colormap: 'RdYlBu_r'
    
  radar_charts:
    enabled: true
    fill_alpha: 0.25
    
  # Dashboard settings
  dashboard:
    create_by_default: true
    include_all_metrics: true
    subplot_layout: 'auto'  # 'auto' or custom grid
    
  # Export settings
  export_format: 'png'  # 'png', 'svg', 'pdf'
  export_dpi: 300
  save_plots: true
  
# Reporting Settings
reporting:
  # Report types
  text_report:
    enabled: true
    include_statistics: true
    include_comparisons: true
    include_rankings: true
    
  json_report:
    enabled: true
    pretty_print: true
    include_metadata: true
    
  csv_report:
    enabled: true
    include_all_metrics: true
    
  html_report:
    enabled: false
    include_plots: true
    template: 'default'
    
  pdf_report:
    enabled: false
    include_plots: true
    
  # Report content
  include_method_details: true
  include_sample_information: true
  include_processing_times: true
  include_confidence_intervals: true
  
  # Metadata
  author: 'Dr. Mohammed Yagoub Esmail'
  institution: 'SUST - BME'
  version: '1.0'
  
# Performance Settings
performance:
  # Parallel processing
  use_multiprocessing: true
  max_workers: -1  # -1 for all available cores
  
  # Memory management
  memory_limit: 8  # GB
  chunk_size: 1000  # Process in chunks
  
  # Caching
  enable_cache: true
  cache_size: 1000  # Number of cached results
  
  # Progress tracking
  show_progress: true
  progress_bar: true
  
  # Optimization
  optimize_calculations: true
  use_vectorized_operations: true
  
# Quality Control Settings
quality_control:
  # Input validation
  validate_inputs: true
  check_binary_masks: true
  check_dimensions: true
  
  # Data quality checks
  min_positive_pixels: 10  # Minimum number of positive pixels
  max_image_size: [2048, 2048]  # Maximum image dimensions
  
  # Metric validation
  validate_metrics: true
  metric_ranges:
    dice_coefficient: [0.0, 1.0]
    jaccard_index: [0.0, 1.0]
    accuracy: [0.0, 1.0]
    precision: [0.0, 1.0]
    recall: [0.0, 1.0]
    specificity: [0.0, 1.0]
    
  # Outlier detection
  detect_outliers: true
  outlier_method: 'iqr'  # 'iqr', 'zscore', 'isolation_forest'
  outlier_threshold: 3.0
  
  # Error handling
  handle_empty_masks: true
  handle_invalid_values: true
  strict_mode: false
  
# Advanced Features Settings
advanced:
  # Bootstrapping
  bootstrap:
    enabled: false
    n_samples: 1000
    confidence_level: 0.95
    
  # Cross-validation
  cross_validation:
    enabled: false
    n_folds: 5
    stratified: true
    
  # Bayesian analysis
  bayesian:
    enabled: false
    prior_distribution: 'uniform'
    n_samples: 10000
    
  # Robust statistics
  robust_statistics:
    enabled: false
    use_median: true
    use_mad: true  # Median Absolute Deviation
    
  # Time series analysis
  time_series:
    enabled: false
    trend_analysis: true
    seasonal_analysis: false
    
# Clinical Settings
clinical:
  # Clinical metrics
  clinical_significance:
    enabled: false
    volume_threshold: 0.5  # ml
    dice_threshold: 0.7
    
  # Diagnostic metrics
  diagnostic:
    sensitivity_threshold: 0.8
    specificity_threshold: 0.9
    ppv_threshold: 0.8  # Positive Predictive Value
    npv_threshold: 0.9  # Negative Predictive Value
    
  # Follow-up analysis
  followup:
    enabled: false
    track_changes: true
    change_threshold: 0.1  # 10% change
    
  # Multi-class support
  multi_class:
    enabled: false
    classes: ['background', 'tumor', 'edema', 'necrosis']
    class_weights: [0.1, 0.4, 0.3, 0.2]
    
# Experimental Features
experimental:
  # Deep learning metrics
  deep_learning:
    enabled: false
    perceptual_loss: false
    adversarial_loss: false
    
  # Uncertainty quantification
  uncertainty:
    enabled: false
    monte_carlo_dropout: false
    ensemble_methods: false
    
  # Explainable AI
  explainable_ai:
    enabled: false
    grad_cam: false
    lime: false
    
# Integration Settings
integration:
  # External tools
  external_tools:
    itk_snap: false
    slicer_3d: false
    imagej: false
    
  # Database integration
  database:
    enabled: false
    type: 'sqlite'  # 'sqlite', 'postgresql', 'mysql'
    connection_string: ''
    
  # Cloud integration
  cloud:
    enabled: false
    provider: 'aws'  # 'aws', 'gcp', 'azure'
    
  # API integration
  api:
    enabled: false
    rest_api: false
    graphql: false
    
# Logging and Debugging
logging:
  level: 'INFO'  # 'DEBUG', 'INFO', 'WARNING', 'ERROR'
  log_file: 'results_calculation.log'
  
  # Detailed logging
  log_metrics: true
  log_comparisons: true
  log_timings: true
  
  # Debug features
  debug_mode: false
  save_debug_info: false
  profile_performance: false
  
# File I/O Settings
file_io:
  # Supported formats
  input_formats: ['npy', 'nii', 'dcm', 'png', 'jpg', 'tiff']
  output_formats: ['npy', 'json', 'csv', 'html', 'pdf']
  
  # Compression
  compression:
    enabled: true
    level: 6  # Compression level
    
  # Backup
  backup:
    enabled: true
    max_backups: 5
    
  # Paths
  default_input_path: 'data/input'
  default_output_path: 'results/output'
  temp_path: 'temp'
  
# Security Settings
security:
  # Data protection
  anonymize_data: false
  encrypt_outputs: false
  
  # Access control
  require_authentication: false
  allowed_users: []
  
  # Audit trail
  audit_trail: false
  log_user_actions: false