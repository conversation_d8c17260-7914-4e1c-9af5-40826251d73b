# Feature Extraction Configuration
# Author: Dr. <PERSON>smail

# General Settings
general:
  random_seed: 42
  n_jobs: -1  # Use all available cores
  verbose: true

# Texture Features Configuration
texture:
  glcm:
    distances: [1, 2, 3, 4, 5]
    angles: [0, 45, 90, 135]  # degrees
    levels: 256
    symmetric: true
    normed: true
    properties: ['contrast', 'dissimilarity', 'homogeneity', 'energy', 'correlation', 'ASM']
  
  lbp:
    radius: 3
    n_points: 24
    method: 'uniform'
    
  edge_detection:
    operators: ['roberts', 'sobel', 'scharr', 'prewitt']
    
  gabor:
    frequencies: [0.1, 0.3, 0.5]
    angles: [0, 45, 90, 135]
    sigma_x: 2
    sigma_y: 2

# Shape Features Configuration
shape:
  basic_features:
    - area
    - perimeter
    - circularity
    - compactness
    - extent
    - solidity
    - aspect_ratio
    - equivalent_diameter
    - bounding_box_area
    - convex_area
    
  advanced_features:
    - eccentricity
    - major_axis_length
    - minor_axis_length
    - orientation
    - elongation
    - roundness
    - fractal_dimension
    
  moments:
    - hu_moments  # 7 Hu invariant moments
    - central_moments
    - normalized_moments
    
  fractal:
    min_box_size: 2
    max_box_size: 128
    n_scales: 20

# Statistical Features Configuration
statistical:
  first_order:
    - mean
    - std
    - variance
    - median
    - mode
    - min
    - max
    - range
    - iqr
    - skewness
    - kurtosis
    - entropy
    - energy
    - coefficient_of_variation
    
  percentiles: [5, 10, 25, 50, 75, 90, 95, 99]
  
  second_order:
    - gradient_mean
    - gradient_std
    - gradient_max
    - laplacian_mean
    - laplacian_std
    - laplacian_variance
    
  histogram:
    bins: 256
    range: [0, 255]

# Feature Selection Configuration
feature_selection:
  methods:
    univariate:
      k_best: 20
      score_function: 'f_classif'
      
    rfe:
      n_features: 20
      estimator: 'random_forest'
      step: 1
      
    importance:
      threshold: 0.01
      estimator: 'random_forest'
      n_estimators: 100
      
    lasso:
      alpha: 0.01
      max_iter: 1000
      
    mutual_info:
      k_best: 20
      
  preprocessing:
    standardization: true
    normalization: false
    remove_low_variance: true
    variance_threshold: 0.01

# Visualization Configuration
visualization:
  figure_size: [12, 8]
  dpi: 300
  style: 'seaborn-v0_8'
  color_palette: 'Set1'
  
  plots:
    distribution:
      bins: 50
      alpha: 0.7
      
    correlation:
      method: 'pearson'
      annot: true
      fmt: '.2f'
      
    importance:
      top_n: 20
      orientation: 'horizontal'
      
    clustering:
      method: 'ward'
      metric: 'euclidean'
      
    dimensionality_reduction:
      tsne:
        perplexity: 30
        n_iter: 1000
        learning_rate: 200
      pca:
        n_components: 2
        whiten: false

# Output Configuration
output:
  save_features: true
  save_plots: true
  save_statistics: true
  
  file_formats:
    features: 'csv'
    plots: 'png'
    statistics: 'json'
    
  directories:
    features: 'results/features'
    plots: 'results/plots'
    statistics: 'results/statistics'

# Performance Configuration
performance:
  metrics:
    classification:
      - accuracy
      - precision
      - recall
      - f1_score
      - roc_auc
      
    regression:
      - mse
      - rmse
      - mae
      - r2
      
  cross_validation:
    folds: 5
    shuffle: true
    stratify: true
    
  validation:
    test_size: 0.2
    random_state: 42

# Feature Categories for Analysis
feature_categories:
  texture:
    - glcm_contrast_mean
    - glcm_dissimilarity_mean
    - glcm_homogeneity_mean
    - glcm_energy_mean
    - glcm_correlation_mean
    - lbp_uniformity
    - lbp_entropy
    - edge_roberts_mean
    - edge_sobel_mean
    
  shape:
    - area
    - perimeter
    - circularity
    - eccentricity
    - solidity
    - extent
    - compactness
    - aspect_ratio
    - equivalent_diameter
    - fractal_dimension
    
  statistical:
    - mean
    - std
    - variance
    - skewness
    - kurtosis
    - entropy
    - energy
    - median
    - percentile_25
    - percentile_75
    
  intensity:
    - min
    - max
    - range
    - gradient_mean
    - gradient_std
    - laplacian_mean
    - coefficient_of_variation

# Quality Control
quality_control:
  feature_validation:
    check_nan: true
    check_inf: true
    check_variance: true
    min_variance: 1e-6
    
  outlier_detection:
    method: 'iqr'
    threshold: 1.5
    
  feature_stability:
    min_stability: 0.8
    n_bootstrap: 100# Feature Extraction Configuration
# Author: Dr. Mohammed Yagoub Esmail

# General Settings
general:
  random_seed: 42
  n_jobs: -1  # Use all available cores
  verbose: true

# Texture Features Configuration
texture:
  glcm:
    distances: [1, 2, 3, 4, 5]
    angles: [0, 45, 90, 135]  # degrees
    levels: 256
    symmetric: true
    normed: true
    properties: ['contrast', 'dissimilarity', 'homogeneity', 'energy', 'correlation', 'ASM']
  
  lbp:
    radius: 3
    n_points: 24
    method: 'uniform'
    
  edge_detection:
    operators: ['roberts', 'sobel', 'scharr', 'prewitt']
    
  gabor:
    frequencies: [0.1, 0.3, 0.5]
    angles: [0, 45, 90, 135]
    sigma_x: 2
    sigma_y: 2

# Shape Features Configuration
shape:
  basic_features:
    - area
    - perimeter
    - circularity
    - compactness
    - extent
    - solidity
    - aspect_ratio
    - equivalent_diameter
    - bounding_box_area
    - convex_area
    
  advanced_features:
    - eccentricity
    - major_axis_length
    - minor_axis_length
    - orientation
    - elongation
    - roundness
    - fractal_dimension
    
  moments:
    - hu_moments  # 7 Hu invariant moments
    - central_moments
    - normalized_moments
    
  fractal:
    min_box_size: 2
    max_box_size: 128
    n_scales: 20

# Statistical Features Configuration
statistical:
  first_order:
    - mean
    - std
    - variance
    - median
    - mode
    - min
    - max
    - range
    - iqr
    - skewness
    - kurtosis
    - entropy
    - energy
    - coefficient_of_variation
    
  percentiles: [5, 10, 25, 50, 75, 90, 95, 99]
  
  second_order:
    - gradient_mean
    - gradient_std
    - gradient_max
    - laplacian_mean
    - laplacian_std
    - laplacian_variance
    
  histogram:
    bins: 256
    range: [0, 255]

# Feature Selection Configuration
feature_selection:
  methods:
    univariate:
      k_best: 20
      score_function: 'f_classif'
      
    rfe:
      n_features: 20
      estimator: 'random_forest'
      step: 1
      
    importance:
      threshold: 0.01
      estimator: 'random_forest'
      n_estimators: 100
      
    lasso:
      alpha: 0.01
      max_iter: 1000
      
    mutual_info:
      k_best: 20
      
  preprocessing:
    standardization: true
    normalization: false
    remove_low_variance: true
    variance_threshold: 0.01

# Visualization Configuration
visualization:
  figure_size: [12, 8]
  dpi: 300
  style: 'seaborn-v0_8'
  color_palette: 'Set1'
  
  plots:
    distribution:
      bins: 50
      alpha: 0.7
      
    correlation:
      method: 'pearson'
      annot: true
      fmt: '.2f'
      
    importance:
      top_n: 20
      orientation: 'horizontal'
      
    clustering:
      method: 'ward'
      metric: 'euclidean'
      
    dimensionality_reduction:
      tsne:
        perplexity: 30
        n_iter: 1000
        learning_rate: 200
      pca:
        n_components: 2
        whiten: false

# Output Configuration
output:
  save_features: true
  save_plots: true
  save_statistics: true
  
  file_formats:
    features: 'csv'
    plots: 'png'
    statistics: 'json'
    
  directories:
    features: 'results/features'
    plots: 'results/plots'
    statistics: 'results/statistics'

# Performance Configuration
performance:
  metrics:
    classification:
      - accuracy
      - precision
      - recall
      - f1_score
      - roc_auc
      
    regression:
      - mse
      - rmse
      - mae
      - r2
      
  cross_validation:
    folds: 5
    shuffle: true
    stratify: true
    
  validation:
    test_size: 0.2
    random_state: 42

# Feature Categories for Analysis
feature_categories:
  texture:
    - glcm_contrast_mean
    - glcm_dissimilarity_mean
    - glcm_homogeneity_mean
    - glcm_energy_mean
    - glcm_correlation_mean
    - lbp_uniformity
    - lbp_entropy
    - edge_roberts_mean
    - edge_sobel_mean
    
  shape:
    - area
    - perimeter
    - circularity
    - eccentricity
    - solidity
    - extent
    - compactness
    - aspect_ratio
    - equivalent_diameter
    - fractal_dimension
    
  statistical:
    - mean
    - std
    - variance
    - skewness
    - kurtosis
    - entropy
    - energy
    - median
    - percentile_25
    - percentile_75
    
  intensity:
    - min
    - max
    - range
    - gradient_mean
    - gradient_std
    - laplacian_mean
    - coefficient_of_variation

# Quality Control
quality_control:
  feature_validation:
    check_nan: true
    check_inf: true
    check_variance: true
    min_variance: 1e-6
    
  outlier_detection:
    method: 'iqr'
    threshold: 1.5
    
  feature_stability:
    min_stability: 0.8
    n_bootstrap: 100