# 3D Image Reconstruction Module

## Overview

This module provides comprehensive 3D reconstruction capabilities for brain tumor visualization from segmented 2D MRI slices. It implements advanced techniques for volume interpolation, isotropic voxel creation, 3D visualization, orthogonal views, and surface rendering.

**Author:** Dr<PERSON> <PERSON>smail  
**Institution:** SUST - BME (Biomedical Engineering)

## Features

### 🔄 Volume Interpolation
- **Linear Interpolation**: Fast and smooth interpolation between slices
- **Cubic Interpolation**: Higher-quality interpolation with smooth derivatives
- **Nearest Neighbor**: Preserves original values without smoothing
- **Adaptive Smoothing**: Gaussian smoothing with configurable parameters

### 📐 Isotropic Voxel Creation
- **Automatic Spacing**: Determines optimal isotropic spacing
- **Manual Control**: User-defined target spacing
- **Volume Metrics**: Comprehensive volume and surface measurements
- **Quality Preservation**: Anti-aliasing and range preservation

### 🎯 Orthogonal Views
- **Standard Views**: Axial, Sagittal, Coronal planes
- **Maximum Intensity Projection (MIP)**: Highlights brightest features
- **Mean Intensity Projection**: Shows average intensity patterns
- **Interactive Slicing**: User-controlled slice positioning

### 🎨 Surface Rendering
- **Marching Cubes**: High-quality surface extraction
- **3D Visualization**: Interactive 3D surface rendering
- **Wireframe Display**: Mesh structure visualization
- **Surface Metrics**: Area, volume, and geometric analysis

### 📊 Volume Visualization
- **Slice Grids**: Multiple slice visualization
- **Overlay Display**: Image-mask overlays with transparency
- **3D Scatter Plots**: Point cloud visualization
- **Comprehensive Dashboards**: Multi-view analysis panels

## Installation

### Prerequisites

```bash
pip install numpy scipy matplotlib scikit-image
pip install opencv-python
pip install pyyaml
```

### Module Structure

```
src/
├── reconstruction_3d.py      # Main reconstruction classes
└── feature_extraction.py     # Feature extraction support

examples/
└── reconstruction_3d_demo.py # Comprehensive demonstration

config/
└── reconstruction_config.yaml # Configuration parameters

docs/
└── RECONSTRUCTION_3D_README.md
```

## Usage

### Basic Reconstruction

```python
from reconstruction_3d import Reconstruction3D
import numpy as np

# Initialize reconstruction system
reconstructor = Reconstruction3D()

# Load your 2D slices
slices = [slice1, slice2, slice3, ...]  # List of 2D numpy arrays

# Perform reconstruction
reconstructed_volume = reconstructor.reconstruct_from_slices(
    slices=slices,
    slice_spacing=2.0,                    # mm between slices
    pixel_spacing=(1.0, 1.0),            # mm per pixel
    target_isotropic_spacing=1.0,         # Target isotropic spacing
    interpolation_method='linear',        # Interpolation method
    smoothing_sigma=0.5                   # Smoothing parameter
)

print(f"Reconstructed volume shape: {reconstructed_volume.shape}")
```

### Advanced Reconstruction

```python
# Create isotropic voxels
from reconstruction_3d import IsotropicVoxelCreator

creator = IsotropicVoxelCreator()
isotropic_volume = creator.create_isotropic_volume(
    volume=original_volume,
    original_spacing=(2.0, 1.0, 1.0),
    target_spacing=1.0
)

# Calculate volume metrics
metrics = creator.calculate_volume_metrics(isotropic_volume, (1.0, 1.0, 1.0))
print(f"Volume: {metrics['total_volume_ml']:.2f} ml")
print(f"Surface area: {metrics['surface_area_mm2']:.2f} mm²")
```

### Orthogonal Views

```python
from reconstruction_3d import OrthogonalViews

# Create orthogonal views
ortho_viewer = OrthogonalViews()
views = ortho_viewer.generate_orthogonal_views(volume)

# Visualize
ortho_viewer.visualize_orthogonal_views(volume, title="Tumor Analysis")

# Create MIP views
mip_views = ortho_viewer.create_mip_views(volume)
```

### Surface Rendering

```python
from reconstruction_3d import SurfaceRenderer

# Extract and render surface
renderer = SurfaceRenderer()
vertices, faces, normals, values = renderer.extract_surface(
    volume=volume,
    level=0.5,
    spacing=(1.0, 1.0, 1.0)
)

# Render 3D surface
renderer.render_surface_3d(
    volume=volume,
    level=0.5,
    spacing=(1.0, 1.0, 1.0),
    color='red',
    alpha=0.8,
    title="3D Tumor Surface"
)

# Calculate surface metrics
metrics = renderer.calculate_surface_metrics(volume)
print(f"Surface area: {metrics['surface_area_mm2']:.2f} mm²")
print(f"Number of vertices: {metrics['n_vertices']}")
```

## Detailed Component Guide

### 1. Volume Interpolation

#### VolumeInterpolator Class

```python
from reconstruction_3d import VolumeInterpolator

interpolator = VolumeInterpolator(method='linear')

# Interpolate volume
interpolated_volume = interpolator.interpolate_volume(
    slices=slices,
    slice_spacing=2.0,      # Original spacing
    target_spacing=1.0,     # Target spacing
    axis=0                  # Interpolation axis
)

# Apply smoothing
smoothed_volume = interpolator.apply_smoothing(
    volume=interpolated_volume,
    sigma=1.0
)
```

#### Interpolation Methods

1. **Linear Interpolation**
   - Fast and memory-efficient
   - Smooth transitions between slices
   - Good for most applications

2. **Cubic Interpolation**
   - Higher quality with smooth derivatives
   - Requires at least 4 data points
   - Better for detailed analysis

3. **Nearest Neighbor**
   - Preserves original values
   - No smoothing artifacts
   - Useful for binary masks

### 2. Isotropic Voxel Creation

#### IsotropicVoxelCreator Class

```python
from reconstruction_3d import IsotropicVoxelCreator

creator = IsotropicVoxelCreator()

# Create isotropic voxels
isotropic_volume = creator.create_isotropic_volume(
    volume=volume,
    original_spacing=(2.0, 1.0, 1.0),  # z, y, x spacing
    target_spacing=1.0                  # Isotropic spacing
)

# Resample to specific spacing
resampled = creator.resample_to_spacing(
    volume=volume,
    current_spacing=(2.0, 1.0, 1.0),
    target_spacing=(1.0, 1.0, 1.0)
)
```

#### Volume Metrics

The system calculates comprehensive volume metrics:

```python
metrics = creator.calculate_volume_metrics(volume, spacing)

# Available metrics:
# - total_volume_mm3: Total volume in cubic millimeters
# - total_volume_ml: Total volume in milliliters
# - surface_area_mm2: Approximate surface area
# - bounding_box_min/max/size: Bounding box information
# - voxel_count: Number of non-zero voxels
# - voxel_volume_mm3: Volume of individual voxel
```

### 3. Orthogonal Views

#### OrthogonalViews Class

```python
from reconstruction_3d import OrthogonalViews

ortho_viewer = OrthogonalViews()

# Generate standard orthogonal views
views = ortho_viewer.generate_orthogonal_views(
    volume=volume,
    slice_indices={
        'axial': 50,      # z-axis slice
        'sagittal': 64,   # x-axis slice
        'coronal': 64     # y-axis slice
    }
)

# Access individual views
axial_view = views['axial']
sagittal_view = views['sagittal']
coronal_view = views['coronal']
```

#### Projection Methods

1. **Maximum Intensity Projection (MIP)**
   ```python
   mip_views = ortho_viewer.create_mip_views(volume)
   ```

2. **Mean Intensity Projection**
   ```python
   mean_views = ortho_viewer.create_mean_views(volume)
   ```

### 4. Surface Rendering

#### SurfaceRenderer Class

```python
from reconstruction_3d import SurfaceRenderer

renderer = SurfaceRenderer()

# Extract surface using marching cubes
vertices, faces, normals, values = renderer.extract_surface(
    volume=volume,
    level=0.5,                    # Isosurface level
    spacing=(1.0, 1.0, 1.0)      # Voxel spacing
)

# Render 3D surface
renderer.render_surface_3d(
    volume=volume,
    level=0.5,
    spacing=(1.0, 1.0, 1.0),
    alpha=0.8,                   # Transparency
    color='red',                 # Surface color
    title="3D Tumor Surface"
)
```

#### Surface Analysis

```python
# Calculate comprehensive surface metrics
metrics = renderer.calculate_surface_metrics(volume, level=0.5, spacing=(1.0, 1.0, 1.0))

# Available metrics:
# - surface_area_mm2: Surface area in square millimeters
# - convex_volume_mm3: Convex hull volume
# - n_vertices: Number of surface vertices
# - n_faces: Number of surface faces
# - bounding_box_min/max/size: 3D bounding box
# - centroid: Surface centroid coordinates
```

### 5. Volume Visualization

#### VolumeVisualizer Class

```python
from reconstruction_3d import VolumeVisualizer

visualizer = VolumeVisualizer()

# Create volume slice grid
visualizer.create_volume_slices(
    volume=volume,
    n_slices=9,                  # Number of slices
    axis=0,                      # Slicing axis
    title="Volume Slices"
)

# Create overlay visualization
visualizer.create_volume_overlay(
    image_volume=image_volume,
    mask_volume=mask_volume,
    slice_index=50,
    alpha=0.5,                   # Overlay transparency
    title="Tumor Overlay"
)

# Create 3D scatter plot
visualizer.create_3d_scatter(
    volume=volume,
    threshold=0.5,               # Intensity threshold
    sample_ratio=0.1,            # Sampling ratio
    title="3D Scatter Plot"
)
```

## Complete Reconstruction Pipeline

### Reconstruction3D Class

The main class that combines all reconstruction capabilities:

```python
from reconstruction_3d import Reconstruction3D

# Initialize reconstruction system
reconstructor = Reconstruction3D()

# Perform complete reconstruction
reconstructed_volume = reconstructor.reconstruct_from_slices(
    slices=slices,
    slice_spacing=2.0,
    pixel_spacing=(1.0, 1.0),
    target_isotropic_spacing=1.0,
    interpolation_method='linear',
    smoothing_sigma=0.5
)

# Analyze reconstruction
analysis = reconstructor.analyze_reconstruction()

# Create comprehensive visualization
reconstructor.create_comprehensive_visualization(
    title="Brain Tumor Reconstruction"
)

# Export results
reconstructor.export_reconstruction_data(
    export_path="tumor_reconstruction",
    analysis=analysis
)
```

### Analysis Output

The reconstruction analysis provides comprehensive information:

```python
{
    'reconstruction_info': {
        'original_slices': 20,
        'original_slice_spacing': 2.0,
        'original_pixel_spacing': (1.0, 1.0),
        'target_isotropic_spacing': 1.0,
        'final_spacing': (1.0, 1.0, 1.0),
        'interpolation_method': 'linear',
        'smoothing_sigma': 0.5,
        'reconstruction_time': 2.34,
        'final_volume_shape': (40, 128, 128)
    },
    'volume_metrics': {
        'total_volume_ml': 12.5,
        'surface_area_mm2': 156.3,
        'voxel_count': 12500,
        'bounding_box_size': [20.0, 25.0, 30.0]
    },
    'surface_metrics': {
        'surface_area_mm2': 156.3,
        'n_vertices': 2340,
        'n_faces': 4680,
        'centroid': [64.0, 64.0, 20.0]
    },
    'volume_statistics': {
        'min_value': 0.0,
        'max_value': 255.0,
        'mean_value': 87.5,
        'std_value': 45.2,
        'non_zero_voxels': 12500
    }
}
```

## Configuration

The module uses a YAML configuration file for parameter customization:

```yaml
# config/reconstruction_config.yaml
interpolation:
  method: 'linear'
  target_spacing: 1.0
  smoothing:
    enabled: true
    sigma: 0.5

isotropic:
  auto_spacing: true
  preserve_range: true
  anti_aliasing: true

surface_rendering:
  level: 0.5
  surface_color: 'red'
  surface_alpha: 0.8
  high_quality: true

performance:
  use_multiprocessing: true
  n_jobs: -1
  show_progress: true
```

## Performance Optimization

### Memory Management

```python
# For large volumes, use chunked processing
reconstructor = Reconstruction3D()

# Process in chunks to manage memory
chunk_size = 1000000  # Adjust based on available memory
```

### Parallel Processing

```python
# Enable multiprocessing for faster reconstruction
import multiprocessing
n_cores = multiprocessing.cpu_count()

# Use parallel processing in reconstruction
reconstructor.volume_interpolator.n_jobs = n_cores
```

### GPU Acceleration

```python
# Enable GPU acceleration (if available)
try:
    import cupy as cp
    use_gpu = True
except ImportError:
    use_gpu = False

if use_gpu:
    # Use GPU-accelerated operations
    pass
```

## Quality Control

### Input Validation

```python
# Validate input slices
def validate_slices(slices):
    if len(slices) < 2:
        raise ValueError("Need at least 2 slices")
    
    # Check slice consistency
    shape = slices[0].shape
    for i, slice_img in enumerate(slices):
        if slice_img.shape != shape:
            raise ValueError(f"Slice {i} has inconsistent shape")
    
    return True
```

### Reconstruction Quality Assessment

```python
# Assess reconstruction quality
def assess_quality(original_slices, reconstructed_volume):
    # Calculate interpolation error
    interpolation_error = calculate_interpolation_error(
        original_slices, reconstructed_volume)
    
    # Measure surface smoothness
    surface_smoothness = calculate_surface_smoothness(
        reconstructed_volume)
    
    return {
        'interpolation_error': interpolation_error,
        'surface_smoothness': surface_smoothness
    }
```

## Advanced Features

### Multi-Scale Reconstruction

```python
# Reconstruct at multiple scales
scales = [0.5, 1.0, 2.0]
reconstructions = {}

for scale in scales:
    target_spacing = 1.0 / scale
    reconstructions[scale] = reconstructor.reconstruct_from_slices(
        slices=slices,
        target_isotropic_spacing=target_spacing
    )
```

### Texture Analysis Integration

```python
# Extract texture features from reconstructed volume
from feature_extraction import FeatureExtractor

extractor = FeatureExtractor()
features = extractor.extract_all_features(
    image=reconstructed_volume[20],  # Middle slice
    mask=mask_volume[20]
)
```

### Medical Imaging Integration

```python
# Handle DICOM data
import pydicom

def load_dicom_series(dicom_path):
    """Load DICOM series and extract relevant information."""
    dicom_files = sorted(glob.glob(os.path.join(dicom_path, "*.dcm")))
    
    slices = []
    slice_positions = []
    
    for dicom_file in dicom_files:
        ds = pydicom.dcmread(dicom_file)
        slices.append(ds.pixel_array)
        slice_positions.append(ds.SliceLocation)
    
    # Sort by slice position
    sorted_indices = np.argsort(slice_positions)
    sorted_slices = [slices[i] for i in sorted_indices]
    
    # Extract spacing information
    pixel_spacing = ds.PixelSpacing
    slice_spacing = abs(slice_positions[1] - slice_positions[0])
    
    return sorted_slices, slice_spacing, pixel_spacing
```

## Best Practices

### 1. Data Preprocessing

```python
# Normalize slice intensities
def normalize_slices(slices):
    normalized = []
    for slice_img in slices:
        # Normalize to [0, 255] range
        normalized_slice = (slice_img - slice_img.min()) / (slice_img.max() - slice_img.min()) * 255
        normalized.append(normalized_slice)
    return normalized

# Apply preprocessing
slices = normalize_slices(slices)
```

### 2. Parameter Selection

```python
# Choose appropriate interpolation method
def choose_interpolation_method(slice_spacing, target_spacing):
    ratio = slice_spacing / target_spacing
    
    if ratio > 4:
        return 'cubic'  # High upsampling ratio
    elif ratio > 2:
        return 'linear'  # Moderate upsampling
    else:
        return 'nearest'  # Low upsampling
```

### 3. Quality Assessment

```python
# Assess reconstruction quality
def assess_reconstruction_quality(original_slices, reconstructed_volume):
    # Calculate metrics
    metrics = {
        'volume_consistency': calculate_volume_consistency(original_slices, reconstructed_volume),
        'surface_smoothness': calculate_surface_smoothness(reconstructed_volume),
        'interpolation_accuracy': calculate_interpolation_accuracy(original_slices, reconstructed_volume)
    }
    
    return metrics
```

## Troubleshooting

### Common Issues

**1. Memory Errors**
```python
# Solution: Reduce volume size or use chunked processing
def reduce_volume_size(volume, scale_factor=0.5):
    from skimage.transform import resize
    new_shape = tuple(int(dim * scale_factor) for dim in volume.shape)
    return resize(volume, new_shape, preserve_range=True)
```

**2. Interpolation Artifacts**
```python
# Solution: Adjust smoothing parameters
smoothing_sigma = 1.0  # Increase for more smoothing
```

**3. Surface Rendering Issues**
```python
# Solution: Adjust isosurface level
level = 0.3  # Try different levels between 0 and 1
```

**4. Slow Performance**
```python
# Solution: Enable multiprocessing
n_jobs = -1  # Use all available cores
```

## Examples

### Complete Workflow Example

```python
import numpy as np
from reconstruction_3d import Reconstruction3D
import matplotlib.pyplot as plt

# Load data
slices = load_mri_slices("path/to/slices")
slice_spacing = 2.0
pixel_spacing = (1.0, 1.0)

# Initialize reconstruction
reconstructor = Reconstruction3D()

# Perform reconstruction
reconstructed_volume = reconstructor.reconstruct_from_slices(
    slices=slices,
    slice_spacing=slice_spacing,
    pixel_spacing=pixel_spacing,
    target_isotropic_spacing=1.0,
    interpolation_method='linear',
    smoothing_sigma=0.5
)

# Analyze results
analysis = reconstructor.analyze_reconstruction()

# Print results
print(f"Original slices: {len(slices)}")
print(f"Reconstructed volume shape: {reconstructed_volume.shape}")
print(f"Total volume: {analysis['volume_metrics']['total_volume_ml']:.2f} ml")
print(f"Surface area: {analysis['surface_metrics']['surface_area_mm2']:.2f} mm²")

# Create visualizations
reconstructor.create_comprehensive_visualization(
    title="Brain Tumor 3D Reconstruction"
)

# Export results
reconstructor.export_reconstruction_data(
    export_path="tumor_reconstruction_results",
    analysis=analysis
)
```

### Batch Processing Example

```python
# Process multiple patients
patients = ["patient_001", "patient_002", "patient_003"]
results = {}

for patient_id in patients:
    print(f"Processing {patient_id}...")
    
    # Load patient data
    slices = load_patient_slices(patient_id)
    
    # Reconstruct
    reconstructor = Reconstruction3D()
    volume = reconstructor.reconstruct_from_slices(
        slices=slices,
        slice_spacing=2.0,
        pixel_spacing=(1.0, 1.0),
        target_isotropic_spacing=1.0
    )
    
    # Analyze
    analysis = reconstructor.analyze_reconstruction()
    results[patient_id] = analysis
    
    # Export
    reconstructor.export_reconstruction_data(
        export_path=f"results/{patient_id}",
        analysis=analysis
    )

# Compare results
compare_patient_results(results)
```

## References

1. Lorensen, W.E., Cline, H.E. (1987). Marching cubes: A high resolution 3D surface construction algorithm. Computer Graphics, 21(4), 163-169.

2. Lewiner, T., et al. (2003). Efficient implementation of marching cubes' cases with topological guarantees. Journal of Graphics Tools, 8(2), 1-15.

3. Robb, R.A. (2000). Biomedical imaging, visualization, and analysis. Wiley-Liss.

4. Udupa, J.K., Herman, G.T. (2000). 3D imaging in medicine. CRC Press.

## License

This module is part of the Brain Tumor Detection system developed by Dr. Mohammed Yagoub Esmail. All rights reserved.

## Contact

For questions or support, please contact:
- **Email**: <EMAIL>
- **Phone**: +************ | +************

---

*This documentation provides comprehensive guidance for using the 3D reconstruction module. For additional examples and use cases, refer to the demonstration scripts in the examples directory.*