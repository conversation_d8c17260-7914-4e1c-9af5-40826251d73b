/**
 * Brain Tumor Detection in 3D MRI Images - Interactive Web Application
 *
 * Author: Dr. <PERSON>il
 * Affiliation: SUST - BME (Biomedical Engineering)
 * Email: <EMAIL>
 * Phone: +249912867327 | +966538076790
 *
 * Copyright © 2025 Dr. <PERSON>ub Esmail. All rights reserved.
 *
 * This file contains the interactive functionality for the brain tumor detection
 * web application, including tab navigation, animations, and user interactions.
 */

class BrainTumorApp {
    constructor() {
        this.currentTab = 'overview';
        this.isLoading = true;
        this.animationQueue = [];
        
        this.init();
    }

    init() {
        // Wait for DOM to be fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupApp());
        } else {
            this.setupApp();
        }
    }

    setupApp() {
        this.setupLoadingAnimation();
        this.setupTabNavigation();
        this.setupCodeTabs();
        this.setupProcessCards();
        this.setupScrollAnimations();
        this.setupHeaderEffects();
        this.setupEnhancedInteractions();
        
        // Hide loading overlay after setup
        setTimeout(() => this.hideLoading(), 1500);
    }

    setupLoadingAnimation() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            // Add some dynamic loading messages
            const messages = [
                'Initializing DICOM processors...',
                'Loading image segmentation algorithms...',
                'Preparing 3D reconstruction engine...',
                'Calibrating neural networks...',
                'Ready for brain tumor analysis!'
            ];
            
            const loadingText = loadingOverlay.querySelector('p');
            let messageIndex = 0;
            
            const messageInterval = setInterval(() => {
                if (messageIndex < messages.length - 1) {
                    loadingText.textContent = messages[messageIndex];
                    messageIndex++;
                } else {
                    clearInterval(messageInterval);
                    loadingText.textContent = messages[messages.length - 1];
                }
            }, 300);
        }
    }

    hideLoading() {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.classList.add('hidden');
            setTimeout(() => {
                loadingOverlay.style.display = 'none';
            }, 500);
        }
        this.isLoading = false;
        this.triggerEntranceAnimations();
    }

    setupTabNavigation() {
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabPanes = document.querySelectorAll('.tab-pane');

        tabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const targetTab = e.target.getAttribute('data-tab');
                this.switchTab(targetTab, tabButtons, tabPanes);
            });
        });
    }

    switchTab(targetTab, tabButtons, tabPanes) {
        if (targetTab === this.currentTab) return;

        // Remove active class from all buttons and panes
        tabButtons.forEach(btn => btn.classList.remove('active'));
        tabPanes.forEach(pane => {
            pane.classList.remove('active');
            pane.style.opacity = '0';
        });

        // Add active class to target button
        const targetButton = document.querySelector(`[data-tab="${targetTab}"]`);
        if (targetButton) {
            targetButton.classList.add('active');
        }

        // Show target pane with animation
        setTimeout(() => {
            const targetPane = document.getElementById(targetTab);
            if (targetPane) {
                targetPane.classList.add('active');
                targetPane.style.opacity = '1';
                this.animateTabContent(targetPane);
            }
        }, 150);

        this.currentTab = targetTab;
        
        // Add ripple effect to button
        this.addRippleEffect(targetButton);
    }

    setupCodeTabs() {
        // Handle traditional code tabs
        const codeTabButtons = document.querySelectorAll('.code-tab-btn');

        codeTabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const targetCodeTab = e.target.getAttribute('data-code-tab');
                const parentContainer = e.target.closest('.code-tabs');

                if (parentContainer) {
                    this.switchCodeTab(targetCodeTab, parentContainer);
                }
            });
        });

        // Handle implementation tabs for DICOM model
        const implTabButtons = document.querySelectorAll('.impl-tab-btn');

        implTabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const targetImplTab = e.target.getAttribute('data-impl-tab');
                const parentContainer = e.target.closest('.implementation-tabs');

                if (parentContainer) {
                    this.switchImplementationTab(targetImplTab, parentContainer);
                }
            });
        });
    }

    switchCodeTab(targetCodeTab, container) {
        const buttons = container.querySelectorAll('.code-tab-btn');
        const codeBlocks = container.querySelectorAll('.code-block');

        // Remove active class from all buttons and blocks
        buttons.forEach(btn => btn.classList.remove('active'));
        codeBlocks.forEach(block => block.classList.remove('active'));

        // Add active class to target button
        const targetButton = container.querySelector(`[data-code-tab="${targetCodeTab}"]`);
        if (targetButton) {
            targetButton.classList.add('active');
        }

        // Show target code block
        const targetBlock = container.querySelector(`#${targetCodeTab}`);
        if (targetBlock) {
            targetBlock.classList.add('active');
            this.highlightCodeBlock(targetBlock);
        }
    }

    switchImplementationTab(targetImplTab, container) {
        const buttons = container.querySelectorAll('.impl-tab-btn');
        const implContents = container.querySelectorAll('.impl-content');

        // Remove active class from all buttons and content
        buttons.forEach(btn => btn.classList.remove('active'));
        implContents.forEach(content => {
            content.classList.remove('active');
            content.style.opacity = '0';
        });

        // Add active class to target button
        const targetButton = container.querySelector(`[data-impl-tab="${targetImplTab}"]`);
        if (targetButton) {
            targetButton.classList.add('active');
            this.addRippleEffect(targetButton);
        }

        // Show target implementation content with animation
        setTimeout(() => {
            const targetContent = container.querySelector(`#${targetImplTab}`);
            if (targetContent) {
                targetContent.classList.add('active');
                targetContent.style.opacity = '1';
                this.animateImplementationContent(targetContent);
            }
        }, 150);
    }

    setupProcessCards() {
        const processCards = document.querySelectorAll('.process-card');
        
        processCards.forEach(card => {
            card.addEventListener('click', (e) => {
                const targetStep = e.currentTarget.getAttribute('data-step');
                if (targetStep) {
                    this.navigateToStep(targetStep);
                }
            });

            // Add hover effects
            card.addEventListener('mouseenter', (e) => {
                this.addCardHoverEffect(e.currentTarget);
            });

            card.addEventListener('mouseleave', (e) => {
                this.removeCardHoverEffect(e.currentTarget);
            });
        });
    }

    navigateToStep(step) {
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabPanes = document.querySelectorAll('.tab-pane');
        
        this.switchTab(step, tabButtons, tabPanes);
        
        // Smooth scroll to main content
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            mainContent.scrollIntoView({ behavior: 'smooth' });
        }
    }

    setupScrollAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // Observe elements for scroll animations
        const animateElements = document.querySelectorAll('.process-card, .detail-card, .hero-content');
        animateElements.forEach(el => observer.observe(el));
    }

    setupHeaderEffects() {
        const header = document.querySelector('.header');
        let lastScrollY = window.scrollY;

        window.addEventListener('scroll', () => {
            const currentScrollY = window.scrollY;
            
            if (currentScrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.1)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.8)';
                header.style.boxShadow = 'none';
            }

            lastScrollY = currentScrollY;
        });
    }

    setupEnhancedInteractions() {
        // Add click effects to buttons
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                this.addRippleEffect(e.currentTarget);
            });
        });

        // Add keyboard navigation
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardNavigation(e);
        });

        // Add smooth scrolling to anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });
    }

    addRippleEffect(element) {
        const ripple = document.createElement('span');
        ripple.classList.add('ripple');
        ripple.style.position = 'absolute';
        ripple.style.borderRadius = '50%';
        ripple.style.background = 'rgba(255, 255, 255, 0.6)';
        ripple.style.transform = 'scale(0)';
        ripple.style.animation = 'ripple 0.6s linear';
        ripple.style.pointerEvents = 'none';

        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = (rect.width / 2 - size / 2) + 'px';
        ripple.style.top = (rect.height / 2 - size / 2) + 'px';

        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    addCardHoverEffect(card) {
        const icon = card.querySelector('.icon-wrapper');
        if (icon) {
            icon.style.transform = 'scale(1.1) rotate(5deg)';
        }
    }

    removeCardHoverEffect(card) {
        const icon = card.querySelector('.icon-wrapper');
        if (icon) {
            icon.style.transform = 'scale(1) rotate(0deg)';
        }
    }

    highlightCodeBlock(codeBlock) {
        // Add syntax highlighting effect
        const code = codeBlock.querySelector('code');
        if (code) {
            code.style.animation = 'none';
            setTimeout(() => {
                code.style.animation = 'codeHighlight 0.5s ease-in-out';
            }, 10);
        }
    }

    animateTabContent(tabPane) {
        const elements = tabPane.querySelectorAll('.detail-card, .process-card');
        elements.forEach((el, index) => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                el.style.transition = 'all 0.5s ease';
                el.style.opacity = '1';
                el.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    triggerEntranceAnimations() {
        const heroContent = document.querySelector('.hero-content');
        if (heroContent) {
            heroContent.style.animation = 'fadeInUp 1s ease-out';
        }

        const processCards = document.querySelectorAll('.process-card');
        processCards.forEach((card, index) => {
            setTimeout(() => {
                card.style.animation = `fadeInUp 0.6s ease-out ${index * 0.1}s both`;
            }, 500);
        });
    }

    animateImplementationContent(content) {
        // Animate different types of content within implementation tabs
        const codeBlocks = content.querySelectorAll('.code-block');
        const modelComponents = content.querySelectorAll('.architecture-component, .model-layer, .metric');

        // Animate code blocks
        codeBlocks.forEach((block, index) => {
            block.style.opacity = '0';
            block.style.transform = 'translateY(20px)';

            setTimeout(() => {
                block.style.transition = 'all 0.5s ease';
                block.style.opacity = '1';
                block.style.transform = 'translateY(0)';
            }, index * 100);
        });

        // Animate model components
        modelComponents.forEach((component, index) => {
            component.style.opacity = '0';
            component.style.transform = 'scale(0.95)';

            setTimeout(() => {
                component.style.transition = 'all 0.4s ease';
                component.style.opacity = '1';
                component.style.transform = 'scale(1)';
            }, index * 50);
        });

        // Animate pipeline steps if present
        const pipelineSteps = content.querySelectorAll('.pipeline-step');
        pipelineSteps.forEach((step, index) => {
            step.style.opacity = '0';
            step.style.transform = 'translateX(-20px)';

            setTimeout(() => {
                step.style.transition = 'all 0.6s ease';
                step.style.opacity = '1';
                step.style.transform = 'translateX(0)';
            }, index * 150);
        });
    }

    handleKeyboardNavigation(e) {
        const tabButtons = document.querySelectorAll('.tab-btn');
        const currentIndex = Array.from(tabButtons).findIndex(btn => btn.classList.contains('active'));

        if (e.key === 'ArrowLeft' && currentIndex > 0) {
            tabButtons[currentIndex - 1].click();
        } else if (e.key === 'ArrowRight' && currentIndex < tabButtons.length - 1) {
            tabButtons[currentIndex + 1].click();
        }
    }
}

// Add CSS animations dynamically
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    @keyframes codeHighlight {
        0% { text-shadow: 0 0 10px rgba(16, 185, 129, 0.3); }
        50% { text-shadow: 0 0 20px rgba(16, 185, 129, 0.6); }
        100% { text-shadow: 0 0 10px rgba(16, 185, 129, 0.3); }
    }
    
    .animate-in {
        animation: fadeInUp 0.8s ease-out;
    }
`;
document.head.appendChild(style);

// Initialize the application
const app = new BrainTumorApp();
