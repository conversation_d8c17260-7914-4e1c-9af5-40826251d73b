# Feature Extraction and Selection Module

## Overview

This module provides comprehensive feature extraction and selection capabilities for brain tumor characterization from segmented MRI images. It implements state-of-the-art techniques for extracting texture, shape, and statistical features, along with robust feature selection methods.

**Author:** Dr. <PERSON>il  
**Institution:** SUST - BME (Biomedical Engineering)

## Features

### 🔬 Feature Types Supported

1. **Texture Features**
   - Gray-Level Co-occurrence Matrix (GLCM)
   - Local Binary Patterns (LBP)
   - Edge-based features
   - Gabor filters

2. **Shape Features**
   - Basic geometric properties
   - Moment invariants (Hu moments)
   - Fractal dimension
   - Morphological descriptors

3. **Statistical Features**
   - First-order statistics
   - Second-order statistics
   - Histogram-based features
   - Intensity distribution measures

4. **Feature Selection Methods**
   - Univariate statistical tests
   - Recursive Feature Elimination (RFE)
   - Feature importance ranking
   - Mutual information
   - LASSO regularization

### 📊 Visualization Capabilities

- Feature distribution plots
- Correlation matrices
- Feature importance charts
- Dimensionality reduction (t-SNE, PCA)
- Feature clustering dendrograms
- Comprehensive dashboards

## Installation

### Prerequisites

```bash
pip install numpy scipy scikit-learn scikit-image
pip install matplotlib seaborn pandas
pip install opencv-python
pip install pyyaml
```

### Module Structure

```
src/
├── feature_extraction.py      # Main feature extraction classes
├── feature_visualization.py   # Visualization utilities
└── feature_config.yaml       # Configuration file

examples/
└── feature_extraction_demo.py # Demonstration script

docs/
└── FEATURE_EXTRACTION_README.md
```

## Usage

### Basic Usage

```python
from feature_extraction import FeatureExtractor
import numpy as np

# Initialize extractor
extractor = FeatureExtractor()

# Extract features from image and mask
image = np.random.rand(100, 100) * 255  # Your MRI image
mask = np.random.randint(0, 2, (100, 100))  # Your segmentation mask

features = extractor.extract_all_features(image, mask)
print(f"Extracted {len(features)} features")
```

### Batch Processing

```python
# Process multiple images
images = [image1, image2, image3]  # List of images
masks = [mask1, mask2, mask3]      # Corresponding masks

features_list = extractor.extract_features_batch(images, masks)
```

### Feature Selection

```python
from feature_extraction import FeatureSelector
import numpy as np

# Prepare data
X = np.array([[features.get(name, 0) for name in feature_names] 
              for features in features_list])
y = np.array(labels)  # Your class labels

# Initialize selector
selector = FeatureSelector()

# Univariate selection
X_selected, selected_indices = selector.select_features_univariate(X, y, k=20)

# RFE selection
X_rfe, rfe_indices = selector.select_features_rfe(X, y, n_features=20)

# Importance-based selection
X_imp, imp_indices = selector.select_features_importance(X, y, threshold=0.01)
```

### Visualization

```python
from feature_visualization import FeatureVisualizer

# Initialize visualizer
visualizer = FeatureVisualizer()

# Plot feature distribution
visualizer.plot_feature_distribution(features_dict, 'area', labels)

# Create correlation matrix
visualizer.plot_correlation_matrix(features_dict)

# Feature importance plot
visualizer.plot_feature_importance(feature_names, importance_scores)

# Comprehensive dashboard
visualizer.create_feature_summary_dashboard(features_dict, labels)
```

## Feature Types Details

### 1. Texture Features

#### Gray-Level Co-occurrence Matrix (GLCM)
Captures spatial relationships between pixel intensities.

**Features extracted:**
- Contrast: Measures local intensity variations
- Dissimilarity: Measures how different neighboring pixels are
- Homogeneity: Measures similarity of pixel pairs
- Energy: Measures uniformity of intensity distribution
- Correlation: Measures linear dependencies between pixels
- ASM (Angular Second Moment): Measures texture uniformity

#### Local Binary Patterns (LBP)
Describes local texture patterns around each pixel.

**Features extracted:**
- Uniformity: Measures pattern uniformity
- Entropy: Measures pattern complexity
- Contrast: Measures local intensity variations
- Rotation invariance: Measures rotational stability

#### Edge-based Features
Characterizes edge information using different operators.

**Operators used:**
- Roberts: Emphasizes high-frequency details
- Sobel: Emphasizes edges in horizontal/vertical directions
- Scharr: Improved version of Sobel
- Prewitt: Similar to Sobel with different kernel

### 2. Shape Features

#### Basic Geometric Properties
- **Area**: Number of pixels in the region
- **Perimeter**: Length of the region boundary
- **Circularity**: Measures how circular the shape is
- **Compactness**: Ratio of perimeter² to area
- **Extent**: Ratio of region area to bounding box area
- **Solidity**: Ratio of region area to convex hull area
- **Aspect Ratio**: Ratio of width to height
- **Equivalent Diameter**: Diameter of circle with same area

#### Advanced Shape Descriptors
- **Eccentricity**: Measures elongation of the shape
- **Major/Minor Axis Length**: Lengths of major and minor axes
- **Orientation**: Angle of major axis with horizontal
- **Elongation**: Ratio of major to minor axis length
- **Roundness**: Alternative circularity measure
- **Fractal Dimension**: Measures shape complexity

#### Moment Invariants
- **Hu Moments**: 7 rotation, translation, and scale invariant moments
- **Central Moments**: Moments computed around the centroid
- **Normalized Moments**: Scale-normalized central moments

### 3. Statistical Features

#### First-order Statistics
Computed from intensity histogram:
- **Mean**: Average intensity
- **Standard Deviation**: Intensity variation
- **Variance**: Square of standard deviation
- **Skewness**: Asymmetry of intensity distribution
- **Kurtosis**: Peakedness of intensity distribution
- **Entropy**: Randomness of intensity distribution
- **Energy**: Uniformity of intensity distribution

#### Second-order Statistics
Computed from spatial relationships:
- **Gradient Statistics**: Measures of intensity changes
- **Laplacian Statistics**: Measures of intensity curvature

#### Percentile-based Features
- Various percentiles (5th, 10th, 25th, 50th, 75th, 90th, 95th, 99th)
- Interquartile Range (IQR)
- Range (max - min)

## Feature Selection Methods

### 1. Univariate Statistical Tests
Selects features based on statistical significance.

```python
# Uses F-test for classification
X_selected, indices = selector.select_features_univariate(X, y, k=20)
```

### 2. Recursive Feature Elimination (RFE)
Recursively eliminates features using model-based ranking.

```python
# Uses Random Forest as base estimator
X_selected, indices = selector.select_features_rfe(X, y, n_features=20)
```

### 3. Feature Importance Ranking
Selects features based on importance scores from tree-based models.

```python
# Uses Random Forest feature importance
X_selected, indices = selector.select_features_importance(X, y, threshold=0.01)
```

### 4. Mutual Information
Selects features based on mutual information with target variable.

```python
# Measures non-linear dependencies
X_selected, indices = selector.select_features_mutual_info(X, y, k=20)
```

## Configuration

The module uses a YAML configuration file for customization:

```yaml
# config/feature_config.yaml
texture:
  glcm:
    distances: [1, 2, 3, 4, 5]
    angles: [0, 45, 90, 135]
    levels: 256
    
shape:
  basic_features:
    - area
    - perimeter
    - circularity
    
statistical:
  first_order:
    - mean
    - std
    - variance
```

## Performance Optimization

### Parallel Processing
```python
# Use multiple cores for feature extraction
extractor = FeatureExtractor(n_jobs=-1)
```

### Memory Management
```python
# Process images in batches
batch_size = 100
for i in range(0, len(images), batch_size):
    batch_images = images[i:i+batch_size]
    batch_masks = masks[i:i+batch_size]
    features_batch = extractor.extract_features_batch(batch_images, batch_masks)
```

## Quality Control

### Feature Validation
- **NaN/Inf Detection**: Identifies and handles invalid values
- **Variance Checking**: Removes features with very low variance
- **Stability Testing**: Ensures features are stable across different samples

### Outlier Detection
- **IQR Method**: Identifies outliers using interquartile range
- **Z-score Method**: Identifies outliers using standard deviation
- **Isolation Forest**: Model-based outlier detection

## Examples

### Complete Workflow Example

```python
import numpy as np
from feature_extraction import FeatureExtractor, FeatureSelector
from feature_visualization import FeatureVisualizer

# 1. Data preparation
images = load_mri_images()  # Your image loading function
masks = load_segmentation_masks()  # Your mask loading function
labels = load_labels()  # Your label loading function

# 2. Feature extraction
extractor = FeatureExtractor()
features_list = extractor.extract_features_batch(images, masks)

# 3. Convert to matrix format
feature_names = list(features_list[0].keys())
X = np.array([[features.get(name, 0) for name in feature_names] 
              for features in features_list])
X = np.nan_to_num(X)  # Handle NaN values

# 4. Feature selection
selector = FeatureSelector()
X_selected, selected_indices = selector.select_features_univariate(X, labels, k=20)

# 5. Visualization
visualizer = FeatureVisualizer()
features_dict = {name: [features.get(name, 0) for features in features_list] 
                for name in feature_names}
visualizer.create_feature_summary_dashboard(features_dict, labels)

# 6. Classification
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report

X_train, X_test, y_train, y_test = train_test_split(X_selected, labels, test_size=0.2)
clf = RandomForestClassifier(n_estimators=100, random_state=42)
clf.fit(X_train, y_train)

y_pred = clf.predict(X_test)
print(classification_report(y_test, y_pred))
```

### Advanced Feature Analysis

```python
# Analyze feature importance
feature_ranking = selector.get_feature_ranking(feature_names)
print("Top 10 most important features:")
for name, score in feature_ranking[:10]:
    print(f"{name}: {score:.4f}")

# Compare feature selection methods
methods = {
    'Univariate': selector.select_features_univariate(X, labels, k=20)[1],
    'RFE': selector.select_features_rfe(X, labels, n_features=20)[1],
    'Importance': selector.select_features_importance(X, labels, threshold=0.01)[1]
}

visualizer.plot_feature_selection_comparison(X, methods, feature_names)
```

## Best Practices

### 1. Data Preprocessing
- Normalize image intensities to [0, 255] range
- Ensure masks are binary (0s and 1s)
- Handle missing or corrupt data

### 2. Feature Engineering
- Remove highly correlated features (correlation > 0.95)
- Scale features appropriately before selection
- Consider domain knowledge for feature interpretation

### 3. Validation
- Use cross-validation for robust performance estimates
- Test feature stability across different datasets
- Validate biological relevance of selected features

### 4. Performance Monitoring
- Track feature extraction time
- Monitor memory usage for large datasets
- Profile code for optimization opportunities

## Troubleshooting

### Common Issues

**1. Memory Error**
```python
# Solution: Process in smaller batches
batch_size = 50  # Reduce batch size
```

**2. NaN Values in Features**
```python
# Solution: Handle NaN values explicitly
X = np.nan_to_num(X, nan=0.0, posinf=0.0, neginf=0.0)
```

**3. Empty Segmentation Masks**
```python
# Solution: Check mask validity
if np.sum(mask) == 0:
    print("Warning: Empty mask detected")
    continue
```

**4. Feature Selection Fails**
```python
# Solution: Check data quality
if np.var(X, axis=0).min() < 1e-6:
    print("Warning: Low variance features detected")
    # Remove low variance features
```

## References

1. Haralick, R.M., et al. (1973). Textural features for image classification. IEEE Transactions on Systems, Man, and Cybernetics.

2. Ojala, T., et al. (2002). Multiresolution gray-scale and rotation invariant texture classification with local binary patterns. IEEE Transactions on Pattern Analysis and Machine Intelligence.

3. Hu, M.K. (1962). Visual pattern recognition by moment invariants. IRE Transactions on Information Theory.

4. Guyon, I., & Elisseeff, A. (2003). An introduction to variable and feature selection. Journal of Machine Learning Research.

## License

This module is part of the Brain Tumor Detection system developed by Dr. Mohammed Yagoub Esmail. All rights reserved.

## Contact

For questions or support, please contact:
- **Email**: <EMAIL>
- **Phone**: +************ | +************

---

*This documentation provides comprehensive guidance for using the feature extraction module. For additional examples and use cases, refer to the demonstration scripts in the examples directory.*# Feature Extraction and Selection Module

## Overview

This module provides comprehensive feature extraction and selection capabilities for brain tumor characterization from segmented MRI images. It implements state-of-the-art techniques for extracting texture, shape, and statistical features, along with robust feature selection methods.

**Author:** Dr. Mohammed Yagoub Esmail  
**Institution:** SUST - BME (Biomedical Engineering)

## Features

### 🔬 Feature Types Supported

1. **Texture Features**
   - Gray-Level Co-occurrence Matrix (GLCM)
   - Local Binary Patterns (LBP)
   - Edge-based features
   - Gabor filters

2. **Shape Features**
   - Basic geometric properties
   - Moment invariants (Hu moments)
   - Fractal dimension
   - Morphological descriptors

3. **Statistical Features**
   - First-order statistics
   - Second-order statistics
   - Histogram-based features
   - Intensity distribution measures

4. **Feature Selection Methods**
   - Univariate statistical tests
   - Recursive Feature Elimination (RFE)
   - Feature importance ranking
   - Mutual information
   - LASSO regularization

### 📊 Visualization Capabilities

- Feature distribution plots
- Correlation matrices
- Feature importance charts
- Dimensionality reduction (t-SNE, PCA)
- Feature clustering dendrograms
- Comprehensive dashboards

## Installation

### Prerequisites

```bash
pip install numpy scipy scikit-learn scikit-image
pip install matplotlib seaborn pandas
pip install opencv-python
pip install pyyaml
```

### Module Structure

```
src/
├── feature_extraction.py      # Main feature extraction classes
├── feature_visualization.py   # Visualization utilities
└── feature_config.yaml       # Configuration file

examples/
└── feature_extraction_demo.py # Demonstration script

docs/
└── FEATURE_EXTRACTION_README.md
```

## Usage

### Basic Usage

```python
from feature_extraction import FeatureExtractor
import numpy as np

# Initialize extractor
extractor = FeatureExtractor()

# Extract features from image and mask
image = np.random.rand(100, 100) * 255  # Your MRI image
mask = np.random.randint(0, 2, (100, 100))  # Your segmentation mask

features = extractor.extract_all_features(image, mask)
print(f"Extracted {len(features)} features")
```

### Batch Processing

```python
# Process multiple images
images = [image1, image2, image3]  # List of images
masks = [mask1, mask2, mask3]      # Corresponding masks

features_list = extractor.extract_features_batch(images, masks)
```

### Feature Selection

```python
from feature_extraction import FeatureSelector
import numpy as np

# Prepare data
X = np.array([[features.get(name, 0) for name in feature_names] 
              for features in features_list])
y = np.array(labels)  # Your class labels

# Initialize selector
selector = FeatureSelector()

# Univariate selection
X_selected, selected_indices = selector.select_features_univariate(X, y, k=20)

# RFE selection
X_rfe, rfe_indices = selector.select_features_rfe(X, y, n_features=20)

# Importance-based selection
X_imp, imp_indices = selector.select_features_importance(X, y, threshold=0.01)
```

### Visualization

```python
from feature_visualization import FeatureVisualizer

# Initialize visualizer
visualizer = FeatureVisualizer()

# Plot feature distribution
visualizer.plot_feature_distribution(features_dict, 'area', labels)

# Create correlation matrix
visualizer.plot_correlation_matrix(features_dict)

# Feature importance plot
visualizer.plot_feature_importance(feature_names, importance_scores)

# Comprehensive dashboard
visualizer.create_feature_summary_dashboard(features_dict, labels)
```

## Feature Types Details

### 1. Texture Features

#### Gray-Level Co-occurrence Matrix (GLCM)
Captures spatial relationships between pixel intensities.

**Features extracted:**
- Contrast: Measures local intensity variations
- Dissimilarity: Measures how different neighboring pixels are
- Homogeneity: Measures similarity of pixel pairs
- Energy: Measures uniformity of intensity distribution
- Correlation: Measures linear dependencies between pixels
- ASM (Angular Second Moment): Measures texture uniformity

#### Local Binary Patterns (LBP)
Describes local texture patterns around each pixel.

**Features extracted:**
- Uniformity: Measures pattern uniformity
- Entropy: Measures pattern complexity
- Contrast: Measures local intensity variations
- Rotation invariance: Measures rotational stability

#### Edge-based Features
Characterizes edge information using different operators.

**Operators used:**
- Roberts: Emphasizes high-frequency details
- Sobel: Emphasizes edges in horizontal/vertical directions
- Scharr: Improved version of Sobel
- Prewitt: Similar to Sobel with different kernel

### 2. Shape Features

#### Basic Geometric Properties
- **Area**: Number of pixels in the region
- **Perimeter**: Length of the region boundary
- **Circularity**: Measures how circular the shape is
- **Compactness**: Ratio of perimeter² to area
- **Extent**: Ratio of region area to bounding box area
- **Solidity**: Ratio of region area to convex hull area
- **Aspect Ratio**: Ratio of width to height
- **Equivalent Diameter**: Diameter of circle with same area

#### Advanced Shape Descriptors
- **Eccentricity**: Measures elongation of the shape
- **Major/Minor Axis Length**: Lengths of major and minor axes
- **Orientation**: Angle of major axis with horizontal
- **Elongation**: Ratio of major to minor axis length
- **Roundness**: Alternative circularity measure
- **Fractal Dimension**: Measures shape complexity

#### Moment Invariants
- **Hu Moments**: 7 rotation, translation, and scale invariant moments
- **Central Moments**: Moments computed around the centroid
- **Normalized Moments**: Scale-normalized central moments

### 3. Statistical Features

#### First-order Statistics
Computed from intensity histogram:
- **Mean**: Average intensity
- **Standard Deviation**: Intensity variation
- **Variance**: Square of standard deviation
- **Skewness**: Asymmetry of intensity distribution
- **Kurtosis**: Peakedness of intensity distribution
- **Entropy**: Randomness of intensity distribution
- **Energy**: Uniformity of intensity distribution

#### Second-order Statistics
Computed from spatial relationships:
- **Gradient Statistics**: Measures of intensity changes
- **Laplacian Statistics**: Measures of intensity curvature

#### Percentile-based Features
- Various percentiles (5th, 10th, 25th, 50th, 75th, 90th, 95th, 99th)
- Interquartile Range (IQR)
- Range (max - min)

## Feature Selection Methods

### 1. Univariate Statistical Tests
Selects features based on statistical significance.

```python
# Uses F-test for classification
X_selected, indices = selector.select_features_univariate(X, y, k=20)
```

### 2. Recursive Feature Elimination (RFE)
Recursively eliminates features using model-based ranking.

```python
# Uses Random Forest as base estimator
X_selected, indices = selector.select_features_rfe(X, y, n_features=20)
```

### 3. Feature Importance Ranking
Selects features based on importance scores from tree-based models.

```python
# Uses Random Forest feature importance
X_selected, indices = selector.select_features_importance(X, y, threshold=0.01)
```

### 4. Mutual Information
Selects features based on mutual information with target variable.

```python
# Measures non-linear dependencies
X_selected, indices = selector.select_features_mutual_info(X, y, k=20)
```

## Configuration

The module uses a YAML configuration file for customization:

```yaml
# config/feature_config.yaml
texture:
  glcm:
    distances: [1, 2, 3, 4, 5]
    angles: [0, 45, 90, 135]
    levels: 256
    
shape:
  basic_features:
    - area
    - perimeter
    - circularity
    
statistical:
  first_order:
    - mean
    - std
    - variance
```

## Performance Optimization

### Parallel Processing
```python
# Use multiple cores for feature extraction
extractor = FeatureExtractor(n_jobs=-1)
```

### Memory Management
```python
# Process images in batches
batch_size = 100
for i in range(0, len(images), batch_size):
    batch_images = images[i:i+batch_size]
    batch_masks = masks[i:i+batch_size]
    features_batch = extractor.extract_features_batch(batch_images, batch_masks)
```

## Quality Control

### Feature Validation
- **NaN/Inf Detection**: Identifies and handles invalid values
- **Variance Checking**: Removes features with very low variance
- **Stability Testing**: Ensures features are stable across different samples

### Outlier Detection
- **IQR Method**: Identifies outliers using interquartile range
- **Z-score Method**: Identifies outliers using standard deviation
- **Isolation Forest**: Model-based outlier detection

## Examples

### Complete Workflow Example

```python
import numpy as np
from feature_extraction import FeatureExtractor, FeatureSelector
from feature_visualization import FeatureVisualizer

# 1. Data preparation
images = load_mri_images()  # Your image loading function
masks = load_segmentation_masks()  # Your mask loading function
labels = load_labels()  # Your label loading function

# 2. Feature extraction
extractor = FeatureExtractor()
features_list = extractor.extract_features_batch(images, masks)

# 3. Convert to matrix format
feature_names = list(features_list[0].keys())
X = np.array([[features.get(name, 0) for name in feature_names] 
              for features in features_list])
X = np.nan_to_num(X)  # Handle NaN values

# 4. Feature selection
selector = FeatureSelector()
X_selected, selected_indices = selector.select_features_univariate(X, labels, k=20)

# 5. Visualization
visualizer = FeatureVisualizer()
features_dict = {name: [features.get(name, 0) for features in features_list] 
                for name in feature_names}
visualizer.create_feature_summary_dashboard(features_dict, labels)

# 6. Classification
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report

X_train, X_test, y_train, y_test = train_test_split(X_selected, labels, test_size=0.2)
clf = RandomForestClassifier(n_estimators=100, random_state=42)
clf.fit(X_train, y_train)

y_pred = clf.predict(X_test)
print(classification_report(y_test, y_pred))
```

### Advanced Feature Analysis

```python
# Analyze feature importance
feature_ranking = selector.get_feature_ranking(feature_names)
print("Top 10 most important features:")
for name, score in feature_ranking[:10]:
    print(f"{name}: {score:.4f}")

# Compare feature selection methods
methods = {
    'Univariate': selector.select_features_univariate(X, labels, k=20)[1],
    'RFE': selector.select_features_rfe(X, labels, n_features=20)[1],
    'Importance': selector.select_features_importance(X, labels, threshold=0.01)[1]
}

visualizer.plot_feature_selection_comparison(X, methods, feature_names)
```

## Best Practices

### 1. Data Preprocessing
- Normalize image intensities to [0, 255] range
- Ensure masks are binary (0s and 1s)
- Handle missing or corrupt data

### 2. Feature Engineering
- Remove highly correlated features (correlation > 0.95)
- Scale features appropriately before selection
- Consider domain knowledge for feature interpretation

### 3. Validation
- Use cross-validation for robust performance estimates
- Test feature stability across different datasets
- Validate biological relevance of selected features

### 4. Performance Monitoring
- Track feature extraction time
- Monitor memory usage for large datasets
- Profile code for optimization opportunities

## Troubleshooting

### Common Issues

**1. Memory Error**
```python
# Solution: Process in smaller batches
batch_size = 50  # Reduce batch size
```

**2. NaN Values in Features**
```python
# Solution: Handle NaN values explicitly
X = np.nan_to_num(X, nan=0.0, posinf=0.0, neginf=0.0)
```

**3. Empty Segmentation Masks**
```python
# Solution: Check mask validity
if np.sum(mask) == 0:
    print("Warning: Empty mask detected")
    continue
```

**4. Feature Selection Fails**
```python
# Solution: Check data quality
if np.var(X, axis=0).min() < 1e-6:
    print("Warning: Low variance features detected")
    # Remove low variance features
```

## References

1. Haralick, R.M., et al. (1973). Textural features for image classification. IEEE Transactions on Systems, Man, and Cybernetics.

2. Ojala, T., et al. (2002). Multiresolution gray-scale and rotation invariant texture classification with local binary patterns. IEEE Transactions on Pattern Analysis and Machine Intelligence.

3. Hu, M.K. (1962). Visual pattern recognition by moment invariants. IRE Transactions on Information Theory.

4. Guyon, I., & Elisseeff, A. (2003). An introduction to variable and feature selection. Journal of Machine Learning Research.

## License

This module is part of the Brain Tumor Detection system developed by Dr. Mohammed Yagoub Esmail. All rights reserved.

## Contact

For questions or support, please contact:
- **Email**: <EMAIL>
- **Phone**: +************ | +************

---

*This documentation provides comprehensive guidance for using the feature extraction module. For additional examples and use cases, refer to the demonstration scripts in the examples directory.*